import http from './http'
import type { ApiResponse } from '@/types/api'

/**
 * 模拟交易API接口
 */

export interface SimulatedAccountData {
  initial_cash: number
  account_name?: string
  description?: string
}

export interface AccountSettings {
  risk_level: number
  max_position_ratio: number
  stop_loss_ratio: number
  take_profit_ratio: number
}

export interface OrderData {
  symbol: string
  side: 'BUY' | 'SELL'
  order_type: 'MARKET' | 'LIMIT'
  quantity: number
  price?: number
}

// 创建模拟账户
export function createSimulatedAccount(data: SimulatedAccountData): Promise<ApiResponse> {
  return http({
    url: '/api/v1/simulated/accounts/create',
    method: 'post',
    data
  })
}

// 获取模拟账户列表
export function getSimulatedAccounts(): Promise<ApiResponse> {
  return http({
    url: '/api/v1/simulated/accounts',
    method: 'get'
  })
}

// 切换账户
export function switchAccountType(accountType: 'REAL' | 'SIMULATED'): Promise<ApiResponse> {
  return http({
    url: `/api/v1/simulated/accounts/switch/${accountType}`,
    method: 'post'
  })
}

// 重置模拟账户
export function resetSimulatedAccount(accountId: string): Promise<ApiResponse> {
  return http({
    url: `/api/v1/simulated/accounts/${accountId}/reset`,
    method: 'post'
  })
}

// 创建模拟订单
export function createSimulatedOrder(data: OrderData): Promise<ApiResponse> {
  return http({
    url: '/api/v1/simulated/orders',
    method: 'post',
    data
  })
}

// 获取当前账户
export function getCurrentAccount(): Promise<ApiResponse> {
  return http({
    url: '/api/v1/simulated/current-account',
    method: 'get'
  })
}

// 获取账户业绩
export function getAccountPerformance(accountId: string, period: string = '1M'): Promise<ApiResponse> {
  return http({
    url: `/api/v1/simulated/performance/${accountId}`,
    method: 'get',
    params: { period }
  })
}

// 获取模拟交易指南
export function getSimulatedGuide(): Promise<ApiResponse> {
  return http({
    url: '/api/v1/simulated/guide',
    method: 'get'
  })
}

// 更新账户设置
export function updateAccountSettings(accountId: string, settings: AccountSettings): Promise<ApiResponse> {
  return http({
    url: `/api/v1/simulated/accounts/${accountId}/settings`,
    method: 'put',
    data: settings
  })
}
