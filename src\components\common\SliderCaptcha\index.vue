<template>
  <div class="slide-verify" :style="{ width: w + 'px' }">
    <!-- 验证码画布容器 -->
    <div class="slide-verify-block">
      <div 
        class="slide-verify-refresh-icon" 
        @click="refresh"
        :class="{ 'is-loading': loading }"
      >
        <el-icon><Refresh /></el-icon>
      </div>
      
      <!-- 主画布 -->
      <canvas
        ref="canvasRef"
        :width="w"
        :height="h"
        class="slide-verify-canvas"
      ></canvas>
      
      <!-- 拼图块画布 -->
      <canvas
        ref="blockRef"
        :width="w"
        :height="h"
        class="slide-verify-block-canvas"
        :style="{ left: blockLeft + 'px' }"
      ></canvas>
    </div>
    
    <!-- 滑动轨道 -->
    <div 
      class="slide-verify-slider"
      :class="{ 
        'slide-verify-slider-success': isSuccess,
        'slide-verify-slider-fail': isFail 
      }"
    >
      <!-- 进度背景 -->
      <div 
        class="slide-verify-slider-mask"
        :style="{ width: sliderLeft + 'px' }"
      >
        <div 
          class="slide-verify-slider-mask-item"
          :style="{ left: sliderLeft + 'px' }"
        ></div>
      </div>
      
      <!-- 滑块 -->
      <div
        ref="sliderRef"
        class="slide-verify-slider-btn"
        :class="{ 
          'slide-verify-slider-btn-success': isSuccess,
          'slide-verify-slider-btn-fail': isFail 
        }"
        :style="{ left: sliderLeft + 'px' }"
        @mousedown="sliderDown"
        @touchstart="sliderDown"
      >
        <el-icon v-if="isSuccess" class="success-icon"><Check /></el-icon>
        <el-icon v-else-if="isFail" class="fail-icon"><Close /></el-icon>
        <el-icon v-else><ArrowRight /></el-icon>
      </div>
      
      <!-- 提示文字 -->
      <span class="slide-verify-slider-text">
        {{ sliderText }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import { Refresh, Check, Close, ArrowRight } from '@element-plus/icons-vue'

interface Props {
  l?: number        // 拼图块边长
  r?: number        // 拼图块的圆角半径
  w?: number        // canvas宽度
  h?: number        // canvas高度
  sliderText?: string // 滑块文字
  imgs?: string[]   // 背景图片数组
  accuracy?: number // 验证精度（允许的误差范围）
  show?: boolean    // 是否显示刷新按钮
}

const props = withDefaults(defineProps<Props>(), {
  l: 42,
  r: 10,
  w: 310,
  h: 155,
  sliderText: '向右拖动滑块填充拼图',
  imgs: () => [
    'https://picsum.photos/400/200?random=1',
    'https://picsum.photos/400/200?random=2',
    'https://picsum.photos/400/200?random=3',
    'https://picsum.photos/400/200?random=4',
    'https://picsum.photos/400/200?random=5'
  ],
  accuracy: 5,
  show: true
})

const emit = defineEmits<{
  success: []
  fail: []
  refresh: []
  again: []
  fulfilled: []
}>()

// 模板引用
const canvasRef = ref<HTMLCanvasElement>()
const blockRef = ref<HTMLCanvasElement>()
const sliderRef = ref<HTMLElement>()

// 状态
const loading = ref(false)
const isSuccess = ref(false)
const isFail = ref(false)
const sliderLeft = ref(0)
const blockLeft = ref(0)

// 验证数据
const correctX = ref(0)
const blockCtx = ref<CanvasRenderingContext2D>()
const canvasCtx = ref<CanvasRenderingContext2D>()

// 拖拽状态
const originX = ref(0)
const originY = ref(0)
const isMouseDown = ref(false)
const trail = ref<number[]>([])
const startTime = ref(0)
const endTime = ref(0)

// 初始化
const init = () => {
  initDom()
  initImg()
}

// 初始化DOM
const initDom = () => {
  if (!canvasRef.value || !blockRef.value) return
  
  blockCtx.value = blockRef.value.getContext('2d')!
  canvasCtx.value = canvasRef.value.getContext('2d')!
}

// 初始化图片
const initImg = () => {
  const img = new Image()
  img.crossOrigin = 'Anonymous'
  
  img.onload = () => {
    drawImg(img)
    drawBlock()
  }
  
  img.onerror = () => {
    // 如果图片加载失败，生成纯色背景
    drawColorBackground()
    drawBlock()
  }
  
  // 随机选择一张图片
  const randomIndex = Math.floor(Math.random() * props.imgs.length)
  img.src = props.imgs[randomIndex]
}

// 绘制背景图片
const drawImg = (img: HTMLImageElement) => {
  if (!canvasCtx.value) return
  
  canvasCtx.value.drawImage(img, 0, 0, props.w, props.h)
}

// 绘制纯色背景（备用方案）
const drawColorBackground = () => {
  if (!canvasCtx.value) return
  
  const gradient = canvasCtx.value.createLinearGradient(0, 0, props.w, props.h)
  const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd']
  const color1 = colors[Math.floor(Math.random() * colors.length)]
  const color2 = colors[Math.floor(Math.random() * colors.length)]
  
  gradient.addColorStop(0, color1)
  gradient.addColorStop(1, color2)
  
  canvasCtx.value.fillStyle = gradient
  canvasCtx.value.fillRect(0, 0, props.w, props.h)
  
  // 添加一些随机图案
  for (let i = 0; i < 20; i++) {
    const x = Math.random() * props.w
    const y = Math.random() * props.h
    const radius = Math.random() * 30 + 10
    
    canvasCtx.value.beginPath()
    canvasCtx.value.arc(x, y, radius, 0, 2 * Math.PI)
    canvasCtx.value.fillStyle = `hsla(${Math.random() * 360}, 70%, 80%, 0.3)`
    canvasCtx.value.fill()
  }
}

// 绘制拼图块
const drawBlock = () => {
  if (!canvasCtx.value || !blockCtx.value) return
  
  correctX.value = getRandomNumberByRange(props.l + 10, props.w - (props.l + 10))
  const y = getRandomNumberByRange(10 + props.r * 2, props.h - (props.l + 10))
  
  draw(canvasCtx.value, correctX.value, y, 'fill')
  draw(blockCtx.value, correctX.value, y, 'clip')
}

// 绘制拼图块路径
const draw = (ctx: CanvasRenderingContext2D, x: number, y: number, operation: string) => {
  const l = props.l
  const r = props.r
  
  ctx.beginPath()
  ctx.moveTo(x, y)
  ctx.arc(x + l / 2, y - r + 2, r, 0.72 * Math.PI, 2.26 * Math.PI)
  ctx.lineTo(x + l, y)
  ctx.arc(x + l + r - 2, y + l / 2, r, 1.21 * Math.PI, 2.78 * Math.PI)
  ctx.lineTo(x + l, y + l)
  ctx.lineTo(x, y + l)
  ctx.arc(x + r - 2, y + l / 2, r + 0.4, 2.76 * Math.PI, 1.24 * Math.PI, true)
  ctx.lineTo(x, y)
  
  ctx.lineWidth = 2
  ctx.fillStyle = 'rgba(255, 255, 255, 0.7)'
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)'
  ctx.stroke()
  
  if (operation === 'fill') {
    ctx.globalCompositeOperation = 'destination-over'
    ctx.fill()
  } else if (operation === 'clip') {
    ctx.clip()
    ctx.drawImage(canvasRef.value!, 0, 0)
  }
}

// 获取随机数
const getRandomNumberByRange = (start: number, end: number): number => {
  return Math.round(Math.random() * (end - start) + start)
}

// 滑块按下事件
const sliderDown = (event: MouseEvent | TouchEvent) => {
  if (isSuccess.value || loading.value) return
  
  isMouseDown.value = true
  startTime.value = Date.now()
  
  const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX
  const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY
  
  originX.value = clientX
  originY.value = clientY
  
  document.addEventListener('mousemove', sliderMove)
  document.addEventListener('mouseup', sliderUp)
  document.addEventListener('touchmove', sliderMove)
  document.addEventListener('touchend', sliderUp)
}

// 滑块移动事件
const sliderMove = (event: MouseEvent | TouchEvent) => {
  if (!isMouseDown.value) return
  
  event.preventDefault()
  
  const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX
  const moveX = clientX - originX.value
  
  if (moveX < 0 || moveX + 38 > props.w) return
  
  sliderLeft.value = moveX
  blockLeft.value = (props.w - 40) / (props.w - 40) * moveX
  
  // 记录轨迹
  trail.value.push(Math.round(moveX))
}

// 滑块释放事件
const sliderUp = () => {
  if (!isMouseDown.value) return
  
  isMouseDown.value = false
  endTime.value = Date.now()
  
  // 移除事件监听
  document.removeEventListener('mousemove', sliderMove)
  document.removeEventListener('mouseup', sliderUp)
  document.removeEventListener('touchmove', sliderMove)
  document.removeEventListener('touchend', sliderUp)
  
  // 验证结果
  verify()
}

// 验证滑块位置
const verify = () => {
  const arr = trail.value // 拖拽轨迹
  const average = arr.reduce((sum, item) => sum + item, 0) / arr.length
  const deviations = arr.map(x => x - average)
  const stddev = Math.sqrt(deviations.map(x => x * x).reduce((sum, item) => sum + item, 0) / arr.length)
  const left = parseInt(String(blockLeft.value))
  
  if (stddev !== 0) { // 轨迹有变化，非机器人
    if (Math.abs(left - correctX.value) < props.accuracy) {
      // 验证成功
      isSuccess.value = true
      emit('success')
      return
    }
  }
  
  // 验证失败
  isFail.value = true
  setTimeout(() => {
    emit('fail')
    setTimeout(() => {
      reset()
    }, 1000)
  }, 1000)
}

// 重置
const reset = () => {
  isSuccess.value = false
  isFail.value = false
  sliderLeft.value = 0
  blockLeft.value = 0
  trail.value = []
  
  if (canvasCtx.value && blockCtx.value) {
    canvasCtx.value.clearRect(0, 0, props.w, props.h)
    blockCtx.value.clearRect(0, 0, props.w, props.h)
    canvasCtx.value.globalCompositeOperation = 'source-over'
  }
}

// 刷新验证码
const refresh = () => {
  if (loading.value) return
  
  loading.value = true
  reset()
  
  setTimeout(() => {
    init()
    loading.value = false
    emit('refresh')
  }, 300)
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    init()
  })
})

// 暴露方法
defineExpose({
  refresh,
  reset
})
</script>

<style scoped>
.slide-verify {
  position: relative;
  user-select: none;
}

.slide-verify-block {
  position: relative;
  background: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
}

.slide-verify-refresh-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 3;
  transition: all 0.3s ease;
  color: #606266;
}

.slide-verify-refresh-icon:hover {
  background: rgba(255, 255, 255, 1);
  color: var(--el-color-primary);
  transform: scale(1.1);
}

.slide-verify-refresh-icon.is-loading {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.slide-verify-canvas {
  display: block;
  border: none;
}

.slide-verify-block-canvas {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  transition: left 0.1s ease-out;
}

.slide-verify-slider {
  position: relative;
  text-align: center;
  width: 100%;
  height: 40px;
  line-height: 40px;
  margin-top: 15px;
  background: #f7f9fa;
  color: #45494c;
  border: 1px solid #e4e7ed;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.slide-verify-slider-success {
  background: #f0f9ff !important;
  border-color: #67c23a !important;
}

.slide-verify-slider-fail {
  background: #fef0f0 !important;
  border-color: #f56c6c !important;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.slide-verify-slider-mask {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  border: 0 solid #1991fa;
  background: #d1e9fe;
  border-radius: 20px;
  transition: width 0.3s ease;
}

.slide-verify-slider-mask-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 38px;
  height: 100%;
  background: #fff;
  border: 1px solid #1991fa;
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  margin-left: -19px;
  margin-top: -1px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.slide-verify-slider-btn {
  position: absolute;
  top: -1px;
  left: 0;
  width: 38px;
  height: 38px;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  z-index: 2;
}

.slide-verify-slider-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.slide-verify-slider-btn-success {
  background: #67c23a !important;
  border-color: #67c23a !important;
  color: white !important;
}

.slide-verify-slider-btn-fail {
  background: #f56c6c !important;
  border-color: #f56c6c !important;
  color: white !important;
}

.slide-verify-slider-text {
  position: relative;
  z-index: 1;
  font-size: 14px;
  color: #9aa1a8;
  user-select: none;
}

.success-icon, .fail-icon {
  font-size: 16px;
}

.success-icon {
  color: white;
  animation: success-bounce 0.6s ease-in-out;
}

.fail-icon {
  color: white;
  animation: error-pulse 0.6s ease-in-out;
}

@keyframes success-bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

@keyframes error-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .slide-verify-slider {
    height: 36px;
    line-height: 36px;
  }
  
  .slide-verify-slider-btn {
    width: 34px;
    height: 34px;
  }
  
  .slide-verify-slider-mask-item {
    width: 34px;
    margin-left: -17px;
  }
  
  .slide-verify-refresh-icon {
    width: 28px;
    height: 28px;
  }
}
</style>