{"timestamp": "2025-08-07T08:12:35.987Z", "usedDependencies": ["@element-plus/icons-vue", "@sentry/vue", "@vueuse/core", "axios", "big.js", "date-fns", "dayjs", "decimal.js", "dompurify", "echarts", "element-plus", "express", "jwt-decode", "lodash-es", "mitt", "nprogress", "numeral", "pinia", "pinia-plugin-persistedstate", "socket.io-client", "vue", "vue-echarts", "vue-i18n", "vue-router", "@commitlint/cli", "@commitlint/config-conventional", "@playwright/test", "@tailwindcss/forms", "@tailwindcss/typography", "@tsconfig/node22", "@types/big.js", "@types/dompurify", "@types/lodash-es", "@types/node", "@types/nprogress", "@types/numeral", "@vitejs/plugin-vue", "@vitest/ui", "@vue/eslint-config-prettier", "@vue/eslint-config-typescript", "@vue/test-utils", "@vue/tsconfig", "autoprefixer", "chalk", "commitizen", "cz-conventional-changelog", "eslint", "eslint-define-config", "eslint-plugin-vue", "husky", "jiti", "jsdom", "lint-staged", "npm-run-all2", "postcss", "prettier", "<PERSON><PERSON><PERSON>", "rollup-plugin-visualizer", "sass", "standard-version", "stylelint", "stylelint-config-standard", "stylelint-config-standard-scss", "stylelint-config-standard-vue", "stylelint-order", "tailwindcss", "terser", "typescript", "unplugin-auto-import", "unplugin-vue-components", "vite", "vite-plugin-compression2", "vite-plugin-eslint", "vite-plugin-html", "vite-plugin-pwa", "vite-plugin-vue-devtools", "vitest", "vue-tsc"], "unusedDependencies": [], "summary": {"total": 78, "used": 78, "unused": 0}}