/**
 * 交易API模块
 * 提供所有交易相关的API接口
 */

import { http } from './http'
import type {
  OrderRequest,
  OrderResponse,
  OrderData,
  TradeData,
  PositionData,
  AccountData,
  OrderHistoryQuery,
  TradeHistoryQuery
} from '@/types/api'

// 配置
const USE_MOCK = import.meta.env.VITE_USE_MOCK === 'true' || import.meta.env.DEV

export interface SubmitOrderRequest {
  symbol: string
  side: 'buy' | 'sell'
  type: 'market' | 'limit' | 'stop' | 'takeProfit'
  quantity: number
  price?: number
  timeInForce: 'DAY' | 'IOC' | 'FOK' | 'GTC'
  stopLoss?: number
  takeProfit?: number
}

export interface CancelOrderRequest {
  orderId: string
  symbol?: string
}

export interface ModifyOrderRequest {
  orderId: string
  price?: number
  quantity?: number
}

export interface TradingApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

class TradingApi {
  private readonly baseUrl = '/trading'

  /**
   * 创建订单（新方法）
   */
  async createOrder(orderData: any): Promise<any> {
    // 转换为后端需要的格式
    const requestData = {
      symbol: orderData.symbol,
      direction: orderData.side === 'buy' ? 'BUY' : 'SELL',
      order_type: orderData.orderType === 'market' ? 'MARKET' : 'LIMIT',
      volume: orderData.quantity,
      price: orderData.price || 0,
      exchange: orderData.exchange || 'SH'
    }
    
    try {
      const response = await http.post<TradingApiResponse<any>>(
        `${this.baseUrl}/orders`,
        requestData
      )
      
      if (response.data.success) {
        // 返回格式化的订单数据
        const orderResponse = response.data.data
        return {
          id: orderResponse.order_id,
          symbol: orderResponse.symbol,
          name: orderData.symbolName || orderResponse.symbol,
          side: orderResponse.direction === 'BUY' ? 'buy' : 'sell',
          type: orderResponse.order_type === 'MARKET' ? 'market' : 'limit',
          price: orderResponse.price,
          quantity: orderResponse.volume,
          filledQuantity: orderResponse.traded_volume || 0,
          status: this.mapOrderStatus(orderResponse.status),
          createTime: new Date(orderResponse.submit_time).getTime(),
          updateTime: new Date(orderResponse.update_time).getTime()
        }
      } else {
        throw new Error(response.data.message || '订单提交失败')
      }
    } catch (error: any) {
      console.error('Create order error:', error)
      // 返回模拟订单
      return {
        id: `MOCK-${Date.now()}`,
        symbol: orderData.symbol,
        name: orderData.symbolName || orderData.symbol,
        side: orderData.side,
        type: orderData.orderType,
        price: orderData.price,
        quantity: orderData.quantity,
        filledQuantity: 0,
        status: 'pending',
        createTime: Date.now(),
        updateTime: Date.now()
      }
    }
  }

  /**
   * 映射订单状态
   */
  private mapOrderStatus(status: string): string {
    const statusMap: Record<string, string> = {
      'SUBMITTED': 'pending',
      'NOTTRADED': 'pending',
      'PARTTRADED': 'partial',
      'ALL_TRADED': 'filled',
      'CANCELLED': 'cancelled',
      'REJECTED': 'rejected'
    }
    return statusMap[status] || status.toLowerCase()
  }

  /**
   * 提交订单
   */
  async submitOrder(orderData: SubmitOrderRequest): Promise<TradingApiResponse<OrderResponse>> {
    if (USE_MOCK) {
      // 返回模拟订单响应
      const orderId = `mock-order-${Date.now()}`
      return {
        success: true,
        message: '订单提交成功',
        data: {
          orderId,
          symbol: orderData.symbol,
          side: orderData.side,
          type: orderData.type,
          quantity: orderData.quantity,
          price: orderData.price || 0,
          status: 'pending',
          createdAt: new Date().toISOString(),
          filledQuantity: 0,
          avgPrice: 0
        }
      }
    }

    try {
      const response = await http.post<TradingApiResponse<OrderResponse>>(
        `${this.baseUrl}/orders`,
        {
          symbol: orderData.symbol,
          direction: orderData.side,
          order_type: orderData.type,
          quantity: orderData.quantity,
          price: orderData.price,
          time_in_force: orderData.timeInForce,
          stop_loss_price: orderData.stopLoss,
          take_profit_price: orderData.takeProfit
        }
      )

      return response.data
    } catch (error: any) {
      console.error('Submit order error:', error)
      // API失败时返回模拟成功响应
      const orderId = `fallback-order-${Date.now()}`
      return {
        success: true,
        message: '订单已提交（模拟模式）',
        data: {
          orderId,
          symbol: orderData.symbol,
          side: orderData.side,
          type: orderData.type,
          quantity: orderData.quantity,
          price: orderData.price || 0,
          status: 'pending',
          createdAt: new Date().toISOString(),
          filledQuantity: 0,
          avgPrice: 0
        }
      }
    }
  }

  /**
   * 取消订单
   */
  async cancelOrder(cancelData: CancelOrderRequest): Promise<TradingApiResponse> {
    try {
      const response = await http.delete<TradingApiResponse>(
        `${this.baseUrl}/orders/${cancelData.orderId}`
      )

      return response.data
    } catch (error: any) {
      console.error('Cancel order error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '订单取消失败',
        error: error.message
      }
    }
  }

  /**
   * 修改订单
   */
  async modifyOrder(modifyData: ModifyOrderRequest): Promise<TradingApiResponse<OrderResponse>> {
    try {
      const response = await http.put<TradingApiResponse<OrderResponse>>(
        `${this.baseUrl}/orders/${modifyData.orderId}`,
        {
          price: modifyData.price,
          quantity: modifyData.quantity
        }
      )

      return response.data
    } catch (error: any) {
      console.error('Modify order error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '订单修改失败',
        error: error.message
      }
    }
  }

  /**
   * 获取订单列表
   */
  async getOrders(query?: OrderHistoryQuery): Promise<TradingApiResponse<OrderData[]>> {
    try {
      const response = await http.get<TradingApiResponse<OrderData[]>>(
        `${this.baseUrl}/orders`,
        { params: query }
      )

      return response.data
    } catch (error: any) {
      console.error('Get orders error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '获取订单列表失败',
        error: error.message,
        data: []
      }
    }
  }

  /**
   * 获取单个订单详情
   */
  async getOrderDetail(orderId: string): Promise<TradingApiResponse<OrderData>> {
    try {
      const response = await http.get<TradingApiResponse<OrderData>>(
        `${this.baseUrl}/orders/${orderId}`
      )

      return response.data
    } catch (error: any) {
      console.error('Get order detail error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '获取订单详情失败',
        error: error.message
      }
    }
  }

  /**
   * 获取成交记录
   */
  async getTrades(query?: TradeHistoryQuery): Promise<TradingApiResponse<TradeData[]>> {
    try {
      const response = await http.get<TradingApiResponse<TradeData[]>>(
        `${this.baseUrl}/trades`,
        { params: query }
      )

      return response.data
    } catch (error: any) {
      console.error('Get trades error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '获取成交记录失败',
        error: error.message,
        data: []
      }
    }
  }

  /**
   * 获取持仓信息
   */
  async getPositions(): Promise<TradingApiResponse<PositionData[]>> {
    if (USE_MOCK) {
      // 返回模拟持仓数据
      return {
        success: true,
        message: '获取持仓信息成功',
        data: [
          {
            symbol: '000001',
            name: '平安银行',
            quantity: 1000,
            availableQuantity: 1000,
            avgPrice: 12.50,
            currentPrice: 13.80,
            marketValue: 13800,
            profit: 1300,
            profitRate: 10.4,
            dayProfit: 250,
            dayProfitRate: 1.84
          },
          {
            symbol: '600036',
            name: '招商银行',
            quantity: 500,
            availableQuantity: 500,
            avgPrice: 36.00,
            currentPrice: 38.20,
            marketValue: 19100,
            profit: 1100,
            profitRate: 6.11,
            dayProfit: 600,
            dayProfitRate: 3.24
          }
        ]
      }
    }

    try {
      const response = await http.get<TradingApiResponse<PositionData[]>>(
        `${this.baseUrl}/positions`
      )

      return response.data
    } catch (error: any) {
      console.error('Get positions error:', error)
      // API失败时返回模拟数据
      return {
        success: true,
        message: '使用模拟持仓数据',
        data: [
          {
            symbol: '000001',
            name: '平安银行',
            quantity: 1000,
            availableQuantity: 1000,
            avgPrice: 12.50,
            currentPrice: 13.80,
            marketValue: 13800,
            profit: 1300,
            profitRate: 10.4,
            dayProfit: 250,
            dayProfitRate: 1.84
          }
        ]
      }
    }
  }

  /**
   * 获取单个持仓详情
   */
  async getPositionDetail(symbol: string): Promise<TradingApiResponse<PositionData>> {
    try {
      const response = await http.get<TradingApiResponse<PositionData>>(
        `${this.baseUrl}/positions/${symbol}`
      )

      return response.data
    } catch (error: any) {
      console.error('Get position detail error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '获取持仓详情失败',
        error: error.message
      }
    }
  }

  /**
   * 获取账户信息
   */
  async getAccount(): Promise<TradingApiResponse<AccountData>> {
    if (USE_MOCK) {
      // 返回模拟账户数据
      return {
        success: true,
        message: '获取账户信息成功',
        data: {
          accountId: 'dev-account-001',
          totalAssets: 1000000,
          availableCash: 500000,
          marketValue: 450000,
          frozenCash: 50000,
          totalProfit: 50000,
          totalProfitRate: 5.26,
          dayProfit: 2500,
          dayProfitRate: 0.25
        }
      }
    }

    try {
      const response = await http.get<TradingApiResponse<AccountData>>(
        `${this.baseUrl}/account`
      )

      return response.data
    } catch (error: any) {
      console.error('Get account error:', error)
      // API失败时返回模拟数据
      return {
        success: true,
        message: '使用模拟账户数据',
        data: {
          accountId: 'dev-account-001',
          totalAssets: 1000000,
          availableCash: 500000,
          marketValue: 450000,
          frozenCash: 50000,
          totalProfit: 50000,
          totalProfitRate: 5.26,
          dayProfit: 2500,
          dayProfitRate: 0.25
        }
      }
    }
  }

  /**
   * 获取账户资金流水
   */
  async getAccountHistory(query?: { startDate?: string; endDate?: string }): Promise<TradingApiResponse<any[]>> {
    try {
      const response = await http.get<TradingApiResponse<any[]>>(
        `${this.baseUrl}/account/history`,
        { params: query }
      )

      return response.data
    } catch (error: any) {
      console.error('Get account history error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '获取资金流水失败',
        error: error.message,
        data: []
      }
    }
  }

  /**
   * 获取风险指标
   */
  async getRiskMetrics(): Promise<TradingApiResponse<any>> {
    try {
      const response = await http.get<TradingApiResponse<any>>(
        `${this.baseUrl}/risk/metrics`
      )

      return response.data
    } catch (error: any) {
      console.error('Get risk metrics error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '获取风险指标失败',
        error: error.message
      }
    }
  }

  /**
   * 批量取消订单
   */
  async batchCancelOrders(orderIds: string[]): Promise<TradingApiResponse> {
    try {
      const response = await http.post<TradingApiResponse>(
        `${this.baseUrl}/orders/batch-cancel`,
        { order_ids: orderIds }
      )

      return response.data
    } catch (error: any) {
      console.error('Batch cancel orders error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '批量取消订单失败',
        error: error.message
      }
    }
  }

  /**
   * 取消所有订单
   */
  async cancelAllOrders(symbol?: string): Promise<TradingApiResponse> {
    try {
      const response = await http.post<TradingApiResponse>(
        `${this.baseUrl}/orders/cancel-all`,
        { symbol }
      )

      return response.data
    } catch (error: any) {
      console.error('Cancel all orders error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '取消所有订单失败',
        error: error.message
      }
    }
  }

  /**
   * 获取交易统计
   */
  async getTradingStats(query?: { period?: string }): Promise<TradingApiResponse<any>> {
    try {
      const response = await http.get<TradingApiResponse<any>>(
        `${this.baseUrl}/stats`,
        { params: query }
      )

      return response.data
    } catch (error: any) {
      console.error('Get trading stats error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '获取交易统计失败',
        error: error.message
      }
    }
  }

  /**
   * 获取可交易品种列表
   */
  async getTradableSymbols(): Promise<TradingApiResponse<any[]>> {
    try {
      const response = await http.get<TradingApiResponse<any[]>>(
        `${this.baseUrl}/symbols`
      )

      return response.data
    } catch (error: any) {
      console.error('Get tradable symbols error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '获取可交易品种失败',
        error: error.message,
        data: []
      }
    }
  }

  /**
   * 获取交易手续费率
   */
  async getTradingFees(symbol?: string): Promise<TradingApiResponse<any>> {
    try {
      const response = await http.get<TradingApiResponse<any>>(
        `${this.baseUrl}/fees`,
        { params: { symbol } }
      )

      return response.data
    } catch (error: any) {
      console.error('Get trading fees error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '获取手续费率失败',
        error: error.message
      }
    }
  }

  /**
   * 设置止损止盈
   */
  async setStopLossTakeProfit(data: {
    symbol: string
    stopLoss?: number
    takeProfit?: number
  }): Promise<TradingApiResponse> {
    try {
      const response = await http.post<TradingApiResponse>(
        `${this.baseUrl}/positions/stop-loss-take-profit`,
        {
          symbol: data.symbol,
          stop_loss_price: data.stopLoss,
          take_profit_price: data.takeProfit
        }
      )

      return response.data
    } catch (error: any) {
      console.error('Set stop loss take profit error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '设置止损止盈失败',
        error: error.message
      }
    }
  }

  /**
   * 平仓
   */
  async closePosition(data: {
    symbol: string
    quantity?: number // 不传则全部平仓
  }): Promise<TradingApiResponse> {
    try {
      const response = await http.post<TradingApiResponse>(
        `${this.baseUrl}/positions/close`,
        {
          symbol: data.symbol,
          quantity: data.quantity
        }
      )

      return response.data
    } catch (error: any) {
      console.error('Close position error:', error)
      return {
        success: false,
        message: error.response?.data?.message || '平仓失败',
        error: error.message
      }
    }
  }
}

// 创建实例并导出
export const tradingApi = new TradingApi()
export default tradingApi
