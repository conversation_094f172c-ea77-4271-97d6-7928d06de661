<template>
  <div class="appearance-settings">
    <div class="settings-section">
      <h3 class="section-title">
        <el-icon><Brush /></el-icon>
        外观设置
      </h3>
      <p class="section-description">自定义界面外观和主题</p>

      <el-form :model="form" label-width="120px">
        <!-- 主题设置 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>主题设置</span>
          </template>
          
          <el-form-item label="主题模式">
            <el-radio-group v-model="form.theme">
              <el-radio label="light">浅色主题</el-radio>
              <el-radio label="dark">深色主题</el-radio>
              <el-radio label="auto">跟随系统</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="主色调">
            <div class="color-picker-group">
              <div 
                v-for="color in themeColors" 
                :key="color.value"
                class="color-option"
                :class="{ active: form.primaryColor === color.value }"
                @click="form.primaryColor = color.value"
              >
                <div class="color-circle" :style="{ backgroundColor: color.value }"></div>
                <span class="color-name">{{ color.name }}</span>
              </div>
            </div>
          </el-form-item>
        </el-card>

        <!-- 布局设置 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>布局设置</span>
          </template>
          
          <el-form-item label="侧边栏">
            <el-switch v-model="form.sidebarCollapsed" />
            <span class="switch-label">默认收起侧边栏</span>
          </el-form-item>

          <el-form-item label="面包屑">
            <el-switch v-model="form.showBreadcrumb" />
            <span class="switch-label">显示面包屑导航</span>
          </el-form-item>

          <el-form-item label="页面标签">
            <el-switch v-model="form.showTabs" />
            <span class="switch-label">显示页面标签栏</span>
          </el-form-item>
        </el-card>

        <!-- 字体设置 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>字体设置</span>
          </template>
          
          <el-form-item label="字体大小">
            <el-radio-group v-model="form.fontSize">
              <el-radio label="small">小</el-radio>
              <el-radio label="medium">中</el-radio>
              <el-radio label="large">大</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="字体族">
            <el-select v-model="form.fontFamily" placeholder="选择字体">
              <el-option label="系统默认" value="system" />
              <el-option label="微软雅黑" value="Microsoft YaHei" />
              <el-option label="苹方" value="PingFang SC" />
            </el-select>
          </el-form-item>
        </el-card>

        <el-form-item>
          <el-button type="primary" @click="saveSettings" :loading="saving">
            保存设置
          </el-button>
          <el-button @click="resetSettings">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Brush } from '@element-plus/icons-vue'

const saving = ref(false)

const form = reactive({
  theme: 'light',
  primaryColor: '#409eff',
  sidebarCollapsed: false,
  showBreadcrumb: true,
  showTabs: true,
  fontSize: 'medium',
  fontFamily: 'system'
})

const themeColors = [
  { name: '默认蓝', value: '#409eff' },
  { name: '成功绿', value: '#67c23a' },
  { name: '警告橙', value: '#e6a23c' },
  { name: '危险红', value: '#f56c6c' },
  { name: '信息灰', value: '#909399' },
  { name: '紫色', value: '#722ed1' }
]

const saveSettings = async () => {
  saving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('外观设置保存成功')
  } finally {
    saving.value = false
  }
}

const resetSettings = () => {
  Object.assign(form, {
    theme: 'light',
    primaryColor: '#409eff',
    sidebarCollapsed: false,
    showBreadcrumb: true,
    showTabs: true,
    fontSize: 'medium',
    fontFamily: 'system'
  })
}
</script>

<style scoped>
.appearance-settings {
  max-width: 800px;
}

.settings-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.section-description {
  color: #666;
  font-size: 14px;
  margin: 0 0 24px 0;
}

.form-section {
  margin-bottom: 24px;
  border: 1px solid #e6e6e6;
}

.color-picker-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.color-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s;
}

.color-option:hover {
  background: #f5f5f5;
}

.color-option.active {
  background: #e6f7ff;
  border: 1px solid #409eff;
}

.color-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-bottom: 4px;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-name {
  font-size: 12px;
  color: #666;
}

.switch-label {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}
</style>
