/**
 * 内存泄漏检测工具
 * 用于检测和预防Vue应用中的内存泄漏
 */

interface MemorySnapshot {
  timestamp: number
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
  domNodes: number
  listeners: number
  components: number
}

interface LeakDetectionResult {
  hasLeak: boolean
  severity: 'low' | 'medium' | 'high'
  details: string[]
  recommendations: string[]
}

export class MemoryLeakDetector {
  private snapshots: MemorySnapshot[] = []
  private maxSnapshots = 50
  private detectionInterval: number | null = null
  private componentRegistry = new Set<string>()
  private listenerRegistry = new Map<string, number>()

  /**
   * 开始内存监控
   */
  startMonitoring(intervalMs: number = 5000): void {
    if (this.detectionInterval) {
      this.stopMonitoring()
    }

    this.detectionInterval = setInterval(() => {
      this.takeSnapshot()
      this.analyzeSnapshots()
    }, intervalMs)

    console.log('🔍 内存泄漏检测已启动')
  }

  /**
   * 停止内存监控
   */
  stopMonitoring(): void {
    if (this.detectionInterval) {
      clearInterval(this.detectionInterval)
      this.detectionInterval = null
      console.log('⏹️ 内存泄漏检测已停止')
    }
  }

  /**
   * 拍摄内存快照
   */
  takeSnapshot(): MemorySnapshot {
    const memory = this.getMemoryInfo()
    const snapshot: MemorySnapshot = {
      timestamp: Date.now(),
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      domNodes: this.countDOMNodes(),
      listeners: this.countEventListeners(),
      components: this.componentRegistry.size
    }

    this.snapshots.push(snapshot)

    // 保持快照数量在限制内
    if (this.snapshots.length > this.maxSnapshots) {
      this.snapshots.shift()
    }

    return snapshot
  }

  /**
   * 获取内存信息
   */
  private getMemoryInfo(): {
    usedJSHeapSize: number
    totalJSHeapSize: number
    jsHeapSizeLimit: number
  } {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      }
    }

    return {
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
      jsHeapSizeLimit: 0
    }
  }

  /**
   * 计算DOM节点数量
   */
  private countDOMNodes(): number {
    return document.querySelectorAll('*').length
  }

  /**
   * 计算事件监听器数量（估算）
   */
  private countEventListeners(): number {
    let total = 0
    this.listenerRegistry.forEach(count => {
      total += count
    })
    return total
  }

  /**
   * 分析快照，检测内存泄漏
   */
  analyzeSnapshots(): LeakDetectionResult {
    if (this.snapshots.length < 5) {
      return {
        hasLeak: false,
        severity: 'low',
        details: ['快照数量不足，无法进行分析'],
        recommendations: []
      }
    }

    const result: LeakDetectionResult = {
      hasLeak: false,
      severity: 'low',
      details: [],
      recommendations: []
    }

    // 分析内存使用趋势
    this.analyzeMemoryTrend(result)
    
    // 分析DOM节点增长
    this.analyzeDOMGrowth(result)
    
    // 分析事件监听器增长
    this.analyzeListenerGrowth(result)

    // 分析组件数量
    this.analyzeComponentGrowth(result)

    if (result.hasLeak) {
      console.warn('⚠️ 检测到潜在的内存泄漏:', result)
    }

    return result
  }

  /**
   * 分析内存使用趋势
   */
  private analyzeMemoryTrend(result: LeakDetectionResult): void {
    const recent = this.snapshots.slice(-10)
    const memoryGrowth = recent[recent.length - 1].usedJSHeapSize - recent[0].usedJSHeapSize
    const timeSpan = recent[recent.length - 1].timestamp - recent[0].timestamp
    const growthRate = memoryGrowth / timeSpan * 1000 // bytes per second

    if (growthRate > 1024 * 100) { // 100KB/s
      result.hasLeak = true
      result.severity = 'high'
      result.details.push(`内存增长过快: ${(growthRate / 1024).toFixed(2)} KB/s`)
      result.recommendations.push('检查是否有未清理的定时器、事件监听器或闭包引用')
    } else if (growthRate > 1024 * 10) { // 10KB/s
      result.hasLeak = true
      result.severity = 'medium'
      result.details.push(`内存持续增长: ${(growthRate / 1024).toFixed(2)} KB/s`)
      result.recommendations.push('监控内存使用，检查可能的内存泄漏源')
    }
  }

  /**
   * 分析DOM节点增长
   */
  private analyzeDOMGrowth(result: LeakDetectionResult): void {
    const recent = this.snapshots.slice(-5)
    const domGrowth = recent[recent.length - 1].domNodes - recent[0].domNodes

    if (domGrowth > 1000) {
      result.hasLeak = true
      result.severity = result.severity === 'high' ? 'high' : 'medium'
      result.details.push(`DOM节点增长过多: +${domGrowth} 个节点`)
      result.recommendations.push('检查是否有未清理的DOM元素或组件')
    }
  }

  /**
   * 分析事件监听器增长
   */
  private analyzeListenerGrowth(result: LeakDetectionResult): void {
    const recent = this.snapshots.slice(-5)
    const listenerGrowth = recent[recent.length - 1].listeners - recent[0].listeners

    if (listenerGrowth > 50) {
      result.hasLeak = true
      result.severity = result.severity === 'high' ? 'high' : 'medium'
      result.details.push(`事件监听器增长过多: +${listenerGrowth} 个监听器`)
      result.recommendations.push('检查是否有未移除的事件监听器')
    }
  }

  /**
   * 分析组件数量增长
   */
  private analyzeComponentGrowth(result: LeakDetectionResult): void {
    const recent = this.snapshots.slice(-5)
    const componentGrowth = recent[recent.length - 1].components - recent[0].components

    if (componentGrowth > 20) {
      result.hasLeak = true
      result.severity = result.severity === 'high' ? 'high' : 'medium'
      result.details.push(`组件数量增长过多: +${componentGrowth} 个组件`)
      result.recommendations.push('检查组件是否正确卸载，避免组件实例累积')
    }
  }

  /**
   * 注册组件
   */
  registerComponent(componentName: string): void {
    this.componentRegistry.add(componentName)
  }

  /**
   * 注销组件
   */
  unregisterComponent(componentName: string): void {
    this.componentRegistry.delete(componentName)
  }

  /**
   * 注册事件监听器
   */
  registerListener(listenerId: string): void {
    const current = this.listenerRegistry.get(listenerId) || 0
    this.listenerRegistry.set(listenerId, current + 1)
  }

  /**
   * 注销事件监听器
   */
  unregisterListener(listenerId: string): void {
    const current = this.listenerRegistry.get(listenerId) || 0
    if (current <= 1) {
      this.listenerRegistry.delete(listenerId)
    } else {
      this.listenerRegistry.set(listenerId, current - 1)
    }
  }

  /**
   * 获取当前状态
   */
  getStatus(): {
    isMonitoring: boolean
    snapshotCount: number
    latestSnapshot: MemorySnapshot | null
    registeredComponents: number
    registeredListeners: number
  } {
    return {
      isMonitoring: this.detectionInterval !== null,
      snapshotCount: this.snapshots.length,
      latestSnapshot: this.snapshots[this.snapshots.length - 1] || null,
      registeredComponents: this.componentRegistry.size,
      registeredListeners: this.listenerRegistry.size
    }
  }

  /**
   * 清理检测器
   */
  cleanup(): void {
    this.stopMonitoring()
    this.snapshots = []
    this.componentRegistry.clear()
    this.listenerRegistry.clear()
  }

  /**
   * 导出内存报告
   */
  exportReport(): {
    summary: any
    snapshots: MemorySnapshot[]
    analysis: LeakDetectionResult
  } {
    const analysis = this.analyzeSnapshots()
    const latest = this.snapshots[this.snapshots.length - 1]
    
    return {
      summary: {
        timestamp: Date.now(),
        totalSnapshots: this.snapshots.length,
        currentMemoryUsage: latest ? latest.usedJSHeapSize : 0,
        currentDOMNodes: latest ? latest.domNodes : 0,
        registeredComponents: this.componentRegistry.size,
        registeredListeners: this.listenerRegistry.size
      },
      snapshots: [...this.snapshots],
      analysis
    }
  }
}

// 创建全局实例
export const memoryLeakDetector = new MemoryLeakDetector()

// 在开发环境下自动启动监控
if (import.meta.env.DEV) {
  memoryLeakDetector.startMonitoring(10000) // 10秒间隔
  
  // 暴露到全局，方便调试
  ;(window as any).memoryLeakDetector = memoryLeakDetector
}
