<template>
  <div class="dashboard-view">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">投资仪表盘</h1>

          <!-- 实时连接状态指示器 -->
          <div class="connection-status" :class="`status-${connectionStatus}`">
            <div class="status-indicator">
              <div class="status-dot"></div>
              <span class="status-text">
                {{ connectionStatus === 'connected' ? '实时连接' :
                   connectionStatus === 'connecting' ? '连接中...' :
                   connectionStatus === 'error' ? '连接错误' : '已断开' }}
              </span>
            </div>
            <div v-if="connectionQuality && isConnected" class="connection-quality">
              <span :class="`quality-${connectionQuality}`">
                {{ connectionQuality === 'excellent' ? '优秀' :
                   connectionQuality === 'good' ? '良好' : '较差' }}
              </span>
            </div>
          </div>
        </div>

        <div class="header-actions">
          <el-button-group>
            <el-button
              :type="timeRange === 'today' ? 'primary' : 'default'"
              size="small"
              @click="setTimeRange('today')"
            >
              今日
            </el-button>
            <el-button
              :type="timeRange === 'week' ? 'primary' : 'default'"
              size="small"
              @click="setTimeRange('week')"
            >
              本周
            </el-button>
            <el-button
              :type="timeRange === 'month' ? 'primary' : 'default'"
              size="small"
              @click="setTimeRange('month')"
            >
              本月
            </el-button>
          </el-button-group>

          <el-button
            type="primary"
            @click="refreshData"
            :loading="refreshing"
          >
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 核心指标卡片 - 使用专业版MetricCard组件 -->
    <div class="metrics-grid">
      <el-row :gutter="20">
        <el-col :span="6">
          <MetricCard
            title="总资产"
            :value="formatCurrency(accountMetrics.totalAssets || 0)"
            unit="¥"
            :change="accountMetrics.totalProfit || 0"
            :change-percent="accountMetrics.totalProfitPercent || 0"
            type="primary"
            :chart-data="portfolioTrendChartData"
            :show-chart="true"
            :clickable="true"
            @click="viewPortfolioDetail"
          />
        </el-col>

        <el-col :span="6">
          <MetricCard
            title="今日盈亏"
            :value="formatCurrency(Math.abs(accountMetrics.dailyProfit || 0))"
            unit="¥"
            :change="accountMetrics.dailyProfit || 0"
            :change-percent="accountMetrics.dailyProfitPercent || 0"
            :type="(accountMetrics.dailyProfit || 0) >= 0 ? 'success' : 'danger'"
            :chart-data="dailyTrendChartData"
            :show-chart="true"
            :clickable="true"
            @click="viewDailyDetail"
          />
        </el-col>

        <el-col :span="6">
          <MetricCard
            title="总盈亏"
            :value="formatCurrency(Math.abs(accountMetrics.totalProfit || 0))"
            unit="¥"
            :change="accountMetrics.totalProfit || 0"
            :change-percent="accountMetrics.totalProfitPercent || 0"
            :type="(accountMetrics.totalProfit || 0) >= 0 ? 'success' : 'danger'"
            :chart-data="totalProfitTrendChartData"
            :show-chart="true"
            :clickable="true"
            @click="viewProfitDetail"
          />
        </el-col>

        <el-col :span="6">
          <DashboardCard
            title="持仓股票"
            :value="`${accountMetrics.positionCount}`"
            :sub-value="`活跃策略: ${accountMetrics.activeStrategies}`"
            color="info"
            :show-progress="true"
            :progress-value="(accountMetrics.positionCount / 20) * 100"
            progress-label="仓位使用率"
            @click="viewPositionDetail"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 快速导航区域 -->
    <div class="quick-navigation-section">
      <el-card class="navigation-card">
        <template #header>
          <div class="card-header">
            <h3>🚀 快速导航</h3>
            <span class="header-subtitle">快速访问常用功能</span>
          </div>
        </template>

        <div class="navigation-grid">
          <div
            v-for="item in quickNavItems"
            :key="item.path"
            class="nav-item"
            @click="navigateTo(item.path)"
          >
            <div class="nav-icon">
              <el-icon :size="24">
                <component :is="item.icon" />
              </el-icon>
            </div>
            <div class="nav-content">
              <h4 class="nav-title">{{ item.title }}</h4>
              <p class="nav-description">{{ item.description }}</p>
            </div>
            <div class="nav-arrow">
              <el-icon>
                <ArrowRight />
              </el-icon>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <el-row :gutter="20">
        <!-- 左侧区域 -->
        <el-col :span="16">
          <!-- 投资组合图表 - 专业版AdvancedKLineChart -->
          <el-card class="dashboard-panel enhanced-chart-panel">
            <template #header>
              <div class="panel-header">
                <h3>📊 投资组合趋势分析</h3>
              </div>
            </template>

            <div class="enhanced-chart-container">
              <div class="chart-placeholder" v-if="chartLoading">
                <el-skeleton animated>
                  <template #template>
                    <el-skeleton-item variant="rect" style="width: 100%; height: 500px;" />
                  </template>
                </el-skeleton>
              </div>
              <div v-else class="portfolio-chart-area">
                <div class="chart-stats">
                  <div class="stat-item">
                    <span class="stat-label">当前净值</span>
                    <span class="stat-value primary">{{ (accountMetrics.totalAssets / 100000).toFixed(4) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">今日涨跌</span>
                    <span class="stat-value" :class="(accountMetrics.dailyProfitPercent || 0) >= 0 ? 'success' : 'danger'">
                      {{ formatPercent(accountMetrics.dailyProfitPercent || 0) }}
                    </span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">累计收益</span>
                    <span class="stat-value" :class="(accountMetrics.totalProfitPercent || 0) >= 0 ? 'success' : 'danger'">
                      {{ formatPercent(accountMetrics.totalProfitPercent || 0) }}
                    </span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">活跃策略</span>
                    <span class="stat-value info">{{ accountMetrics.activeStrategies }}</span>
                  </div>
                </div>

                <!-- 高级K线图表组件 -->
                <AdvancedKLineChart
                  symbol="PORTFOLIO"
                  symbol-name="投资组合"
                  height="400px"
                  :auto-resize="true"
                  @period-change="handleChartPeriodChange"
                  @chart-type-change="handleChartTypeChange"
                  @indicator-add="handleIndicatorAdd"
                  @indicator-remove="handleIndicatorRemove"
                />
              </div>
            </div>
          </el-card>

          <!-- 持仓列表 -->
          <el-card class="dashboard-panel" style="margin-top: 20px;">
            <template #header>
              <h3>持仓概览</h3>
            </template>

            <el-table :data="mockPositions" style="width: 100%">
              <el-table-column prop="symbol" label="股票代码" width="100" />
              <el-table-column prop="name" label="股票名称" width="120" />
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="avgCost" label="成本价" width="80">
                <template #default="{ row }">
                  ¥{{ row.avgCost.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="currentPrice" label="现价" width="80">
                <template #default="{ row }">
                  ¥{{ row.currentPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="marketValue" label="市值" width="100">
                <template #default="{ row }">
                  ¥{{ row.marketValue.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="pnl" label="盈亏" width="100">
                <template #default="{ row }">
                  <span :class="row.pnl >= 0 ? 'text-success' : 'text-danger'">
                    {{ row.pnl >= 0 ? '+' : '' }}¥{{ row.pnl.toFixed(2) }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <!-- 右侧区域 -->
        <el-col :span="8">
          <!-- 快速交易面板 -->
          <el-card class="dashboard-panel professional-panel">
            <template #header>
              <div class="panel-header">
                <h3>🚀 快速交易</h3>
                <el-button type="primary" size="small" @click="openTradingTerminal">
                  进入交易终端
                </el-button>
              </div>
            </template>

            <div class="quick-trading">
              <div class="trading-stats">
                <div class="stat-row">
                  <span class="stat-label">可用资金</span>
                  <span class="stat-value">¥{{ formatCurrency(accountMetrics.totalAssets * 0.3 || 0) }}</span>
                </div>
                <div class="stat-row">
                  <span class="stat-label">持仓市值</span>
                  <span class="stat-value">¥{{ formatCurrency(accountMetrics.totalAssets * 0.7 || 0) }}</span>
                </div>
                <div class="stat-row">
                  <span class="stat-label">今日成交</span>
                  <span class="stat-value">12笔</span>
                </div>
              </div>

              <div class="quick-actions">
                <el-button type="success" size="small" style="width: 48%;" @click="quickBuy">
                  快速买入
                </el-button>
                <el-button type="danger" size="small" style="width: 48%;" @click="quickSell">
                  快速卖出
                </el-button>
              </div>
            </div>
          </el-card>

          <!-- 市场概览 -->
          <el-card class="dashboard-panel" style="margin-top: 20px;">
            <template #header>
              <div class="panel-header">
                <h3>📈 市场概览</h3>
                <el-button text size="small" @click="viewMarketDetail">
                  查看更多 →
                </el-button>
              </div>
            </template>

            <div class="market-indices">
              <div class="index-item" v-for="index in marketIndices" :key="index.symbol">
                <div class="index-info">
                  <span class="index-name">{{ index.name }}</span>
                  <span
                    class="index-value"
                    :class="getPriceFlashClass(index.symbol)"
                  >
                    {{ index.value.toFixed(2) }}
                  </span>
                </div>
                <div class="index-change" :class="index.change >= 0 ? 'text-success' : 'text-danger'">
                  {{ index.change >= 0 ? '+' : '' }}{{ index.change.toFixed(2) }} ({{ index.changePercent.toFixed(2) }}%)
                </div>
              </div>
            </div>
          </el-card>

          <!-- 热门股票 -->
          <el-card class="dashboard-panel" style="margin-top: 20px;">
            <template #header>
              <h3>今日热门</h3>
            </template>

            <div class="hot-stocks-list">
              <div class="hot-stock-item" v-for="stock in hotStocks" :key="stock.symbol">
                <div class="stock-info">
                  <span class="stock-symbol">{{ stock.symbol }}</span>
                  <span class="stock-name">{{ stock.name }}</span>
                </div>
                <div class="stock-price">
                  <span
                    class="price"
                    :class="getPriceFlashClass(stock.symbol)"
                  >
                    ¥{{ stock.currentPrice.toFixed(2) }}
                  </span>
                  <span class="change" :class="stock.change >= 0 ? 'text-success' : 'text-danger'">
                    {{ stock.change >= 0 ? '+' : '' }}{{ stock.changePercent.toFixed(2) }}%
                  </span>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 实时资讯推送 -->
          <el-card class="dashboard-panel realtime-news-panel" style="margin-top: 20px;">
            <template #header>
              <div class="panel-header">
                <h3>📰 实时资讯</h3>
                <div class="news-status">
                  <el-badge
                    :value="realtimeNews.length"
                    :max="99"
                    type="primary"
                  >
                    <el-icon><Bell /></el-icon>
                  </el-badge>
                </div>
              </div>
            </template>

            <div class="realtime-news-list">
              <div
                class="realtime-news-item"
                v-for="news in realtimeNews.slice(0, 8)"
                :key="news.id"
                :class="`importance-${news.importance}`"
              >
                <div class="news-header">
                  <span class="news-category" :class="`category-${news.category}`">
                    {{ getCategoryName(news.category) }}
                  </span>
                  <span class="news-time">{{ formatRelativeTime(news.timestamp) }}</span>
                </div>
                <div class="news-title">{{ news.title }}</div>
                <div v-if="news.content" class="news-content">
                  {{ news.content.slice(0, 50) }}{{ news.content.length > 50 ? '...' : '' }}
                </div>
              </div>

              <div v-if="realtimeNews.length === 0" class="no-news">
                <el-empty description="暂无实时资讯" :image-size="60" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { Bell, ArrowRight } from '@element-plus/icons-vue'
import { formatTime } from '@/utils/formatters'
import { useTradingStore } from '@/stores/modules/trading'
import { useMarketStore } from '@/stores/modules/market'
import { usePortfolioStore } from '@/stores/modules/portfolio'
import { debounce, throttle, PerformanceMonitor } from '@/utils/performance'
import * as echarts from 'echarts'
import SkeletonLoader from '@/components/common/SkeletonLoader.vue'
import MetricCard from '@/components/widgets/MetricCard.vue'
import AdvancedKLineChart from '@/components/charts/AdvancedKLineChart.vue'
import DashboardCard from './components/DashboardCard.vue'
import { safeInitChart, setupChartResize, createLineChartOption } from '@/utils/chartUtils'
import { useRealTimeData } from '@/composables/useRealTimeData'

const router = useRouter()

// Store引用
const tradingStore = useTradingStore()
const marketStore = useMarketStore()
const portfolioStore = usePortfolioStore()

// 实时数据流
const {
  isConnected,
  connectionStatus,
  connectionQuality,
  realTimeMetrics,
  priceFlashEffects,
  realtimeNews,
  getPriceFlashClass
} = useRealTimeData()

// 状态
const refreshing = ref(false)
const chartLoading = ref(true)
const timeRange = ref<'today' | 'week' | 'month'>('today')
const chartTimeframe = ref('1D')
const chartType = ref<'line' | 'candle'>('line')

// 图表引用
const portfolioChartRef = ref<HTMLElement>()
let portfolioChart: echarts.ECharts | null = null
let chartCleanupFunctions: Array<() => void> = []

// 计算属性 - 优先使用实时数据，回退到store数据
const accountMetrics = computed(() => {
  const account = tradingStore.account || {}
  const positions = tradingStore.positions || []

  // 如果有实时数据且连接正常，优先使用实时数据
  if (isConnected.value && realTimeMetrics.lastUpdate) {
    return {
      totalAssets: realTimeMetrics.totalAssets || account.totalAssets || 0,
      dailyProfit: realTimeMetrics.dailyProfit || account.dailyProfit || 0,
      dailyProfitPercent: realTimeMetrics.dailyProfitPercent || account.dailyProfitPercent || 0,
      totalProfit: realTimeMetrics.totalProfit || account.totalProfit || 0,
      totalProfitPercent: realTimeMetrics.totalProfitPercent || account.totalProfitPercent || 0,
      positionCount: realTimeMetrics.positionCount || positions.length,
      activeStrategies: realTimeMetrics.activeStrategies || 3
    }
  }

  // 回退到store数据
  return {
    totalAssets: account.totalAssets || 0,
    dailyProfit: account.dailyProfit || 0,
    dailyProfitPercent: account.dailyProfitPercent || 0,
    totalProfit: account.totalProfit || 0,
    totalProfitPercent: account.totalProfitPercent || 0,
    positionCount: positions.length,
    activeStrategies: 3
  }
})

const marketIndices = computed(() => {
  return Object.entries(marketStore.indices).map(([symbol, data]) => ({
    symbol,
    name: getIndexName(symbol),
    value: data.currentPrice,
    change: data.change,
    changePercent: data.changePercent
  }))
})

const hotStocks = computed(() => {
  return marketStore.hotStocks.slice(0, 5)
})

// 图表数据
const portfolioTrendData = computed(() => {
  // 生成模拟的投资组合趋势数据
  const days = 30
  const data = []
  let baseValue = 100000

  for (let i = 0; i < days; i++) {
    const change = (Math.random() - 0.5) * 2000
    baseValue += change
    data.push({
      date: new Date(Date.now() - (days - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      value: baseValue
    })
  }
  return data
})

const dailyTrendData = computed(() => {
  // 生成模拟的日内趋势数据
  const hours = 24
  const data = []
  let baseValue = 1000

  for (let i = 0; i < hours; i++) {
    const change = (Math.random() - 0.5) * 200
    baseValue += change
    data.push({
      time: `${i.toString().padStart(2, '0')}:00`,
      value: baseValue
    })
  }
  return data
})

const totalProfitTrendData = computed(() => {
  // 生成模拟的总盈亏趋势数据
  const months = 12
  const data = []
  let baseValue = 0

  for (let i = 0; i < months; i++) {
    const change = (Math.random() - 0.3) * 5000 // 略微偏向盈利
    baseValue += change
    data.push({
      month: `${new Date().getFullYear()}-${(i + 1).toString().padStart(2, '0')}`,
      value: baseValue
    })
  }
  return data
})

const portfolioAssetData = computed(() => {
  return portfolioTrendData.value.map(item => ({
    date: new Date(item.date).getTime(),
    value: item.value,
    benchmark: item.value * 0.95 // 模拟基准数据
  }))
})

const portfolioKLineData = computed(() => {
  // 生成模拟的K线数据
  const days = 60
  const data = []
  let basePrice = 100000

  for (let i = 0; i < days; i++) {
    const open = basePrice
    const change = (Math.random() - 0.5) * 4000
    const close = open + change
    const high = Math.max(open, close) + Math.random() * 1000
    const low = Math.min(open, close) - Math.random() * 1000
    const volume = Math.random() * 1000000

    data.push({
      timestamp: new Date(Date.now() - (days - i) * 24 * 60 * 60 * 1000).getTime(),
      open,
      high,
      low,
      close,
      volume
    })

    basePrice = close
  }
  return data
})

const latestNews = computed(() => {
  return Array.isArray(marketStore.news) ? marketStore.news.slice(0, 3) : []
})

// 图表数据数组格式（用于 MetricCard）
const portfolioTrendChartData = computed(() => {
  return portfolioTrendData.value.map(item => item.value)
})

const dailyTrendChartData = computed(() => {
  return dailyTrendData.value.map(item => item.value)
})

const totalProfitTrendChartData = computed(() => {
  return totalProfitTrendData.value.map(item => item.value)
})

// 模拟数据 - 保留作为后备
const mockPositions = ref([
  { symbol: '000001', name: '平安银行', quantity: 1000, avgCost: 12.50, currentPrice: 13.20, marketValue: 13200, pnl: 700 },
  { symbol: '000002', name: '万科A', quantity: 500, avgCost: 24.00, currentPrice: 23.50, marketValue: 11750, pnl: -250 },
  { symbol: '000858', name: '五粮液', quantity: 100, avgCost: 150.00, currentPrice: 158.00, marketValue: 15800, pnl: 800 }
])

// 格式化函数
const formatCurrency = (value: number, showSign = false): string => {
  const formatted = new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(Math.abs(value))

  if (showSign && value !== 0) {
    return value > 0 ? `+${formatted}` : `-${formatted}`
  }
  return formatted
}

const formatPercent = (value: number): string => {
  if (value === 0) return '0.00%'
  const sign = value > 0 ? '+' : ''
  return `${sign}${value.toFixed(2)}%`
}

const getChangeClass = (value: number): string => {
  if (value > 0) return 'text-success'
  if (value < 0) return 'text-danger'
  return 'text-info'
}

// 方法
const getIndexName = (symbol: string): string => {
  const nameMap: Record<string, string> = {
    'SH000001': '上证指数',
    'SZ399001': '深证成指',
    'SZ399006': '创业板指',
    'SH000688': '科创50'
  }
  return nameMap[symbol] || symbol
}



const setTimeRange = (range: 'today' | 'week' | 'month') => {
  timeRange.value = range
  // 重新加载对应时间范围的数据
  loadPortfolioData(range)
}

// 使用防抖优化数据刷新，避免频繁请求
const refreshData = debounce(async () => {
  if (refreshing.value) return

  PerformanceMonitor.mark('refresh-start')
  refreshing.value = true

  // 显示刷新提示
  ElMessage.info('正在刷新数据...')

  try {
    // 并行请求，提升性能
    await Promise.all([
      tradingStore.refresh(),
      marketStore.fetchMarketOverview(),
      marketStore.fetchHotStocks(),
      marketStore.fetchNews()
    ])

    // 刷新图表数据
    await loadPortfolioData(timeRange.value)

    // 成功提示
    ElMessage.success('数据刷新成功！')
    PerformanceMonitor.measure('数据刷新', 'refresh-start')
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('数据刷新失败，请重试')
  } finally {
    refreshing.value = false
  }
}, 1000) // 1秒防抖

const viewMarketDetail = () => {
  router.push('/market')
}

// 快速交易功能
const openTradingTerminal = () => {
  router.push('/trading/terminal')
  ElMessage.success('正在打开专业交易终端...')
}

const quickBuy = () => {
  router.push('/trading/terminal?action=buy')
  ElMessage.success('正在打开快速买入界面...')
}

const quickSell = () => {
  router.push('/trading/terminal?action=sell')
  ElMessage.success('正在打开快速卖出界面...')
}

// 快速导航数据
const quickNavItems = ref([
  {
    path: '/market/realtime',
    title: '实时行情',
    description: '查看股票实时价格和市场动态',
    icon: 'TrendCharts'
  },
  {
    path: '/trading/terminal',
    title: '交易终端',
    description: '专业交易界面，支持下单和管理',
    icon: 'Monitor'
  },
  {
    path: '/strategy/center',
    title: '策略中心',
    description: '管理和监控投资策略',
    icon: 'DataAnalysis'
  },
  {
    path: '/backtest/analysis',
    title: '回测分析',
    description: '策略历史表现分析和优化',
    icon: 'DataLine'
  },
  {
    path: '/portfolio',
    title: '投资组合',
    description: '查看持仓和资产配置',
    icon: 'PieChart'
  },
  {
    path: '/risk',
    title: '风险管理',
    description: '监控投资风险和设置预警',
    icon: 'Warning'
  }
])

// 快速导航方法
const navigateTo = (path: string) => {
  router.push(path)
  const item = quickNavItems.value.find(item => item.path === path)
  if (item) {
    ElMessage.success(`正在打开${item.title}...`)
  }
}

// 新闻相关工具函数
const getCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    market: '市场',
    trading: '交易',
    risk: '风险',
    system: '系统'
  }
  return categoryMap[category] || category
}

const formatRelativeTime = (timestamp: Date) => {
  const now = new Date()
  const diff = now.getTime() - timestamp.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 高级图表事件处理方法
const handleChartPeriodChange = (period: string) => {
  chartTimeframe.value = period
  ElMessage.success(`时间周期已切换到${period}`)
  // 重新加载对应周期的数据
  loadPortfolioData(period)
}

const handleChartTypeChange = (type: string) => {
  chartType.value = type as 'line' | 'candle'
  ElMessage.success(`图表类型已切换到${type === 'line' ? '线图' : 'K线图'}`)
}

const handleIndicatorAdd = (indicator: string) => {
  ElMessage.success(`已添加技术指标：${indicator}`)
  // 这里可以处理指标添加逻辑
}

const handleIndicatorRemove = (indicator: string) => {
  ElMessage.info(`已移除技术指标：${indicator}`)
  // 这里可以处理指标移除逻辑
}

// 保留原有的兼容方法
const toggleChartType = () => {
  const newType = chartType.value === 'line' ? 'candle' : 'line'
  handleChartTypeChange(newType)
}

const exportChart = () => {
  ElMessage.info('正在导出图表...')
  // 这里可以实现图表导出功能
  setTimeout(() => {
    ElMessage.success('图表导出成功')
  }, 1000)
}

const fullscreenChart = () => {
  ElMessage.info('进入全屏模式')
  // 这里可以实现全屏功能
}

const handleTimeframeChange = (timeframe: string) => {
  handleChartPeriodChange(timeframe)
}

// MetricCard 点击事件处理
const viewPortfolioDetail = () => {
  router.push('/portfolio')
  ElMessage.info('跳转到投资组合详情')
}

const viewDailyDetail = () => {
  router.push('/trading/history')
  ElMessage.info('查看今日交易详情')
}

const viewProfitDetail = () => {
  router.push('/analytics/profit')
  ElMessage.info('查看盈亏分析')
}

const viewPositionDetail = () => {
  router.push('/trading/positions')
  ElMessage.info('查看持仓详情')
}

const loadPortfolioData = async (range: string) => {
  try {
    await portfolioStore.fetchPortfolioTrend({
      timeRange: range,
      symbol: 'portfolio'
    })
    updatePortfolioChart()
  } catch (error) {
    console.error('加载投资组合数据失败:', error)
  }
}

const initPortfolioChart = async () => {
  try {
    // 确保图表容器可见
    chartLoading.value = false
    await nextTick()

    const chart = await safeInitChart(portfolioChartRef, {
      maxRetries: 15,
      retryDelay: 300,
      onRetry: (retryCount) => {
        console.warn(`投资组合图表容器未找到，延迟初始化 (${retryCount}/15)`)
      },
      onError: (error) => {
        console.error('投资组合图表初始化失败:', error)
        chartLoading.value = false
      },
      onSuccess: (chartInstance) => {
        portfolioChart = chartInstance

        // 设置默认图表配置
        const defaultOption = {
          title: {
            text: '投资组合价值趋势',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal'
            }
          },
          tooltip: {
            trigger: 'axis',
            formatter: (params: any) => {
              const data = params[0]
              const value = data?.value || 0
              return `${data?.name || ''}<br/>总资产: ¥${value.toLocaleString()}`
            }
          },
          xAxis: {
            type: 'category',
            data: ['今日', '昨日', '前日'],
            axisLine: {
              lineStyle: {
                color: '#E8E8E8'
              }
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: (value: number) => `¥${(value / 10000).toFixed(0)}万`
            },
            splitLine: {
              lineStyle: {
                color: '#E8E8E8'
              }
            }
          },
          series: [
            {
              name: '总资产',
              type: 'line',
              data: [1000000, 980000, 1020000],
              smooth: true,
              symbol: 'circle',
              symbolSize: 6,
              lineStyle: {
                width: 3,
                color: '#5470c6'
              },
              itemStyle: {
                color: '#5470c6'
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(84, 112, 198, 0.3)' },
                  { offset: 1, color: 'rgba(84, 112, 198, 0.05)' }
                ])
              }
            }
          ]
        }

        chartInstance.setOption(defaultOption)

        // 设置响应式处理
        const cleanupResize = setupChartResize(chartInstance)

        // 保存清理函数，在组件卸载时调用
        chartCleanupFunctions.push(() => {
          cleanupResize()
          chartInstance.dispose()
        })

        console.log('投资组合图表初始化成功')
        chartLoading.value = false
      }
    })

    if (!chart) {
      chartLoading.value = false
    }

  } catch (error) {
    console.error('投资组合图表初始化失败:', error)
    chartLoading.value = false
  }
}

const updatePortfolioChart = () => {
  if (!portfolioChart) return

  const trendData = portfolioStore.portfolioTrend

  // 如果没有数据，使用模拟数据
  const chartData = trendData.length > 0 ? trendData : [
    { date: '今日', totalAssets: 1000000 },
    { date: '昨日', totalAssets: 980000 },
    { date: '前日', totalAssets: 1020000 }
  ]

  const option = {
    title: {
      text: '投资组合价值趋势',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        const value = data?.value || 0
        return `${data?.name || ''}<br/>总资产: ¥${value.toLocaleString()}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.date),
      axisLine: {
        lineStyle: {
          color: '#e0e6ed'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '总资产',
        type: 'line',
        smooth: true,
        data: chartData.map(item => item.totalAssets),
        lineStyle: {
          color: '#409eff',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  portfolioChart.setOption(option)
}

onMounted(async () => {
  try {
    // 初始化数据
    await Promise.allSettled([
      tradingStore.initialize(),
      marketStore.initialize(),
      portfolioStore.initialize()
    ])

    // 延迟初始化图表，确保DOM完全渲染和样式计算完成
    setTimeout(async () => {
      try {
        await initPortfolioChart()
      } catch (error) {
        console.error('图表初始化失败:', error)
        // 再次尝试初始化
        setTimeout(() => initPortfolioChart(), 1000)
      }
    }, 500)

    // 加载投资组合数据
    await loadPortfolioData(timeRange.value)
  } catch (error) {
    console.error('仪表盘初始化失败:', error)
    // 即使初始化失败，也要初始化图表
    setTimeout(() => initPortfolioChart(), 1000)
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 执行所有清理函数
  chartCleanupFunctions.forEach(cleanup => {
    try {
      cleanup()
    } catch (error) {
      console.error('清理图表资源失败:', error)
    }
  })
  chartCleanupFunctions = []

  // 清理图表实例
  if (portfolioChart) {
    try {
      portfolioChart.dispose()
      portfolioChart = null
    } catch (error) {
      console.error('清理投资组合图表失败:', error)
    }
  }
})
</script>

<style scoped>
.dashboard-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 连接状态指示器样式 */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.connection-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-connected .status-dot {
  background: #67c23a;
}

.status-connecting .status-dot {
  background: #e6a23c;
}

.status-disconnected .status-dot {
  background: #909399;
}

.status-error .status-dot {
  background: #f56c6c;
}

.status-text {
  color: #666;
  font-weight: 500;
}

.connection-quality {
  font-size: 10px;
}

.quality-excellent {
  color: #67c23a;
}

.quality-good {
  color: #e6a23c;
}

.quality-poor {
  color: #f56c6c;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 价格闪烁效果 */
.price-flash {
  transition: all 0.3s ease;
  border-radius: 4px;
  padding: 2px 4px;
  margin: -2px -4px;
}

.price-flash--up {
  background: rgba(103, 194, 58, 0.3);
  color: #67c23a;
  box-shadow: 0 0 8px rgba(103, 194, 58, 0.4);
}

.price-flash--down {
  background: rgba(245, 108, 108, 0.3);
  color: #f56c6c;
  box-shadow: 0 0 8px rgba(245, 108, 108, 0.4);
}

.price-flash--neutral {
  background: rgba(144, 147, 153, 0.2);
}

/* 不透明度渐变效果 */
.opacity-10 { opacity: 1; }
.opacity-9 { opacity: 0.9; }
.opacity-8 { opacity: 0.8; }
.opacity-7 { opacity: 0.7; }
.opacity-6 { opacity: 0.6; }
.opacity-5 { opacity: 0.5; }
.opacity-4 { opacity: 0.4; }
.opacity-3 { opacity: 0.3; }
.opacity-2 { opacity: 0.2; }
.opacity-1 { opacity: 0.1; }
.opacity-0 { opacity: 0; }

/* 交互反馈优化 */
.el-button {
  transition: all 0.3s ease;
}

.el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.el-button:active {
  transform: translateY(0);
}

.el-button.is-loading {
  pointer-events: none;
}

.dashboard-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.metrics-grid {
  margin-bottom: 24px;
}

.metric-card {
  height: 120px;
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 16px;
  height: 100%;
}

.metric-icon {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f9ff;
  border-radius: 8px;
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.metric-change {
  font-size: 12px;
  color: #999;
}

.metric-change.positive {
  color: #67c23a;
}

.dashboard-content {
  margin-top: 24px;
}

.dashboard-panel {
  margin-bottom: 20px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.chart-container {
  padding: 16px 0;
  position: relative;
  min-height: 300px;
}

/* 高级图表面板样式 */
.advanced-chart-panel {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.advanced-chart-panel:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.advanced-chart-container {
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  margin: 16px 0;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 增强的面板头部 */
.panel-header h3 {
  color: #1e293b !important;
  font-weight: 700 !important;
  font-size: 18px !important;
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-header h3::before {
  content: '📊';
  font-size: 20px;
}

.panel-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 增强的指标卡片样式 */
.enhanced-metric-card {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.enhanced-metric-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.enhanced-metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
}

.enhanced-metric-card.primary-card::before {
  background: linear-gradient(90deg, #409eff, #5dade2);
}

.enhanced-metric-card.success-card::before {
  background: linear-gradient(90deg, #67c23a, #58d68d);
}

.enhanced-metric-card.danger-card::before {
  background: linear-gradient(90deg, #f56c6c, #ec7063);
}

.enhanced-metric-card.info-card::before {
  background: linear-gradient(90deg, #909399, #85929e);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.metric-icon.primary-icon {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1d4ed8;
}

.metric-icon.success-icon {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #15803d;
}

.metric-icon.danger-icon {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #dc2626;
}

.metric-icon.info-icon {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #475569;
}

.metric-title {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.metric-content {
  margin-bottom: 16px;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 4px;
}

.metric-change {
  font-size: 14px;
  font-weight: 600;
}

.metric-subtitle {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

.metric-chart {
  height: 40px;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  background: linear-gradient(90deg, rgba(64, 158, 255, 0.1) 0%, rgba(103, 194, 58, 0.1) 100%);
}

.mini-trend-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(64, 158, 255, 0.3) 70%);
  animation: trendAnimation 3s ease-in-out infinite;
}

.mini-trend-line.primary-trend {
  background: linear-gradient(45deg, transparent 30%, rgba(64, 158, 255, 0.4) 70%);
}

.mini-trend-line.success-trend {
  background: linear-gradient(45deg, transparent 30%, rgba(103, 194, 58, 0.4) 70%);
}

.mini-trend-line.danger-trend {
  background: linear-gradient(45deg, transparent 30%, rgba(245, 108, 108, 0.4) 70%);
}

@keyframes trendAnimation {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

.metric-progress {
  margin-top: 12px;
}

.progress-label {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
  display: block;
}

/* 增强的图表面板样式 */
.enhanced-chart-panel {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

.enhanced-chart-panel .panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.enhanced-chart-panel .panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.enhanced-chart-container {
  padding: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.chart-stats {
  display: flex;
  gap: 32px;
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
}

.stat-value.primary {
  color: #1d4ed8;
}

.stat-value.success {
  color: #15803d;
}

.stat-value.danger {
  color: #dc2626;
}

.chart-container {
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  position: relative;
  overflow: hidden;
}

.chart-container::before {
  content: '📊 图表加载中...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #9ca3af;
  font-size: 16px;
  z-index: 1;
}

/* 文本颜色类 */
.text-success {
  color: #15803d !important;
}

.text-danger {
  color: #dc2626 !important;
}

.text-info {
  color: #6b7280 !important;
}

.market-indices {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.index-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.index-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.index-name {
  font-size: 14px;
  color: #333;
}

.index-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.index-change {
  font-size: 14px;
  text-align: right;
}

.hot-stocks-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.hot-stock-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stock-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stock-symbol {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.stock-name {
  font-size: 12px;
  color: #666;
}

.stock-price {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: right;
}

.price {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.change {
  font-size: 12px;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.news-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.news-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}

/* 专业交易面板样式 */
.professional-panel {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.professional-panel .panel-header h3 {
  color: white !important;
  font-weight: 700;
}

.professional-panel .el-card__header {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.professional-panel .el-card__body {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.quick-trading {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.trading-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.quick-actions {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.quick-actions .el-button {
  border: none;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.quick-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* 实时新闻面板样式 */
.realtime-news-panel {
  border-left: 4px solid #409eff;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.realtime-news-panel .panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.news-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.realtime-news-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 8px 0;
}

.realtime-news-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  background: white;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  cursor: pointer;
}

.realtime-news-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
  transform: translateY(-1px);
}

.realtime-news-item.importance-high {
  border-left: 4px solid #f56c6c;
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
}

.realtime-news-item.importance-medium {
  border-left: 4px solid #e6a23c;
}

.realtime-news-item.importance-low {
  border-left: 4px solid #909399;
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.news-category {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
}

.category-market {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.category-trading {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.category-risk {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

.category-system {
  background: rgba(144, 147, 153, 0.1);
  color: #909399;
}

/* 快速导航样式 */
.quick-navigation-section {
  margin: 20px 0;
}

.navigation-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-subtitle {
  font-size: 14px;
  color: #909399;
  font-weight: normal;
}

.navigation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.nav-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  color: white;
  margin-right: 16px;
  flex-shrink: 0;
}

.nav-content {
  flex: 1;
}

.nav-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.nav-description {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
}

.nav-arrow {
  color: #c0c4cc;
  transition: all 0.3s ease;
}

.nav-item:hover .nav-arrow {
  color: #409eff;
  transform: translateX(4px);
}

.news-time {
  font-size: 11px;
  color: #999;
}

.news-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-content {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

.no-news {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

/* 滚动条美化 */
.realtime-news-list::-webkit-scrollbar {
  width: 4px;
}

.realtime-news-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.realtime-news-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.realtime-news-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
