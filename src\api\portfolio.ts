import request from './request'
import type { PortfolioData, Position, PortfolioPerformance, RebalanceRequest } from '@/types/portfolio'

// 获取投资组合概览
export const getPortfolioOverview = async () => {
  const response = await request.get<PortfolioData>('/api/v1/portfolio/overview')
  return response.data
}

// 获取持仓列表
export const getPositions = async (params?: {
  sortBy?: string
  order?: 'asc' | 'desc'
}) => {
  const response = await request.get<Position[]>('/api/v1/portfolio/positions', { params })
  return response.data
}

// 获取投资组合绩效
export const getPortfolioPerformance = async (params: {
  period: string
  benchmark?: string
}) => {
  const response = await request.get<PortfolioPerformance>('/api/v1/portfolio/performance', { params })
  return response.data
}

// 获取行业分布
export const getIndustryDistribution = async () => {
  const response = await request.get('/api/v1/portfolio/industry-distribution')
  return response.data
}

// 获取资产配置
export const getAssetAllocation = async () => {
  const response = await request.get('/api/v1/portfolio/asset-allocation')
  return response.data
}

// 获取投资组合风险指标
export const getPortfolioRiskMetrics = async () => {
  const response = await request.get('/api/v1/portfolio/risk-metrics')
  return response.data
}

// 检查再平衡需求
export const checkRebalanceNeeds = async () => {
  const response = await request.get('/api/v1/portfolio/rebalance/check')
  return response.data
}

// 执行再平衡
export const executeRebalance = async (request: RebalanceRequest) => {
  const response = await request.post('/api/v1/portfolio/rebalance/execute', request)
  return response.data
}

// 导出投资组合报告
export const exportPortfolioReport = async (params: {
  format: 'pdf' | 'excel' | 'csv'
  period?: string
  includeDetails?: boolean
}) => {
  const response = await request.post('/api/v1/portfolio/report/export', params, {
    responseType: 'blob'
  })
  return response.data
}

// 获取历史持仓
export const getPositionHistory = async (params: {
  symbol?: string
  startDate: string
  endDate: string
}) => {
  const response = await request.get('/api/v1/portfolio/positions/history', { params })
  return response.data
}

// 获取交易记录
export const getTransactionHistory = async (params: {
  symbol?: string
  type?: 'buy' | 'sell' | 'all'
  startDate?: string
  endDate?: string
  page?: number
  pageSize?: number
}) => {
  const response = await request.get('/api/v1/portfolio/transactions', { params })
  return response.data
}

// 获取股息记录
export const getDividendHistory = async (params?: {
  symbol?: string
  year?: number
}) => {
  const response = await request.get('/api/v1/portfolio/dividends', { params })
  return response.data
}

// 模拟投资组合变化
export const simulatePortfolioChange = async (changes: Array<{
  symbol: string
  action: 'buy' | 'sell'
  quantity: number
  price?: number
}>) => {
  const response = await request.post('/api/v1/portfolio/simulate', { changes })
  return response.data
}

// 获取投资组合目标配置
export const getPortfolioTargets = async () => {
  const response = await request.get('/api/v1/portfolio/targets')
  return response.data
}

// 更新投资组合目标配置
export const updatePortfolioTargets = async (targets: Array<{
  symbol: string
  targetWeight: number
  tolerance: number
}>) => {
  const response = await request.put('/api/v1/portfolio/targets', { targets })
  return response.data
}