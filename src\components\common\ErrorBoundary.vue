<template>
  <div class="error-boundary">
    <!-- 全局错误提示 -->
    <div v-if="hasError" class="error-container">
      <div class="error-content">
        <div class="error-icon">
          <el-icon :size="64" color="#F56C6C">
            <WarningFilled />
          </el-icon>
        </div>
        
        <h2 class="error-title">{{ errorInfo.title }}</h2>
        <p class="error-message">{{ errorInfo.message }}</p>
        
        <div class="error-details" v-if="showDetails">
          <el-collapse>
            <el-collapse-item title="错误详情" name="details">
              <pre class="error-stack">{{ errorInfo.stack }}</pre>
            </el-collapse-item>
          </el-collapse>
        </div>
        
        <div class="error-actions">
          <el-button type="primary" @click="handleRetry">
            重试
          </el-button>
          <el-button @click="handleReload">
            刷新页面
          </el-button>
          <el-button type="text" @click="showDetails = !showDetails">
            {{ showDetails ? '隐藏' : '显示' }}详情
          </el-button>
        </div>
        
        <div class="error-suggestions">
          <h4>可能的解决方案：</h4>
          <ul>
            <li v-for="suggestion in errorInfo.suggestions" :key="suggestion">
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>
    </div>
    
    <!-- 正常内容 -->
    <slot v-else />
  </div>
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, provide } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { WarningFilled } from '@element-plus/icons-vue'

interface ErrorInfo {
  title: string
  message: string
  stack?: string
  suggestions: string[]
  type: 'network' | 'api' | 'runtime' | 'unknown'
}

// 状态管理
const hasError = ref(false)
const showDetails = ref(false)
const errorInfo = ref<ErrorInfo>({
  title: '发生了未知错误',
  message: '抱歉，应用程序遇到了问题',
  suggestions: ['请刷新页面重试', '如果问题持续存在，请联系技术支持'],
  type: 'unknown'
})

// 错误类型映射
const errorTypeMap = {
  network: {
    title: '网络连接错误',
    message: '无法连接到服务器，请检查网络连接',
    suggestions: [
      '检查网络连接是否正常',
      '确认服务器是否正在运行',
      '尝试刷新页面',
      '如果问题持续，请联系管理员'
    ]
  },
  api: {
    title: 'API请求错误',
    message: '服务器返回了错误响应',
    suggestions: [
      '检查请求参数是否正确',
      '确认您有足够的权限',
      '稍后重试',
      '联系技术支持获取帮助'
    ]
  },
  runtime: {
    title: '运行时错误',
    message: '应用程序执行过程中发生错误',
    suggestions: [
      '刷新页面重新加载',
      '清除浏览器缓存',
      '尝试使用其他浏览器',
      '联系技术支持'
    ]
  },
  auth: {
    title: '身份验证错误',
    message: '您的登录状态已过期或无效',
    suggestions: [
      '请重新登录',
      '检查账户是否被锁定',
      '确认用户名和密码正确',
      '联系管理员重置密码'
    ]
  },
  permission: {
    title: '权限不足',
    message: '您没有执行此操作的权限',
    suggestions: [
      '联系管理员申请权限',
      '确认您的账户角色',
      '尝试使用其他账户',
      '查看帮助文档了解权限要求'
    ]
  }
}

// 错误处理函数
const handleError = (error: Error, type: keyof typeof errorTypeMap = 'runtime') => {
  console.error('Error caught by boundary:', error)
  
  const errorTemplate = errorTypeMap[type] || errorTypeMap.runtime
  
  errorInfo.value = {
    title: errorTemplate.title,
    message: errorTemplate.message,
    stack: error.stack,
    suggestions: errorTemplate.suggestions,
    type
  }
  
  hasError.value = true
  
  // 发送错误通知
  ElNotification({
    title: errorTemplate.title,
    message: errorTemplate.message,
    type: 'error',
    duration: 5000
  })
  
  // 可选：发送错误报告到服务器
  reportError(error, type)
}

// 错误报告函数
const reportError = async (error: Error, type: string) => {
  try {
    // 这里可以发送错误报告到服务器
    const errorReport = {
      message: error.message,
      stack: error.stack,
      type,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    // 发送到错误收集服务
    // await api.post('/api/v1/errors/report', errorReport)
    console.log('Error report:', errorReport)
  } catch (reportError) {
    console.error('Failed to report error:', reportError)
  }
}

// 重试函数
const handleRetry = () => {
  hasError.value = false
  showDetails.value = false
  ElMessage.success('正在重试...')
}

// 刷新页面
const handleReload = () => {
  window.location.reload()
}

// 捕获子组件错误
onErrorCaptured((error: Error) => {
  handleError(error)
  return false // 阻止错误继续传播
})

// 全局错误处理
window.addEventListener('error', (event) => {
  handleError(new Error(event.message), 'runtime')
})

window.addEventListener('unhandledrejection', (event) => {
  handleError(new Error(event.reason), 'runtime')
})

// 提供错误处理方法给子组件
provide('errorHandler', {
  handleNetworkError: (error: Error) => handleError(error, 'network'),
  handleApiError: (error: Error) => handleError(error, 'api'),
  handleAuthError: (error: Error) => handleError(error, 'auth'),
  handlePermissionError: (error: Error) => handleError(error, 'permission'),
  handleRuntimeError: (error: Error) => handleError(error, 'runtime')
})
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.error-content {
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.error-icon {
  margin-bottom: 24px;
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.error-message {
  font-size: 16px;
  color: #606266;
  margin-bottom: 32px;
  line-height: 1.6;
}

.error-details {
  margin: 24px 0;
  text-align: left;
}

.error-stack {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #606266;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.error-actions {
  margin-bottom: 32px;
}

.error-actions .el-button {
  margin: 0 8px 8px 0;
}

.error-suggestions {
  text-align: left;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.error-suggestions h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.error-suggestions ul {
  margin: 0;
  padding-left: 20px;
}

.error-suggestions li {
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-container {
    padding: 20px 16px;
  }
  
  .error-title {
    font-size: 20px;
  }
  
  .error-message {
    font-size: 14px;
  }
  
  .error-actions .el-button {
    width: 100%;
    margin: 0 0 8px 0;
  }
}

/* 暗色主题支持 */
.dark .error-title {
  color: #e5eaf3;
}

.dark .error-message {
  color: #a3a6ad;
}

.dark .error-stack {
  background: #2d2d2d;
  color: #a3a6ad;
}

.dark .error-suggestions {
  background: #2d2d2d;
  border-left-color: #409EFF;
}

.dark .error-suggestions h4 {
  color: #e5eaf3;
}

.dark .error-suggestions li {
  color: #a3a6ad;
}
</style>
