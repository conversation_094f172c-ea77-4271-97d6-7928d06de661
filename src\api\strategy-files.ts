import { httpClient } from './http'

// 配置 - 优先使用真实API
const USE_MOCK = import.meta.env.VITE_USE_MOCK === 'true'

export interface StrategyFileInfo {
  year: string
  filename: string
  title?: string
  author?: string
  source_url?: string
  description?: string
  content?: string
  content_length?: number
  line_count?: number
}

export interface StrategyFileListResponse {
  files: string[]
  total: number
  year: string
}

export interface AvailableYearsResponse {
  years: string[]
  total: number
  message: string
}

export interface SearchResult {
  year: string
  filename: string
  title?: string
  author?: string
  description?: string
}

export interface ImportResponse {
  success: boolean
  strategy_id: string
  message: string
}

/**
 * 策略文件管理API
 */
export const strategyFileApi = {
  /**
   * 获取可用年份列表
   */
  async getAvailableYears(): Promise<AvailableYearsResponse> {
    if (USE_MOCK) {
      // 返回模拟年份数据
      return {
        years: ['2024', '2023', '2022', '2021', '2020'],
        total: 5,
        message: '获取年份列表成功'
      }
    }

    try {
      const response = await httpClient.get('/api/v1/strategy-files/years')
      return response.data
    } catch (error) {
      console.warn('获取年份列表API调用失败，使用模拟数据:', error)
      // API失败时返回模拟数据
      return {
        years: ['2024', '2023', '2022'],
        total: 3,
        message: '获取年份列表成功（模拟模式）'
      }
    }
  },

  /**
   * 获取指定年份的策略文件列表
   */
  async getFilesByYear(year: string): Promise<StrategyFileListResponse> {
    if (USE_MOCK) {
      // 返回模拟文件列表数据
      const mockFiles = {
        '2024': [
          '双均线策略优化版.py',
          'RSI反转策略.py',
          '多因子选股模型.py',
          '动量策略改进版.py',
          '网格交易策略.py'
        ],
        '2023': [
          '经典双均线策略.py',
          'MACD金叉策略.py',
          '布林带突破策略.py',
          '均值回归策略.py'
        ],
        '2022': [
          '趋势跟踪策略.py',
          '配对交易策略.py',
          '统计套利策略.py'
        ]
      }

      return {
        files: mockFiles[year as keyof typeof mockFiles] || [],
        total: mockFiles[year as keyof typeof mockFiles]?.length || 0,
        year: year
      }
    }

    try {
      const response = await httpClient.get(`/strategy-files/${year}`)
      return response.data
    } catch (error) {
      console.warn(`获取${year}年文件列表API调用失败，使用模拟数据:`, error)
      // API失败时返回模拟数据
      return {
        files: ['双均线策略.py', 'RSI策略.py'],
        total: 2,
        year: year
      }
    }
  },

  /**
   * 获取策略文件详情
   */
  async getFileDetail(year: string, filename: string): Promise<StrategyFileInfo> {
    if (USE_MOCK) {
      // 返回模拟文件详情数据
      return {
        year: year,
        filename: filename,
        title: filename.replace('.py', ''),
        author: '量化研究员',
        source_url: 'https://github.com/example/strategy',
        description: `这是一个${filename.replace('.py', '')}的详细说明。该策略基于技术分析指标，适用于中长期投资。`,
        content: `# ${filename.replace('.py', '')}
import pandas as pd
import numpy as np
from typing import Dict, List

class Strategy:
    def __init__(self):
        self.name = "${filename.replace('.py', '')}"
        self.description = "策略描述"

    def initialize(self):
        """初始化策略"""
        pass

    def handle_data(self, context, data):
        """处理数据"""
        pass

    def calculate_signals(self, data):
        """计算交易信号"""
        return []

# 策略实例
strategy = Strategy()`,
        content_length: 500,
        line_count: 25
      }
    }

    try {
      const response = await httpClient.get(`/api/v1/strategy-files/${year}/${filename}`)
      return response.data
    } catch (error) {
      console.warn(`获取文件详情API调用失败，使用模拟数据:`, error)
      // API失败时返回模拟数据
      return {
        year: year,
        filename: filename,
        title: filename.replace('.py', ''),
        author: '未知作者',
        description: '策略文件详情',
        content: '# 策略代码\nprint("Hello Strategy")',
        content_length: 100,
        line_count: 2
      }
    }
  },

  /**
   * 搜索策略文件
   */
  async searchStrategies(keyword: string): Promise<SearchResult[]> {
    if (USE_MOCK) {
      // 返回模拟搜索结果
      const allStrategies = [
        { year: '2024', filename: '双均线策略优化版.py', title: '双均线策略优化版', author: '量化团队', description: '基于双均线的趋势跟踪策略' },
        { year: '2024', filename: 'RSI反转策略.py', title: 'RSI反转策略', author: '技术分析师', description: '利用RSI指标的反转策略' },
        { year: '2024', filename: '多因子选股模型.py', title: '多因子选股模型', author: '量化研究员', description: '基于多因子的选股模型' },
        { year: '2023', filename: '经典双均线策略.py', title: '经典双均线策略', author: '策略开发者', description: '经典的双均线交易策略' },
        { year: '2023', filename: 'MACD金叉策略.py', title: 'MACD金叉策略', author: '技术分析师', description: 'MACD指标金叉买入策略' }
      ]

      // 简单的关键词匹配
      const results = allStrategies.filter(strategy =>
        strategy.title.toLowerCase().includes(keyword.toLowerCase()) ||
        strategy.description.toLowerCase().includes(keyword.toLowerCase()) ||
        strategy.author.toLowerCase().includes(keyword.toLowerCase())
      )

      return results
    }

    try {
      const response = await httpClient.get('/strategy-files/search', {
        params: { keyword }
      })
      return response.data
    } catch (error) {
      console.warn('搜索策略API调用失败，使用模拟数据:', error)
      // API失败时返回模拟数据
      return [
        { year: '2024', filename: '双均线策略.py', title: '双均线策略', author: '量化团队', description: '搜索结果示例' }
      ]
    }
  },

  /**
   * 导入策略文件到用户策略库
   */
  async importFile(year: string, filename: string, strategyName?: string): Promise<ImportResponse> {
    const response = await httpClient.post(`/api/v1/strategy-files/${year}/${filename}/import`, {
      strategy_name: strategyName
    })
    return response.data
  },

  /**
   * 获取策略模板列表
   */
  async getTemplates(): Promise<any[]> {
    const response = await httpClient.get('/api/v1/strategy-files/templates')
    return response.data
  },

  /**
   * 获取策略模板内容
   */
  async getTemplateContent(templateName: string): Promise<any> {
    const response = await httpClient.get(`/api/v1/strategy-files/templates/content/${templateName}`)
    return response.data
  },

  /**
   * 使用模板创建策略
   */
  async createFromTemplate(templateName: string, filename: string): Promise<any> {
    const response = await httpClient.post('/api/v1/strategy-files/templates/use', {
      template_name: templateName,
      filename: filename
    })
    return response.data
  },

  /**
   * 获取用户策略文件列表
   */
  async getUserFiles(): Promise<any[]> {
    const response = await httpClient.get('/api/v1/strategy-files/editor/files')
    return response.data
  },

  /**
   * 获取用户策略文件内容
   */
  async getUserFileContent(filename: string): Promise<any> {
    const response = await httpClient.get(`/api/v1/strategy-files/editor/content/${filename}`)
    return response.data
  },

  /**
   * 创建新策略文件
   */
  async createFile(filename: string, content: string, description?: string): Promise<any> {
    const formData = new FormData()
    formData.append('filename', filename)
    formData.append('content', content)
    if (description) {
      formData.append('description', description)
    }

    const response = await httpClient.post('/api/v1/strategy-files/editor/create', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  /**
   * 更新策略文件
   */
  async updateFile(filename: string, content: string, description?: string): Promise<any> {
    const formData = new FormData()
    formData.append('content', content)
    if (description) {
      formData.append('description', description)
    }

    const response = await httpClient.put(`/api/v1/strategy-files/editor/update/${filename}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  /**
   * 删除策略文件
   */
  async deleteFile(filename: string): Promise<any> {
    const response = await httpClient.delete(`/api/v1/strategy-files/editor/delete/${filename}`)
    return response.data
  },

  /**
   * 上传策略文件
   */
  async uploadFile(file: File): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await httpClient.post('/api/v1/strategy-files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  /**
   * 验证策略代码
   */
  async validateCode(code: string): Promise<any> {
    const response = await httpClient.post('/api/v1/strategy-files/validate', {
      code: code
    })
    return response.data
  },

  /**
   * 格式化策略代码
   */
  async formatCode(code: string): Promise<any> {
    const response = await httpClient.post('/api/v1/strategy-files/format', {
      code: code
    })
    return response.data
  },

  /**
   * 获取策略统计信息
   */
  async getStatistics(): Promise<any> {
    const response = await httpClient.get('/api/v1/strategy-files/statistics')
    return response.data
  },

  /**
   * 批量导入策略文件
   */
  async batchImport(files: { year: string; filename: string }[]): Promise<any> {
    const response = await httpClient.post('/api/v1/strategy-files/batch-import', {
      files: files
    })
    return response.data
  },

  /**
   * 导出策略文件
   */
  async exportFile(year: string, filename: string): Promise<Blob> {
    const response = await httpClient.get(`/api/v1/strategy-files/${year}/${filename}/export`, {
      responseType: 'blob'
    })
    return response.data
  },

  /**
   * 获取策略文件历史版本
   */
  async getFileHistory(filename: string): Promise<any[]> {
    const response = await httpClient.get(`/api/v1/strategy-files/editor/history/${filename}`)
    return response.data
  },

  /**
   * 恢复策略文件历史版本
   */
  async restoreFileVersion(filename: string, version: string): Promise<any> {
    const response = await httpClient.post(`/api/v1/strategy-files/editor/restore/${filename}`, {
      version: version
    })
    return response.data
  }
}

export default strategyFileApi
