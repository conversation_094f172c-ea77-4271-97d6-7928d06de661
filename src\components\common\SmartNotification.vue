<template>
  <div class="smart-notification-container">
    <!-- 通知列表 -->
    <transition-group name="notification" tag="div" class="notification-list">
      <div
        v-for="notification in visibleNotifications"
        :key="notification.id"
        class="smart-notification"
        :class="[
          `notification-${notification.type}`,
          { 'notification-persistent': notification.persistent }
        ]"
        @mouseenter="pauseTimer(notification.id)"
        @mouseleave="resumeTimer(notification.id)"
      >
        <!-- 通知图标 -->
        <div class="notification-icon">
          <el-icon :size="20">
            <component :is="getNotificationIcon(notification.type)" />
          </el-icon>
        </div>

        <!-- 通知内容 -->
        <div class="notification-content">
          <div v-if="notification.title" class="notification-title">
            {{ notification.title }}
          </div>
          <div class="notification-message">
            {{ notification.message }}
          </div>
          
          <!-- 操作按钮 -->
          <div v-if="notification.actions?.length" class="notification-actions">
            <el-button
              v-for="action in notification.actions"
              :key="action.label"
              :type="action.type || 'primary'"
              size="small"
              @click="handleAction(notification.id, action)"
            >
              {{ action.label }}
            </el-button>
          </div>
        </div>

        <!-- 关闭按钮 -->
        <div class="notification-close" @click="closeNotification(notification.id)">
          <el-icon><Close /></el-icon>
        </div>

        <!-- 进度条 -->
        <div
          v-if="!notification.persistent && notification.showProgress"
          class="notification-progress"
          :style="{ width: getProgressWidth(notification.id) + '%' }"
        ></div>
      </div>
    </transition-group>

    <!-- 全局操作 -->
    <div v-if="visibleNotifications.length > 1" class="global-actions">
      <el-button size="small" @click="clearAll">
        清除所有
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Close, SuccessFilled, WarningFilled, CircleCloseFilled, 
  InfoFilled, QuestionFilled, Bell
} from '@element-plus/icons-vue'

// 通知类型
export type NotificationType = 'success' | 'warning' | 'error' | 'info' | 'loading'

// 通知动作接口
export interface NotificationAction {
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  handler: () => void | Promise<void>
}

// 通知接口
export interface SmartNotificationItem {
  id: string
  type: NotificationType
  title?: string
  message: string
  duration?: number
  persistent?: boolean
  showProgress?: boolean
  actions?: NotificationAction[]
  timestamp: number
}

// 响应式数据
const notifications = ref<SmartNotificationItem[]>([])
const timers = ref<Map<string, NodeJS.Timeout>>(new Map())
const progressTimers = ref<Map<string, NodeJS.Timeout>>(new Map())
const progressValues = ref<Map<string, number>>(new Map())

// 计算属性
const visibleNotifications = computed(() => {
  return notifications.value.slice(0, 5) // 最多显示5个通知
})

// 方法
const addNotification = (notification: Omit<SmartNotificationItem, 'id' | 'timestamp'>) => {
  const id = generateId()
  const newNotification: SmartNotificationItem = {
    ...notification,
    id,
    timestamp: Date.now(),
    duration: notification.duration ?? (notification.persistent ? 0 : 4000),
    showProgress: notification.showProgress ?? true
  }

  notifications.value.unshift(newNotification)

  // 设置自动关闭定时器
  if (!newNotification.persistent && newNotification.duration > 0) {
    startTimer(newNotification)
  }

  // 限制通知数量
  if (notifications.value.length > 10) {
    const removed = notifications.value.splice(10)
    removed.forEach(n => clearTimer(n.id))
  }

  return id
}

const startTimer = (notification: SmartNotificationItem) => {
  if (!notification.duration) return

  // 清除现有定时器
  clearTimer(notification.id)

  // 设置关闭定时器
  const timer = setTimeout(() => {
    closeNotification(notification.id)
  }, notification.duration)
  
  timers.value.set(notification.id, timer)

  // 设置进度条定时器
  if (notification.showProgress) {
    startProgressTimer(notification.id, notification.duration)
  }
}

const startProgressTimer = (id: string, duration: number) => {
  progressValues.value.set(id, 100)
  
  const interval = 50 // 50ms更新一次
  const step = (100 * interval) / duration
  
  const progressTimer = setInterval(() => {
    const current = progressValues.value.get(id) || 0
    const newValue = Math.max(0, current - step)
    progressValues.value.set(id, newValue)
    
    if (newValue <= 0) {
      clearInterval(progressTimer)
      progressTimers.value.delete(id)
    }
  }, interval)
  
  progressTimers.value.set(id, progressTimer)
}

const pauseTimer = (id: string) => {
  const timer = timers.value.get(id)
  if (timer) {
    clearTimeout(timer)
    timers.value.delete(id)
  }
  
  const progressTimer = progressTimers.value.get(id)
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimers.value.delete(id)
  }
}

const resumeTimer = (id: string) => {
  const notification = notifications.value.find(n => n.id === id)
  if (notification && !notification.persistent) {
    const remainingTime = (progressValues.value.get(id) || 0) * (notification.duration || 4000) / 100
    
    if (remainingTime > 0) {
      const timer = setTimeout(() => {
        closeNotification(id)
      }, remainingTime)
      
      timers.value.set(id, timer)
      
      if (notification.showProgress) {
        startProgressTimer(id, remainingTime)
      }
    }
  }
}

const closeNotification = (id: string) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
  clearTimer(id)
}

const clearTimer = (id: string) => {
  const timer = timers.value.get(id)
  if (timer) {
    clearTimeout(timer)
    timers.value.delete(id)
  }
  
  const progressTimer = progressTimers.value.get(id)
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimers.value.delete(id)
  }
  
  progressValues.value.delete(id)
}

const clearAll = () => {
  notifications.value.forEach(n => clearTimer(n.id))
  notifications.value = []
}

const handleAction = async (notificationId: string, action: NotificationAction) => {
  try {
    await action.handler()
    closeNotification(notificationId)
  } catch (error) {
    console.error('通知操作执行失败:', error)
    ElMessage.error('操作执行失败')
  }
}

const getNotificationIcon = (type: NotificationType) => {
  const icons = {
    success: SuccessFilled,
    warning: WarningFilled,
    error: CircleCloseFilled,
    info: InfoFilled,
    loading: Bell
  }
  return icons[type] || QuestionFilled
}

const getProgressWidth = (id: string) => {
  return progressValues.value.get(id) || 0
}

const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 便捷方法
const success = (message: string, options?: Partial<SmartNotificationItem>) => {
  return addNotification({ type: 'success', message, ...options })
}

const warning = (message: string, options?: Partial<SmartNotificationItem>) => {
  return addNotification({ type: 'warning', message, ...options })
}

const error = (message: string, options?: Partial<SmartNotificationItem>) => {
  return addNotification({ 
    type: 'error', 
    message, 
    persistent: true,
    ...options 
  })
}

const info = (message: string, options?: Partial<SmartNotificationItem>) => {
  return addNotification({ type: 'info', message, ...options })
}

const loading = (message: string, options?: Partial<SmartNotificationItem>) => {
  return addNotification({ 
    type: 'loading', 
    message, 
    persistent: true,
    showProgress: false,
    ...options 
  })
}

// 暴露方法
defineExpose({
  addNotification,
  closeNotification,
  clearAll,
  success,
  warning,
  error,
  info,
  loading
})

// 生命周期
onUnmounted(() => {
  clearAll()
})
</script>

<style scoped>
.smart-notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 400px;
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.smart-notification {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.smart-notification:hover {
  transform: translateX(-4px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

.notification-success {
  border-left-color: #52c41a;
}

.notification-warning {
  border-left-color: #faad14;
}

.notification-error {
  border-left-color: #ff4d4f;
}

.notification-info {
  border-left-color: #1890ff;
}

.notification-loading {
  border-left-color: #722ed1;
}

.notification-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-success .notification-icon {
  color: #52c41a;
}

.notification-warning .notification-icon {
  color: #faad14;
}

.notification-error .notification-icon {
  color: #ff4d4f;
}

.notification-info .notification-icon {
  color: #1890ff;
}

.notification-loading .notification-icon {
  color: #722ed1;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  font-size: 14px;
}

.notification-message {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  word-break: break-word;
}

.notification-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

.notification-close {
  flex-shrink: 0;
  cursor: pointer;
  color: #999;
  transition: color 0.2s;
  padding: 2px;
}

.notification-close:hover {
  color: #666;
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: currentColor;
  transition: width 0.1s linear;
  opacity: 0.6;
}

.global-actions {
  margin-top: 12px;
  text-align: center;
}

/* 动画 */
.notification-enter-active {
  transition: all 0.3s ease;
}

.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .smart-notification-container {
    left: 20px;
    right: 20px;
    max-width: none;
  }
  
  .smart-notification {
    padding: 12px;
  }
  
  .notification-title {
    font-size: 13px;
  }
  
  .notification-message {
    font-size: 12px;
  }
}
</style>
