/**
 * 深度测试脚本 - 测试完整Vue应用功能
 */

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class DeepTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = [];
  }

  async init() {
    console.log('🚀 启动深度测试...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    this.page = await this.browser.newPage();
    
    // 监听控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`[${type.toUpperCase()}] ${text}`);
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      console.error('❌ 页面错误:', error.message);
    });
  }

  async takeScreenshot(name) {
    const filename = `deep-test-${name}-${Date.now()}.png`;
    const filepath = path.join(__dirname, 'test-screenshots', filename);
    
    const dir = path.dirname(filepath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    await this.page.screenshot({ path: filepath, fullPage: true });
    console.log(`📸 截图: ${filename}`);
    return filepath;
  }

  async loadFullApp() {
    console.log('\n🔄 加载完整Vue应用...');
    
    // 导航到首页
    await this.page.goto('http://localhost:5173', { waitUntil: 'networkidle0' });
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 截图初始状态
    await this.takeScreenshot('01-initial');
    
    // 点击"加载Vue应用"按钮
    const loadButton = await this.page.$('button');
    if (loadButton) {
      const buttonText = await loadButton.evaluate(el => el.textContent.trim());
      console.log(`点击按钮: ${buttonText}`);
      
      await loadButton.click();
      await new Promise(resolve => setTimeout(resolve, 5000)); // 等待应用加载
      
      // 截图加载后状态
      await this.takeScreenshot('02-after-load');
      
      return true;
    } else {
      console.log('❌ 未找到加载按钮');
      return false;
    }
  }

  async testNavigation() {
    console.log('\n🧭 测试导航系统...');
    
    // 查找导航元素
    const navSelectors = [
      '.navbar-nav',
      '.nav-menu',
      '.sidebar-nav',
      '.top-navbar',
      'nav',
      '.navigation',
      '.menu'
    ];
    
    let navElement = null;
    for (const selector of navSelectors) {
      navElement = await this.page.$(selector);
      if (navElement) {
        console.log(`✅ 找到导航元素: ${selector}`);
        break;
      }
    }
    
    if (!navElement) {
      console.log('❌ 未找到导航元素');
      return false;
    }
    
    // 获取所有导航链接
    const navLinks = await this.page.evaluate(() => {
      const links = Array.from(document.querySelectorAll('a[href], .nav-item, .menu-item'));
      return links.map(link => ({
        text: link.textContent.trim(),
        href: link.getAttribute('href') || link.getAttribute('data-route'),
        className: link.className,
        visible: link.offsetParent !== null
      })).filter(link => link.text && link.visible);
    });
    
    console.log(`找到 ${navLinks.length} 个导航项:`);
    navLinks.forEach((link, index) => {
      console.log(`  ${index + 1}. ${link.text} -> ${link.href || '无链接'}`);
    });
    
    // 测试每个导航项
    for (let i = 0; i < Math.min(navLinks.length, 8); i++) {
      const link = navLinks[i];
      await this.testNavigationItem(link, i + 1);
    }
    
    return true;
  }

  async testNavigationItem(link, index) {
    try {
      console.log(`\n📍 测试导航项 ${index}: ${link.text}`);
      
      // 尝试点击导航项
      const linkElement = await this.page.evaluateHandle((linkText) => {
        const elements = Array.from(document.querySelectorAll('a, .nav-item, .menu-item'));
        return elements.find(el => el.textContent.trim() === linkText);
      }, link.text);
      
      if (linkElement.asElement()) {
        await linkElement.asElement().click();
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 截图
        await this.takeScreenshot(`nav-${index}-${link.text.replace(/[^a-z0-9]/gi, '-')}`);
        
        // 检查页面变化
        const currentUrl = this.page.url();
        const pageTitle = await this.page.title();
        
        console.log(`  当前URL: ${currentUrl}`);
        console.log(`  页面标题: ${pageTitle}`);
        
        // 检查页面内容
        const pageContent = await this.page.evaluate(() => {
          const main = document.querySelector('main, .main-content, .content, #app > div');
          return {
            hasContent: main && main.children.length > 0,
            textLength: document.body.textContent.trim().length,
            hasError: document.body.textContent.toLowerCase().includes('error') ||
                     document.body.textContent.toLowerCase().includes('404') ||
                     document.body.textContent.includes('开发中') ||
                     document.body.textContent.includes('正在开发')
          };
        });
        
        console.log(`  内容长度: ${pageContent.textLength} 字符`);
        
        if (pageContent.hasError) {
          console.log('  ⚠️ 页面显示开发中或错误信息');
        }
        
        // 测试页面特定功能
        await this.testPageFeatures(link.text);
        
        this.testResults.push({
          navigation: link.text,
          url: currentUrl,
          title: pageTitle,
          contentLength: pageContent.textLength,
          hasError: pageContent.hasError,
          timestamp: new Date().toISOString()
        });
        
      } else {
        console.log('  ❌ 无法点击该导航项');
      }
      
    } catch (error) {
      console.log(`  ❌ 导航测试失败: ${error.message}`);
    }
  }

  async testPageFeatures(pageName) {
    console.log(`  🔧 测试 ${pageName} 页面功能...`);
    
    try {
      // 查找页面中的交互元素
      const interactiveElements = await this.page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button:not([disabled])'));
        const inputs = Array.from(document.querySelectorAll('input, textarea, select'));
        const tables = Array.from(document.querySelectorAll('table, .el-table'));
        const charts = Array.from(document.querySelectorAll('.chart, .echarts, canvas'));
        const forms = Array.from(document.querySelectorAll('form'));
        
        return {
          buttons: buttons.length,
          inputs: inputs.length,
          tables: tables.length,
          charts: charts.length,
          forms: forms.length
        };
      });
      
      console.log(`    按钮: ${interactiveElements.buttons}`);
      console.log(`    输入框: ${interactiveElements.inputs}`);
      console.log(`    表格: ${interactiveElements.tables}`);
      console.log(`    图表: ${interactiveElements.charts}`);
      console.log(`    表单: ${interactiveElements.forms}`);
      
      // 测试按钮功能
      if (interactiveElements.buttons > 0) {
        await this.testButtons();
      }
      
      // 测试输入框
      if (interactiveElements.inputs > 0) {
        await this.testInputs();
      }
      
      // 测试表格
      if (interactiveElements.tables > 0) {
        await this.testTables();
      }
      
    } catch (error) {
      console.log(`    ❌ 功能测试失败: ${error.message}`);
    }
  }

  async testButtons() {
    try {
      const buttons = await this.page.$$('button:not([disabled])');
      const testCount = Math.min(buttons.length, 3);
      
      console.log(`    测试 ${testCount} 个按钮...`);
      
      for (let i = 0; i < testCount; i++) {
        const buttonText = await buttons[i].evaluate(el => el.textContent.trim());
        console.log(`      点击: ${buttonText}`);
        
        await buttons[i].click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 检查是否有弹窗或变化
        const hasModal = await this.page.$('.el-dialog, .modal, .popup');
        if (hasModal) {
          console.log(`        ✅ 触发了弹窗`);
          
          // 尝试关闭弹窗
          const closeBtn = await this.page.$('.el-dialog__close, .modal-close, .close');
          if (closeBtn) {
            await closeBtn.click();
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
      }
    } catch (error) {
      console.log(`      ❌ 按钮测试失败: ${error.message}`);
    }
  }

  async testInputs() {
    try {
      const inputs = await this.page.$$('input:not([disabled]), textarea:not([disabled])');
      const testCount = Math.min(inputs.length, 2);
      
      console.log(`    测试 ${testCount} 个输入框...`);
      
      for (let i = 0; i < testCount; i++) {
        const placeholder = await inputs[i].evaluate(el => el.placeholder || el.getAttribute('aria-label') || '输入框');
        console.log(`      输入: ${placeholder}`);
        
        await inputs[i].click();
        await inputs[i].clear();
        await inputs[i].type('测试数据123');
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const value = await inputs[i].evaluate(el => el.value);
        console.log(`        值: ${value}`);
      }
    } catch (error) {
      console.log(`      ❌ 输入框测试失败: ${error.message}`);
    }
  }

  async testTables() {
    try {
      const tableData = await this.page.evaluate(() => {
        const tables = Array.from(document.querySelectorAll('table, .el-table'));
        return tables.map(table => {
          const rows = table.querySelectorAll('tr, .el-table__row');
          const headers = table.querySelectorAll('th, .el-table__header-wrapper th');
          return {
            rows: rows.length,
            columns: headers.length,
            hasData: rows.length > 1
          };
        });
      });
      
      console.log(`    表格数据:`, tableData);
      
    } catch (error) {
      console.log(`      ❌ 表格测试失败: ${error.message}`);
    }
  }

  async generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      testResults: this.testResults,
      summary: {
        totalPages: this.testResults.length,
        pagesWithErrors: this.testResults.filter(r => r.hasError).length,
        avgContentLength: this.testResults.reduce((sum, r) => sum + r.contentLength, 0) / this.testResults.length
      }
    };
    
    const reportPath = path.join(__dirname, `deep-test-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 深度测试报告:');
    console.log(`测试页面数: ${report.summary.totalPages}`);
    console.log(`有问题页面: ${report.summary.pagesWithErrors}`);
    console.log(`平均内容长度: ${Math.round(report.summary.avgContentLength)} 字符`);
    console.log(`详细报告: ${reportPath}`);
    
    return report;
  }

  async runTest() {
    try {
      await this.init();
      
      // 1. 加载完整应用
      const loaded = await this.loadFullApp();
      
      if (loaded) {
        // 2. 测试导航
        await this.testNavigation();
        
        // 3. 生成报告
        await this.generateReport();
      }
      
      console.log('\n🎉 深度测试完成！');
      
    } catch (error) {
      console.error('❌ 深度测试失败:', error);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// 运行测试
const tester = new DeepTester();
tester.runTest().catch(console.error);
