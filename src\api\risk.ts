import request from './request'
import type { RiskMetricsData, RiskAlertsData, RiskConfig } from '@/types/risk'

// 获取风险指标
export const getRiskMetrics = async () => {
  const response = await request.get<RiskMetricsData>('/api/v1/risk/metrics')
  return response.data
}

// 获取风险警报
export const getRiskAlerts = async (params?: {
  status?: string
  level?: string
  limit?: number
}) => {
  const response = await request.get<RiskAlertsData>('/api/v1/risk/alerts', { params })
  return response.data
}

// 获取风险分布
export const getRiskDistribution = async () => {
  const response = await request.get('/api/v1/risk/distribution')
  return response.data
}

// 获取风险限额
export const getRiskLimits = async () => {
  const response = await request.get('/api/v1/risk/limits')
  return response.data
}

// 运行压力测试
export const runStressTest = async (scenarios?: string[]) => {
  const response = await request.post('/api/v1/risk/stress-test', { scenarios })
  return response.data
}

// 获取压力测试结果
export const getStressTestResults = async (testId?: string) => {
  const url = testId ? `/api/v1/risk/stress-test/${testId}` : '/api/v1/risk/stress-test/latest'
  const response = await request.get(url)
  return response.data
}

// 获取风险配置
export const getRiskConfig = async () => {
  const response = await request.get<RiskConfig>('/api/v1/risk/config')
  return response.data
}

// 更新风险配置
export const updateRiskConfig = async (config: Partial<RiskConfig>) => {
  const response = await request.put('/api/v1/risk/config', config)
  return response.data
}

// 导出风险报告
export const exportRiskReport = async (params: {
  format: 'pdf' | 'excel' | 'csv'
  dateRange?: {
    startDate: string
    endDate: string
  }
}) => {
  const response = await request.post('/api/v1/risk/report/export', params, {
    responseType: 'blob'
  })
  return response.data
}

// 清除风险警报
export const clearRiskAlerts = async (alertIds?: number[]) => {
  const response = await request.post('/api/v1/risk/alerts/clear', { alertIds })
  return response.data
}

// 标记警报为已处理
export const handleRiskAlert = async (alertId: number, action: string) => {
  const response = await request.post(`/api/v1/risk/alerts/${alertId}/handle`, { action })
  return response.data
}

// 获取历史风险数据
export const getRiskHistory = async (params: {
  metric: string
  period: string
  interval?: string
}) => {
  const response = await request.get('/api/v1/risk/history', { params })
  return response.data
}

// 获取风险热力图数据
export const getRiskHeatmap = async () => {
  const response = await request.get('/api/v1/risk/heatmap')
  return response.data
}

// 订阅风险实时更新
export const subscribeRiskUpdates = (callback: (data: any) => void) => {
  // WebSocket订阅逻辑
  const ws = new WebSocket(`${import.meta.env.VITE_WS_BASE_URL}/risk`)
  
  ws.onmessage = (event) => {
    const data = JSON.parse(event.data)
    callback(data)
  }
  
  ws.onerror = (error) => {
    console.error('Risk WebSocket error:', error)
  }
  
  return () => {
    ws.close()
  }
}