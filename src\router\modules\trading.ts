import type { RouteRecordRaw } from 'vue-router'

const tradingRoutes: RouteRecordRaw[] = [
  // 主路由重定向到交易中心
  {
    path: 'trading',
    redirect: 'trading/center'
  },

  // 交易中心 - 统一的交易管理界面
  {
    path: 'trading/center',
    name: 'TradingCenter',
    component: () => import('@/views/Trading/TradingCenterSimple.vue'),
    meta: {
      title: '交易中心',
      requiresAuth: false,
      icon: 'Monitor',
      description: '统一的交易管理中心，包含交易终端、账户管理和数据中心'
    }
  },

  // 交易终端 - 独立的交易界面
  {
    path: 'trading/terminal',
    name: 'TradingTerminal',
    component: () => import('@/views/Trading/TradingTerminal.vue'),
    meta: {
      title: '交易终端',
      requiresAuth: true,
      icon: 'Monitor',
      description: '专业交易终端界面'
    }
  },

  // 模拟交易 - 模拟交易专用界面
  {
    path: 'trading/simulated',
    name: 'SimulatedTrading',
    component: () => import('@/views/Trading/SimulatedTrading.vue'),
    meta: {
      title: '模拟交易',
      requiresAuth: false,
      icon: 'School',
      description: '模拟交易练习环境'
    }
  },

  // 实盘交易 - 专业实盘交易
  {
    path: 'trading/miniqmt',
    name: 'MiniQMTTrading',
    component: () => import('@/views/Trading/MiniQMTTrading.vue'),
    meta: {
      title: '实盘交易',
      requiresAuth: true,
      icon: 'Coin',
      description: '专业实盘交易终端，支持高频和普通交易模式'
    }
  },

  // 订单管理 - 订单查询和管理
  {
    path: 'trading/orders',
    name: 'OrderManagement',
    component: () => import('@/views/Trading/OrderManagement.vue'),
    meta: {
      title: '订单管理',
      requiresAuth: true,
      icon: 'Document',
      description: '查看和管理所有交易订单'
    }
  },

  // 持仓管理 - 持仓查询和管理
  {
    path: 'trading/positions',
    name: 'PositionManagement',
    component: () => import('@/views/Trading/PositionManagement.vue'),
    meta: {
      title: '持仓管理',
      requiresAuth: true,
      icon: 'TrendCharts',
      description: '查看和管理投资组合持仓'
    }
  },

  // 图表分析 - K线图和技术指标
  {
    path: 'trading/charts',
    name: 'TradingCharts',
    component: () => import('@/views/Trading/TradingCharts.vue'),
    meta: {
      title: '图表分析',
      requiresAuth: true,
      icon: 'TrendCharts',
      description: 'K线图表和技术指标分析'
    }
  },

  // 算法交易 - 条件单和自动交易
  {
    path: 'trading/algorithm',
    name: 'AlgorithmTrading',
    component: () => import('@/views/Trading/AlgorithmTrading.vue'),
    meta: {
      title: '算法交易',
      requiresAuth: true,
      icon: 'Setting',
      description: '条件单、止损止盈等算法交易功能'
    }
  },

  // 数据导出 - 报表和数据导出
  {
    path: 'trading/export',
    name: 'DataExport',
    component: () => import('@/views/Trading/DataExport.vue'),
    meta: {
      title: '数据导出',
      requiresAuth: true,
      icon: 'Download',
      description: 'Excel、PDF报表导出功能'
    }
  }
]

export default tradingRoutes
