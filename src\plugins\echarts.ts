/**
 * ECharts 配置
 */
import * as echarts from 'echarts/core'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CandlestickChart,
  HeatmapChart,
  TreemapChart,
  GaugeChart
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  MarkPointComponent,
  MarkLineComponent,
  ToolboxComponent,
  BrushComponent,
  VisualMapComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

export const setupECharts = () => {
  // 注册必要的组件
  echarts.use([
    // 图表类型
    Bar<PERSON>hart,
    LineChart,
    PieChart,
    Scatter<PERSON>hart,
    Candlestick<PERSON>hart,
    HeatmapChart,
    TreemapChart,
    GaugeChart,
    
    // 组件
    TitleComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent,
    DataZoomComponent,
    MarkPoint<PERSON>omponent,
    <PERSON><PERSON><PERSON><PERSON>omponent,
    <PERSON>l<PERSON>Component,
    <PERSON>rushComponent,
    VisualMapComponent,
    
    // 渲染器
    CanvasRenderer
  ])
}

export { echarts }