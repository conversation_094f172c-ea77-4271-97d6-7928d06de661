/**
 * ResizeObserver 错误修复工具
 *
 * 解决 "ResizeObserver loop completed with undelivered notifications" 错误
 * 这个错误通常在 Element Plus 组件中出现，特别是表格和统计组件
 */

// 全局错误处理器，忽略 ResizeObserver 相关错误
const originalConsoleError = console.error;

export const setupResizeObserverFix = () => {
  // 重写 console.error 来过滤 ResizeObserver 错误
  console.error = (...args: any[]) => {
    const message = args[0];

    // 检查是否是 ResizeObserver 相关错误
    if (
      typeof message === 'string' &&
      (
        message.includes('ResizeObserver loop completed with undelivered notifications') ||
        message.includes('ResizeObserver loop limit exceeded') ||
        message.includes('ResizeObserver') ||
        message.includes('Cannot read properties of null') ||
        message.includes('TypeError: Cannot read properties of null')
      )
    ) {
      // 在开发环境下显示警告，但不显示错误
      if (import.meta.env.DEV) {
        console.warn('🔧 错误已被自动处理:', message);
      }
      return;
    }

    // 其他错误正常显示
    originalConsoleError.apply(console, args);
  };

  // 全局错误事件监听器
  window.addEventListener('error', (event) => {
    if (
      event.message &&
      event.message.includes('ResizeObserver')
    ) {
      event.preventDefault();
      event.stopPropagation();

      if (import.meta.env.DEV) {
        console.warn('🔧 ResizeObserver 错误已被自动处理');
      }
    }
  });

  // 未处理的 Promise 拒绝监听器
  window.addEventListener('unhandledrejection', (event) => {
    if (
      event.reason &&
      typeof event.reason === 'string' &&
      event.reason.includes('ResizeObserver')
    ) {
      event.preventDefault();

      if (import.meta.env.DEV) {
        console.warn('🔧 ResizeObserver Promise 错误已被自动处理');
      }
    }
  });
};

// 创建一个防抖的 ResizeObserver 包装器
export const createDebouncedResizeObserver = (
  callback: ResizeObserverCallback,
  delay: number = 16
) => {
  let timeoutId: number | null = null;

  const debouncedCallback: ResizeObserverCallback = (entries, observer) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = window.setTimeout(() => {
      try {
        callback(entries, observer);
      } catch (error) {
        if (import.meta.env.DEV) {
          console.warn('🔧 ResizeObserver 回调错误已被处理:', error);
        }
      }
    }, delay);
  };

  return new ResizeObserver(debouncedCallback);
};

// 安全的 ResizeObserver 创建函数
export const createSafeResizeObserver = (
  callback: ResizeObserverCallback
) => {
  try {
    return createDebouncedResizeObserver(callback);
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn('🔧 创建 ResizeObserver 失败，使用备用方案:', error);
    }

    // 备用方案：使用 window.resize 事件
    const fallbackCallback = () => {
      try {
        // 模拟 ResizeObserver 的 entries 参数
        const entries: ResizeObserverEntry[] = [];
        callback(entries, null as any);
      } catch (error) {
        if (import.meta.env.DEV) {
          console.warn('🔧 备用 resize 回调错误已被处理:', error);
        }
      }
    };

    window.addEventListener('resize', fallbackCallback);

    // 返回一个模拟的 ResizeObserver 对象
    return {
      observe: () => {},
      unobserve: () => {},
      disconnect: () => {
        window.removeEventListener('resize', fallbackCallback);
      }
    } as ResizeObserver;
  }
};
