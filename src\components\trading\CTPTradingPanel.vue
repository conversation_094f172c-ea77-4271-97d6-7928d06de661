<template>
  <div class="ctp-trading-panel">
    <!-- CTP连接状态 -->
    <div class="ctp-status-bar">
      <div class="status-info">
        <el-tag 
          :type="ctpStatus.connected ? 'success' : 'danger'"
          effect="dark"
          size="small"
        >
          {{ ctpStatus.connected ? '已连接' : '未连接' }}
        </el-tag>
        <span class="broker-info">
          {{ ctpStatus.broker_id }} - {{ ctpStatus.user_id }}
        </span>
        <el-tag 
          v-if="ctpStatus.simulation_mode"
          type="warning"
          effect="plain"
          size="small"
        >
          仿真模式
        </el-tag>
      </div>
      <div class="status-actions">
        <el-button 
          v-if="!ctpStatus.connected"
          type="primary" 
          size="small"
          @click="initializeCTP"
          :loading="initializing"
        >
          连接CTP
        </el-button>
        <el-button 
          size="small"
          @click="refreshStatus"
          :loading="refreshing"
        >
          刷新状态
        </el-button>
      </div>
    </div>

    <!-- 交易表单 -->
    <el-card class="trading-form-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>CTP交易下单</span>
          <el-switch
            v-model="enableCTPTrading"
            active-text="启用"
            inactive-text="禁用"
          />
        </div>
      </template>

      <el-form 
        :model="orderForm" 
        :rules="orderRules"
        ref="orderFormRef"
        label-width="80px"
        :disabled="!enableCTPTrading || !ctpStatus.connected"
      >
        <el-form-item label="合约代码" prop="symbol">
          <el-select
            v-model="orderForm.symbol"
            placeholder="选择合约"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="instrument in instruments"
              :key="instrument.symbol"
              :label="`${instrument.symbol} - ${instrument.name}`"
              :value="instrument.symbol"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="买卖方向" prop="direction">
          <el-radio-group v-model="orderForm.direction">
            <el-radio-button label="BUY">买入</el-radio-button>
            <el-radio-button label="SELL">卖出</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="订单类型" prop="order_type">
          <el-radio-group v-model="orderForm.order_type">
            <el-radio-button label="LIMIT">限价单</el-radio-button>
            <el-radio-button label="MARKET">市价单</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item 
          label="价格" 
          prop="price"
          v-if="orderForm.order_type === 'LIMIT'"
        >
          <el-input-number
            v-model="orderForm.price"
            :precision="2"
            :step="0.01"
            :min="0.01"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="数量" prop="volume">
          <el-input-number
            v-model="orderForm.volume"
            :min="1"
            :step="1"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="订单金额" v-if="orderAmount > 0">
          <div class="order-amount">
            <span class="amount-value">{{ formatAmount(orderAmount) }}</span>
            <span class="amount-label">预估金额</span>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button 
            type="primary" 
            @click="submitOrder"
            :loading="submitting"
            :disabled="!canSubmitOrder"
            style="width: 100%"
          >
            {{ orderForm.direction === 'BUY' ? '买入' : '卖出' }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 账户信息 -->
    <el-card class="account-card" shadow="never" v-if="account">
      <template #header>
        <span>账户信息</span>
      </template>
      <div class="account-info">
        <div class="account-item">
          <span class="label">总资产:</span>
          <span class="value">{{ formatAmount(account.total_asset) }}</span>
        </div>
        <div class="account-item">
          <span class="label">可用资金:</span>
          <span class="value">{{ formatAmount(account.available) }}</span>
        </div>
        <div class="account-item">
          <span class="label">持仓市值:</span>
          <span class="value">{{ formatAmount(account.balance - account.available) }}</span>
        </div>
        <div class="account-item">
          <span class="label">浮动盈亏:</span>
          <span class="value" :class="account.pnl >= 0 ? 'profit' : 'loss'">
            {{ formatAmount(account.pnl) }}
          </span>
        </div>
      </div>
    </el-card>

    <!-- 当日订单 -->
    <el-card class="orders-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>当日订单</span>
          <el-button size="small" @click="refreshOrders" :loading="loadingOrders">
            刷新
          </el-button>
        </div>
      </template>
      
      <el-table 
        :data="orders" 
        size="small"
        :loading="loadingOrders"
        empty-text="暂无订单"
      >
        <el-table-column prop="order_id" label="订单号" width="120" />
        <el-table-column prop="symbol" label="合约" width="80" />
        <el-table-column prop="direction" label="方向" width="60">
          <template #default="{ row }">
            <el-tag 
              :type="row.direction === 'BUY' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.direction === 'BUY' ? '买' : '卖' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格" width="80">
          <template #default="{ row }">
            {{ row.price ? formatPrice(row.price) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="volume" label="数量" width="60" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag 
              :type="getOrderStatusType(row.status)"
              size="small"
            >
              {{ getOrderStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="{ row }">
            <el-button
              v-if="canCancelOrder(row.status)"
              size="small"
              type="danger"
              @click="cancelOrder(row.order_id)"
              :loading="cancellingOrders.has(row.order_id)"
            >
              撤单
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ctpTradingService, { 
  type CTOrderRequest, 
  type CTOrder, 
  type CTAccount, 
  type CTStatus 
} from '@/services/ctpTradingService'

// 响应式数据
const initializing = ref(false)
const refreshing = ref(false)
const submitting = ref(false)
const loadingOrders = ref(false)
const enableCTPTrading = ref(false)
const cancellingOrders = ref(new Set<string>())

// 表单引用
const orderFormRef = ref()

// CTP状态
const ctpStatus = ref<CTStatus>({
  connected: false,
  logged_in: false,
  broker_id: '',
  user_id: '',
  simulation_mode: true
})

// 账户信息
const account = ref<CTAccount | null>(null)

// 订单列表
const orders = ref<CTOrder[]>([])

// 交易表单
const orderForm = reactive<CTOrderRequest>({
  symbol: '',
  direction: 'BUY',
  order_type: 'LIMIT',
  volume: 1,
  price: 0
})

// 可交易合约列表
const instruments = ref([
  { symbol: 'rb2501', name: '螺纹钢2501' },
  { symbol: 'cu2501', name: '沪铜2501' },
  { symbol: 'au2502', name: '沪金2502' },
  { symbol: 'ag2502', name: '沪银2502' },
  { symbol: 'IF2501', name: '沪深300股指2501' },
  { symbol: 'IC2501', name: '中证500股指2501' },
  { symbol: 'c2501', name: '玉米2501' },
  { symbol: 'm2501', name: '豆粕2501' }
])

// 表单验证规则
const orderRules = {
  symbol: [
    { required: true, message: '请选择合约', trigger: 'change' }
  ],
  direction: [
    { required: true, message: '请选择买卖方向', trigger: 'change' }
  ],
  volume: [
    { required: true, message: '请输入交易数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ],
  price: [
    { 
      required: true, 
      message: '请输入价格', 
      trigger: 'blur',
      validator: (rule: any, value: any, callback: any) => {
        if (orderForm.order_type === 'LIMIT' && (!value || value <= 0)) {
          callback(new Error('限价单请输入有效价格'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 计算属性
const orderAmount = computed(() => {
  if (orderForm.order_type === 'LIMIT' && orderForm.price && orderForm.volume) {
    return orderForm.price * orderForm.volume
  }
  return 0
})

const canSubmitOrder = computed(() => {
  return enableCTPTrading.value && 
         ctpStatus.value.connected && 
         orderForm.symbol && 
         orderForm.direction && 
         orderForm.volume > 0 &&
         (orderForm.order_type === 'MARKET' || (orderForm.price && orderForm.price > 0))
})

// 方法
const initializeCTP = async () => {
  initializing.value = true
  try {
    const success = await ctpTradingService.initialize()
    if (success) {
      ElMessage.success('CTP连接成功')
      await refreshStatus()
      enableCTPTrading.value = true
    } else {
      ElMessage.error('CTP连接失败')
    }
  } catch (error) {
    ElMessage.error('CTP连接异常')
    console.error('CTP初始化失败:', error)
  } finally {
    initializing.value = false
  }
}

const refreshStatus = async () => {
  refreshing.value = true
  try {
    const status = await ctpTradingService.getStatus()
    ctpStatus.value = status
    
    if (status.connected) {
      await Promise.all([
        refreshAccount(),
        refreshOrders()
      ])
    }
  } catch (error) {
    console.error('刷新状态失败:', error)
  } finally {
    refreshing.value = false
  }
}

const refreshAccount = async () => {
  try {
    account.value = await ctpTradingService.queryAccount()
  } catch (error) {
    console.error('查询账户失败:', error)
  }
}

const refreshOrders = async () => {
  loadingOrders.value = true
  try {
    const result = await ctpTradingService.queryOrders()
    orders.value = Array.isArray(result) ? result : []
  } catch (error) {
    console.error('查询订单失败:', error)
    orders.value = []
  } finally {
    loadingOrders.value = false
  }
}

const submitOrder = async () => {
  if (!orderFormRef.value) return
  
  try {
    await orderFormRef.value.validate()
    
    submitting.value = true
    
    const response = await ctpTradingService.submitOrder(orderForm)
    
    if (response.success) {
      ElMessage.success('订单提交成功')
      // 重置表单
      orderForm.volume = 1
      orderForm.price = 0
      // 刷新数据
      await Promise.all([
        refreshAccount(),
        refreshOrders()
      ])
    } else {
      ElMessage.error(response.message || '订单提交失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '订单提交异常')
    console.error('提交订单失败:', error)
  } finally {
    submitting.value = false
  }
}

const cancelOrder = async (orderId: string) => {
  try {
    await ElMessageBox.confirm('确定要撤销此订单吗？', '确认撤单', {
      type: 'warning'
    })
    
    cancellingOrders.value.add(orderId)
    
    const response = await ctpTradingService.cancelOrder(orderId)
    
    if (response.success) {
      ElMessage.success('撤单成功')
      await refreshOrders()
    } else {
      ElMessage.error(response.message || '撤单失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '撤单异常')
      console.error('撤单失败:', error)
    }
  } finally {
    cancellingOrders.value.delete(orderId)
  }
}

const canCancelOrder = (status: string): boolean => {
  return ['SUBMITTED', 'PENDING'].includes(status)
}

const getOrderStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    'PENDING': 'info',
    'SUBMITTED': 'warning',
    'PARTIAL_FILLED': 'primary',
    'FILLED': 'success',
    'CANCELLED': 'info',
    'REJECTED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'PENDING': '待报',
    'SUBMITTED': '已报',
    'PARTIAL_FILLED': '部成',
    'FILLED': '全成',
    'CANCELLED': '已撤',
    'REJECTED': '拒绝'
  }
  return statusMap[status] || status
}

const formatPrice = (price: number): string => {
  return ctpTradingService.formatPrice(price)
}

const formatAmount = (amount: number): string => {
  return ctpTradingService.formatAmount(amount)
}

// 生命周期
onMounted(async () => {
  await refreshStatus()
  
  // 建立WebSocket连接
  try {
    await ctpTradingService.connectWebSocket()
    
    // 注册WebSocket事件
    ctpTradingService.onWebSocketMessage('ctp_order_update', (data: any) => {
      // 更新订单列表
      if (data && data.order_id) {
        const ordersArray = Array.isArray(orders.value) ? orders.value : []
        const index = ordersArray.findIndex(order => order.order_id === data.order_id)
        if (index > -1) {
          orders.value[index] = data
        } else {
          orders.value.unshift(data)
        }
      }
    })
    
    ctpTradingService.onWebSocketMessage('ctp_account_update', (data: any) => {
      account.value = data
    })
    
  } catch (error) {
    console.error('WebSocket连接失败:', error)
  }
})

onUnmounted(() => {
  ctpTradingService.disconnectWebSocket()
})
</script>

<style scoped>
.ctp-trading-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.ctp-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #e6e6e6;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.broker-info {
  font-size: 14px;
  color: #666;
}

.status-actions {
  display: flex;
  gap: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trading-form-card {
  flex-shrink: 0;
}

.order-amount {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-value {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
}

.amount-label {
  font-size: 12px;
  color: #999;
}

.account-card {
  flex-shrink: 0;
}

.account-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.account-item .label {
  color: #666;
  font-size: 14px;
}

.account-item .value {
  font-weight: 600;
  font-size: 14px;
}

.account-item .value.profit {
  color: #67c23a;
}

.account-item .value.loss {
  color: #f56c6c;
}

.orders-card {
  flex: 1;
  min-height: 0;
}

.orders-card :deep(.el-card__body) {
  height: calc(100% - 57px);
  padding: 0;
}

.orders-card :deep(.el-table) {
  height: 100%;
}
</style>
