<template>
  <div class="stock-list">
    <!-- 列表头部 -->
    <div class="list-header">
      <div class="header-left">
        <h3>股票列表</h3>
        <span class="stock-count">共 {{ totalCount }} 只股票</span>
      </div>
      <div class="header-right">
        <el-button-group>
          <el-button 
            :type="viewMode === 'table' ? 'primary' : 'default'"
            @click="viewMode = 'table'"
            size="small"
          >
            <el-icon><List /></el-icon>
            列表
          </el-button>
          <el-button 
            :type="viewMode === 'card' ? 'primary' : 'default'"
            @click="viewMode = 'card'"
            size="small"
          >
            <el-icon><Grid /></el-icon>
            卡片
          </el-button>
        </el-button-group>
        <el-button @click="refreshData" size="small">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 表格视图 -->
    <div v-if="viewMode === 'table'" class="table-view">
      <el-table 
        :data="displayStocks" 
        :loading="loading"
        @sort-change="handleSortChange"
        @row-click="handleRowClick"
        stripe
        height="600"
      >
        <el-table-column prop="symbol" label="代码" width="100" fixed="left">
          <template #default="{ row }">
            <span class="stock-symbol">{{ row.symbol }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="name" label="名称" width="120" fixed="left">
          <template #default="{ row }">
            <span class="stock-name">{{ row.name }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="currentPrice" label="现价" width="100" sortable="custom">
          <template #default="{ row }">
            <span class="price" :class="getPriceClass(row.change)">
              {{ formatPrice(row.currentPrice) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="change" label="涨跌" width="100" sortable="custom">
          <template #default="{ row }">
            <span class="change" :class="getPriceClass(row.change)">
              {{ formatChange(row.change) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="changePercent" label="涨跌幅" width="100" sortable="custom">
          <template #default="{ row }">
            <span class="change-percent" :class="getPriceClass(row.change)">
              {{ formatPercent(row.changePercent) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="volume" label="成交量" width="120" sortable="custom">
          <template #default="{ row }">
            <span class="volume">{{ formatVolume(row.volume) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="turnover" label="成交额" width="120" sortable="custom">
          <template #default="{ row }">
            <span class="turnover">{{ formatAmount(row.turnover) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="high" label="最高" width="100">
          <template #default="{ row }">
            <span class="high">{{ formatPrice(row.high) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="low" label="最低" width="100">
          <template #default="{ row }">
            <span class="low">{{ formatPrice(row.low) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="openPrice" label="今开" width="100">
          <template #default="{ row }">
            <span class="open">{{ formatPrice(row.openPrice) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="previousClose" label="昨收" width="100">
          <template #default="{ row }">
            <span class="prev-close">{{ formatPrice(row.previousClose) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              @click.stop="toggleWatchlist(row)"
              :type="isInWatchlist(row.symbol) ? 'danger' : 'primary'"
              size="small"
              text
            >
              <el-icon>
                <Star v-if="!isInWatchlist(row.symbol)" />
                <StarFilled v-else />
              </el-icon>
              {{ isInWatchlist(row.symbol) ? '取消' : '自选' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 卡片视图 -->
    <div v-else class="card-view">
      <el-row :gutter="16">
        <el-col 
          v-for="stock in displayStocks" 
          :key="stock.symbol" 
          :span="6"
          class="stock-card-col"
        >
          <StockCard 
            :stock="stock"
            :selected="selectedStock?.symbol === stock.symbol"
            @click="handleStockClick"
            @favorite="handleFavorite"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 50, 100, 200]"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { List, Grid, Refresh, Star, StarFilled } from '@element-plus/icons-vue'
import { formatPrice, formatChange, formatPercent, formatVolume, formatAmount } from '@/utils/formatters'
import StockCard from '../StockCard.vue'
import type { QuoteData } from '@/types/market'

interface Props {
  stocks: QuoteData[]
  loading?: boolean
  watchlist?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  stocks: () => [],
  loading: false,
  watchlist: () => []
})

const emit = defineEmits<{
  (e: 'stock-click', stock: QuoteData): void
  (e: 'refresh'): void
  (e: 'sort-change', sort: { prop: string; order: string }): void
  (e: 'watchlist-toggle', symbol: string, add: boolean): void
}>()

// 响应式数据
const viewMode = ref<'table' | 'card'>('table')
const currentPage = ref(1)
const pageSize = ref(50)
const selectedStock = ref<QuoteData | null>(null)
const sortConfig = ref({ prop: '', order: '' })

// 计算属性
const totalCount = computed(() => props.stocks.length)

const displayStocks = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return props.stocks.slice(start, end)
})

// 方法
const getPriceClass = (change: number) => {
  if (change > 0) return 'up'
  if (change < 0) return 'down'
  return 'neutral'
}

const isInWatchlist = (symbol: string) => {
  return props.watchlist.includes(symbol)
}

const handleRowClick = (row: QuoteData) => {
  selectedStock.value = row
  emit('stock-click', row)
}

const handleStockClick = (stock: QuoteData) => {
  selectedStock.value = stock
  emit('stock-click', stock)
}

const handleFavorite = (stock: QuoteData, favorited: boolean) => {
  emit('watchlist-toggle', stock.symbol, favorited)
}

const toggleWatchlist = (stock: QuoteData) => {
  const isCurrentlyInWatchlist = isInWatchlist(stock.symbol)
  emit('watchlist-toggle', stock.symbol, !isCurrentlyInWatchlist)
}

const refreshData = () => {
  emit('refresh')
}

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  sortConfig.value = { prop, order }
  emit('sort-change', { prop, order })
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 监听股票数据变化，重置分页
watch(() => props.stocks.length, () => {
  currentPage.value = 1
})
</script>

<style scoped>
.stock-list {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stock-count {
  font-size: 14px;
  color: #909399;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-view {
  overflow: hidden;
}

.stock-symbol {
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.stock-name {
  font-weight: 500;
}

.price, .change, .change-percent {
  font-weight: 600;
}

.volume, .turnover {
  font-family: 'Courier New', monospace;
}

.card-view {
  padding: 20px;
}

.stock-card-col {
  margin-bottom: 16px;
}

.pagination-wrapper {
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;
  background: #fafafa;
  display: flex;
  justify-content: center;
}

/* 价格颜色 */
.up {
  color: #f56c6c;
}

.down {
  color: #67c23a;
}

.neutral {
  color: #909399;
}

/* 表格行悬停效果 */
:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
