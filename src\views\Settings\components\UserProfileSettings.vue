<template>
  <div class="user-profile-settings">
    <div class="settings-section">
      <h3 class="section-title">
        <el-icon><User /></el-icon>
        个人资料
      </h3>
      <p class="section-description">管理您的个人信息和账户详情</p>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="profile-form"
      >
        <!-- 头像上传 -->
        <el-form-item label="头像">
          <div class="avatar-upload">
            <el-avatar :size="80" :src="form.avatar" class="avatar">
              <el-icon v-if="!form.avatar"><User /></el-icon>
            </el-avatar>
            <div class="upload-actions">
              <el-button size="small" @click="handleAvatarUpload">
                <el-icon><Upload /></el-icon>
                上传头像
              </el-button>
              <el-button size="small" @click="handleAvatarRemove" v-if="form.avatar">
                移除
              </el-button>
            </div>
          </div>
        </el-form-item>

        <!-- 基本信息 -->
        <el-form-item label="用户名" prop="username">
          <el-input
            id="profile-username-input"
            v-model="form.username"
            placeholder="请输入用户名"
            aria-label="用户名输入框"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input
            id="profile-email-input"
            v-model="form.email"
            placeholder="请输入邮箱地址"
            aria-label="邮箱地址输入框"
          />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input
            id="profile-phone-input"
            v-model="form.phone"
            placeholder="请输入手机号"
            aria-label="手机号输入框"
          />
        </el-form-item>

        <el-form-item label="真实姓名" prop="realName">
          <el-input
            id="profile-realname-input"
            v-model="form.realName"
            placeholder="请输入真实姓名"
            aria-label="真实姓名输入框"
          />
        </el-form-item>

        <el-form-item label="性别" prop="gender">
          <el-radio-group
            id="profile-gender-group"
            v-model="form.gender"
            aria-label="性别选择"
          >
            <el-radio label="male">男</el-radio>
            <el-radio label="female">女</el-radio>
            <el-radio label="other">其他</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="生日" prop="birthday">
          <el-date-picker
            id="profile-birthday-picker"
            v-model="form.birthday"
            type="date"
            placeholder="请选择生日"
            style="width: 100%"
            aria-label="生日选择器"
          />
        </el-form-item>

        <el-form-item label="所在地区" prop="location">
          <el-cascader
            v-model="form.location"
            :options="locationOptions"
            placeholder="请选择所在地区"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="个人简介" prop="bio">
          <el-input
            v-model="form.bio"
            type="textarea"
            :rows="4"
            placeholder="请输入个人简介"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <!-- 投资偏好 -->
        <el-divider content-position="left">投资偏好</el-divider>

        <el-form-item label="风险偏好" prop="riskPreference">
          <el-radio-group v-model="form.riskPreference">
            <el-radio label="conservative">保守型</el-radio>
            <el-radio label="moderate">稳健型</el-radio>
            <el-radio label="aggressive">激进型</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="投资经验" prop="investmentExperience">
          <el-select v-model="form.investmentExperience" placeholder="请选择投资经验">
            <el-option label="新手（1年以下）" value="beginner" />
            <el-option label="初级（1-3年）" value="junior" />
            <el-option label="中级（3-5年）" value="intermediate" />
            <el-option label="高级（5年以上）" value="senior" />
          </el-select>
        </el-form-item>

        <el-form-item label="关注市场" prop="interestedMarkets">
          <el-checkbox-group v-model="form.interestedMarkets">
            <el-checkbox label="A股">A股</el-checkbox>
            <el-checkbox label="港股">港股</el-checkbox>
            <el-checkbox label="美股">美股</el-checkbox>
            <el-checkbox label="期货">期货</el-checkbox>
            <el-checkbox label="外汇">外汇</el-checkbox>
            <el-checkbox label="数字货币">数字货币</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSave" :loading="saving">
            保存设置
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 账户统计 -->
    <div class="settings-section">
      <h3 class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        账户统计
      </h3>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-statistic title="注册时间" :value="accountStats.registerDate" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="登录次数" :value="accountStats.loginCount" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="最后登录" :value="accountStats.lastLogin" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Upload, DataAnalysis } from '@element-plus/icons-vue'
import { httpClient } from '@/api/http'
import { updateProfile } from '@/api/user'

// 表单引用
const formRef = ref()
const saving = ref(false)

// 表单数据
const form = reactive({
  avatar: '',
  username: 'admin',
  email: '<EMAIL>',
  phone: '',
  realName: '',
  gender: 'male',
  birthday: null,
  location: [],
  bio: '',
  riskPreference: 'moderate',
  investmentExperience: 'intermediate',
  interestedMarkets: ['A股']
})

// 验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 地区选项
const locationOptions = [
  {
    value: 'beijing',
    label: '北京市',
    children: [
      { value: 'chaoyang', label: '朝阳区' },
      { value: 'haidian', label: '海淀区' },
      { value: 'dongcheng', label: '东城区' }
    ]
  },
  {
    value: 'shanghai',
    label: '上海市',
    children: [
      { value: 'huangpu', label: '黄浦区' },
      { value: 'xuhui', label: '徐汇区' },
      { value: 'changning', label: '长宁区' }
    ]
  }
]

// 账户统计
const accountStats = reactive({
  registerDate: '2024-01-01',
  loginCount: 156,
  lastLogin: '2025-07-29 14:30:00'
})

// 头像上传
const handleAvatarUpload = () => {
  ElMessage.info('头像上传功能开发中...')
}

// 移除头像
const handleAvatarRemove = () => {
  form.avatar = ''
  ElMessage.success('头像已移除')
}

// 保存设置
const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    // 调用API保存用户资料
    try {
      // 准备要发送的数据，符合后端的UserUpdate模型
      const profileData = {
        full_name: form.value.realName,
        email: form.value.email,
        phone: form.value.phone
      }

      const response = await updateProfile(profileData)
      if (response.success || response.data) {
        ElMessage.success('个人资料保存成功')
        // 更新本地存储的用户信息
        localStorage.setItem('userProfile', JSON.stringify(form.value))
      } else {
        throw new Error(response.message || '保存失败')
      }
    } catch (apiError) {
      console.warn('API保存失败，使用本地存储:', apiError)
      // API失败时保存到本地存储作为备用
      localStorage.setItem('userProfile', JSON.stringify(form.value))
      ElMessage.success('个人资料已保存到本地')
    }

  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    saving.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}

// 加载用户数据
const loadUserData = async () => {
  try {
    // 这里可以调用API获取用户数据
    // const userData = await userApi.getProfile()
    // Object.assign(form, userData)
  } catch (error) {
    ElMessage.error('加载用户数据失败')
  }
}

onMounted(() => {
  loadUserData()
})
</script>

<style scoped>
.user-profile-settings {
  max-width: 800px;
}

.settings-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.section-description {
  color: #666;
  font-size: 14px;
  margin: 0 0 24px 0;
}

.avatar-upload {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar {
  border: 2px solid #e6e6e6;
}

.upload-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.profile-form {
  margin-top: 24px;
}

.profile-form .el-form-item {
  margin-bottom: 24px;
}
</style>
