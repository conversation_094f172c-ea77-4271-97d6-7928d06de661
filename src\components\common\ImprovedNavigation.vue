<template>
  <div class="improved-navigation">
    <!-- 顶部导航栏 -->
    <header class="top-navbar">
      <div class="navbar-content">
        <!-- 左侧：Logo和标题 -->
        <div class="navbar-left">
          <div class="logo-section">
            <img src="/favicon.ico" alt="Logo" class="logo" />
            <span class="app-title">量化投资平台</span>
          </div>
          
          <!-- 面包屑导航 -->
          <div class="breadcrumb-section">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.path">
                <router-link v-if="item.path" :to="item.path">{{ item.title }}</router-link>
                <span v-else>{{ item.title }}</span>
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
        </div>

        <!-- 中间：搜索栏 -->
        <div class="navbar-center">
          <div class="search-container">
            <el-input
              v-model="searchQuery"
              placeholder="搜索股票、策略或功能..."
              class="search-input"
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            
            <!-- 搜索建议下拉 -->
            <div v-if="showSearchSuggestions" class="search-suggestions">
              <div class="suggestion-group">
                <div class="group-title">股票</div>
                <div v-for="stock in stockSuggestions" :key="stock.symbol" 
                     class="suggestion-item" @click="selectStock(stock)">
                  <span class="symbol">{{ stock.symbol }}</span>
                  <span class="name">{{ stock.name }}</span>
                </div>
              </div>
              
              <div class="suggestion-group">
                <div class="group-title">功能</div>
                <div v-for="feature in featureSuggestions" :key="feature.path" 
                     class="suggestion-item" @click="navigateTo(feature.path)">
                  <el-icon><component :is="feature.icon" /></el-icon>
                  <span>{{ feature.title }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：用户操作 -->
        <div class="navbar-right">
          <!-- 通知中心 -->
          <div class="notification-center">
            <el-badge :value="unreadCount" :hidden="unreadCount === 0">
              <el-button text @click="showNotifications">
                <el-icon><Bell /></el-icon>
              </el-button>
            </el-badge>
          </div>

          <!-- 市场状态指示器 -->
          <div class="market-status" :class="`status-${marketStatus}`">
            <div class="status-dot"></div>
            <span class="status-text">{{ marketStatusText }}</span>
          </div>

          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand">
            <div class="user-avatar">
              <el-avatar :size="32" :src="userAvatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ userName }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                <el-dropdown-item command="settings">系统设置</el-dropdown-item>
                <el-dropdown-item command="help">帮助中心</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 主导航菜单 -->
    <nav class="main-navigation">
      <div class="nav-content">
        <div class="nav-tabs">
          <div v-for="tab in mainTabs" :key="tab.key" 
               class="nav-tab" 
               :class="{ active: activeTab === tab.key }"
               @click="setActiveTab(tab.key)">
            <el-icon><component :is="tab.icon" /></el-icon>
            <span>{{ tab.title }}</span>
            
            <!-- 子菜单指示器 -->
            <el-icon v-if="tab.children" class="submenu-arrow">
              <ArrowDown />
            </el-icon>
          </div>
        </div>

        <!-- 快速操作按钮 -->
        <div class="quick-actions">
          <el-button type="primary" @click="quickTrade">
            <el-icon><TrendCharts /></el-icon>
            快速交易
          </el-button>
          <el-button @click="refreshData" :loading="refreshing">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 子菜单 -->
      <div v-if="activeTabData?.children" class="submenu">
        <div class="submenu-content">
          <router-link v-for="item in activeTabData.children" :key="item.path"
                       :to="item.path" 
                       class="submenu-item"
                       :class="{ active: $route.path === item.path }">
            <el-icon><component :is="item.icon" /></el-icon>
            <span>{{ item.title }}</span>
          </router-link>
        </div>
      </div>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Search, Bell, User, ArrowDown, TrendCharts, Refresh,
  Monitor, DataAnalysis, PieChart, Setting, Document
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const showSearchSuggestions = ref(false)
const activeTab = ref('dashboard')
const unreadCount = ref(5)
const refreshing = ref(false)
const userName = ref('投资者')
const userAvatar = ref('')

// 主导航标签
const mainTabs = ref([
  {
    key: 'dashboard',
    title: '仪表盘',
    icon: 'Monitor',
    path: '/dashboard'
  },
  {
    key: 'market',
    title: '市场行情',
    icon: 'DataAnalysis',
    path: '/market',
    children: [
      { title: '实时行情', path: '/market', icon: 'TrendCharts' },
      { title: '历史数据', path: '/market/history', icon: 'Document' },
      { title: '股票详情', path: '/market/detail', icon: 'DataAnalysis' }
    ]
  },
  {
    key: 'trading',
    title: '交易中心',
    icon: 'TrendCharts',
    path: '/trading',
    children: [
      { title: '模拟交易', path: '/trading/simulation', icon: 'TrendCharts' },
      { title: '实盘交易', path: '/trading/live', icon: 'Monitor' },
      { title: '订单管理', path: '/trading/orders', icon: 'Document' },
      { title: '持仓管理', path: '/trading/positions', icon: 'PieChart' }
    ]
  },
  {
    key: 'portfolio',
    title: '投资组合',
    icon: 'PieChart',
    path: '/portfolio'
  },
  {
    key: 'strategy',
    title: '策略中心',
    icon: 'Document',
    path: '/strategy',
    children: [
      { title: '策略库', path: '/strategy/library', icon: 'Document' },
      { title: '策略开发', path: '/strategy/develop', icon: 'Setting' },
      { title: '策略监控', path: '/strategy/monitor', icon: 'Monitor' }
    ]
  }
])

// 搜索建议
const stockSuggestions = ref([
  { symbol: '000001', name: '平安银行' },
  { symbol: '600036', name: '招商银行' },
  { symbol: '000858', name: '五粮液' }
])

const featureSuggestions = ref([
  { title: '快速交易', path: '/trading', icon: 'TrendCharts' },
  { title: '市场行情', path: '/market', icon: 'DataAnalysis' },
  { title: '投资组合', path: '/portfolio', icon: 'PieChart' }
])

// 计算属性
const activeTabData = computed(() => {
  return mainTabs.value.find(tab => tab.key === activeTab.value)
})

const breadcrumbs = computed(() => {
  const paths = route.path.split('/').filter(Boolean)
  const crumbs = [{ title: '首页', path: '/dashboard' }]
  
  let currentPath = ''
  paths.forEach(path => {
    currentPath += `/${path}`
    const tab = mainTabs.value.find(t => t.path === currentPath)
    if (tab) {
      crumbs.push({ title: tab.title, path: currentPath })
    }
  })
  
  return crumbs
})

const marketStatus = computed(() => {
  const hour = new Date().getHours()
  return (hour >= 9 && hour < 15) ? 'open' : 'closed'
})

const marketStatusText = computed(() => {
  return marketStatus.value === 'open' ? '开盘中' : '已收盘'
})

// 监听路由变化
watch(() => route.path, (newPath) => {
  const tab = mainTabs.value.find(t => newPath.startsWith(t.path))
  if (tab) {
    activeTab.value = tab.key
  }
}, { immediate: true })

// 方法
const setActiveTab = (tabKey: string) => {
  activeTab.value = tabKey
  const tab = mainTabs.value.find(t => t.key === tabKey)
  if (tab) {
    router.push(tab.path)
  }
}

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    ElMessage.info(`搜索: ${searchQuery.value}`)
    // 实现搜索逻辑
  }
}

const selectStock = (stock: any) => {
  router.push(`/market/detail/${stock.symbol}`)
  showSearchSuggestions.value = false
  searchQuery.value = ''
}

const navigateTo = (path: string) => {
  router.push(path)
  showSearchSuggestions.value = false
  searchQuery.value = ''
}

const showNotifications = () => {
  router.push('/notifications')
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'help':
      router.push('/help')
      break
    case 'logout':
      ElMessage.success('已退出登录')
      // 实现登出逻辑
      break
  }
}

const quickTrade = () => {
  router.push('/trading')
}

const refreshData = async () => {
  refreshing.value = true
  try {
    // 模拟刷新
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('数据已刷新')
  } finally {
    refreshing.value = false
  }
}

// 搜索框焦点事件
watch(searchQuery, (newValue) => {
  showSearchSuggestions.value = newValue.length > 0
})
</script>

<style scoped>
.improved-navigation {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.top-navbar {
  height: 64px;
  border-bottom: 1px solid #e8e8e8;
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 32px;
  height: 32px;
}

.app-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.navbar-center {
  flex: 1;
  max-width: 400px;
  position: relative;
}

.search-container {
  position: relative;
}

.search-input {
  width: 100%;
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1001;
}

.suggestion-group {
  padding: 8px 0;
}

.group-title {
  padding: 8px 16px;
  font-size: 12px;
  color: #999;
  font-weight: 600;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.suggestion-item:hover {
  background: #f5f5f5;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.market-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-open {
  background: #f0f9ff;
  color: #1890ff;
}

.status-closed {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.user-avatar:hover {
  background: #f5f5f5;
}

.main-navigation {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.nav-tabs {
  display: flex;
  gap: 8px;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s;
  color: #666;
}

.nav-tab:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.nav-tab.active {
  background: #1890ff;
  color: white;
}

.submenu {
  background: white;
  border-bottom: 1px solid #e8e8e8;
}

.submenu-content {
  display: flex;
  gap: 8px;
  padding: 8px 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.submenu-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 4px;
  text-decoration: none;
  color: #666;
  transition: all 0.2s;
}

.submenu-item:hover {
  background: #f5f5f5;
  color: #333;
}

.submenu-item.active {
  background: #e6f7ff;
  color: #1890ff;
}
</style>
