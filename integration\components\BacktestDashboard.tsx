/**
 * 回测仪表盘组件
 * 展示回测任务列表和状态
 */

import React, { useState, useMemo } from 'react';
import { useBacktest } from '../hooks/useBacktest';
import { BacktestResult } from '../api-client';

interface BacktestDashboardProps {
  className?: string;
  onTaskSelect?: (task: BacktestResult) => void;
  onCreateNew?: () => void;
}

const BacktestDashboard: React.FC<BacktestDashboardProps> = ({
  className = '',
  onTaskSelect,
  onCreateNew,
}) => {
  const {
    tasks,
    loading,
    error,
    creating,
    selectedTask,
    cancelBacktest,
    deleteBacktest,
    selectTask,
    clearError,
    refreshTasks,
  } = useBacktest();

  const [filter, setFilter] = useState<'all' | 'running' | 'completed' | 'failed'>('all');
  const [sortBy, setSortBy] = useState<'created_at' | 'progress' | 'status'>('created_at');

  // 过滤和排序任务
  const filteredTasks = useMemo(() => {
    let filtered = tasks;

    // 状态过滤
    if (filter !== 'all') {
      filtered = filtered.filter(task => task.status === filter);
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'created_at':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'progress':
          return b.progress - a.progress;
        case 'status':
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });

    return filtered;
  }, [tasks, filter, sortBy]);

  // 统计信息
  const stats = useMemo(() => {
    const total = tasks.length;
    const running = tasks.filter(t => t.status === 'running').length;
    const completed = tasks.filter(t => t.status === 'completed').length;
    const failed = tasks.filter(t => t.status === 'failed').length;

    return { total, running, completed, failed };
  }, [tasks]);

  const handleTaskClick = (task: BacktestResult) => {
    selectTask(task);
    onTaskSelect?.(task);
  };

  const handleCancel = async (taskId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (confirm('确定要取消这个任务吗？')) {
      await cancelBacktest(taskId);
    }
  };

  const handleDelete = async (taskId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (confirm('确定要删除这个任务吗？此操作不可恢复。')) {
      await deleteBacktest(taskId);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-blue-600 bg-blue-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      default: return 'text-yellow-600 bg-yellow-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'created': return '已创建';
      case 'running': return '运行中';
      case 'completed': return '已完成';
      case 'failed': return '失败';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  const formatDuration = (start: string, end?: string) => {
    const startTime = new Date(start);
    const endTime = end ? new Date(end) : new Date();
    const duration = endTime.getTime() - startTime.getTime();
    
    const minutes = Math.floor(duration / (1000 * 60));
    const seconds = Math.floor((duration % (1000 * 60)) / 1000);
    
    if (minutes > 0) {
      return `${minutes}分${seconds}秒`;
    }
    return `${seconds}秒`;
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* 头部 */}
      <div className="p-6 border-b">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-gray-900">回测任务</h2>
          <div className="flex gap-2">
            <button
              onClick={refreshTasks}
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? '刷新中...' : '刷新'}
            </button>
            {onCreateNew && (
              <button
                onClick={onCreateNew}
                disabled={creating}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {creating ? '创建中...' : '新建回测'}
              </button>
            )}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-4 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-500">总任务</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.running}</div>
            <div className="text-sm text-gray-500">运行中</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
            <div className="text-sm text-gray-500">已完成</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
            <div className="text-sm text-gray-500">失败</div>
          </div>
        </div>

        {/* 过滤和排序 */}
        <div className="flex gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              状态过滤
            </label>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="block w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="all">全部</option>
              <option value="running">运行中</option>
              <option value="completed">已完成</option>
              <option value="failed">失败</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              排序方式
            </label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="block w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="created_at">创建时间</option>
              <option value="progress">进度</option>
              <option value="status">状态</option>
            </select>
          </div>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mx-6 mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={clearError}
                className="text-red-400 hover:text-red-600"
              >
                <span className="sr-only">关闭</span>
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 任务列表 */}
      <div className="p-6">
        {loading && tasks.length === 0 ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-500">加载中...</p>
          </div>
        ) : filteredTasks.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {filter === 'all' ? '暂无回测任务' : `暂无${getStatusText(filter)}任务`}
            </p>
            {onCreateNew && (
              <button
                onClick={onCreateNew}
                className="mt-4 px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800"
              >
                创建第一个回测任务
              </button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredTasks.map((task) => (
              <div
                key={task.task_id}
                onClick={() => handleTaskClick(task)}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedTask?.task_id === task.task_id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-medium text-gray-900">
                        {task.task_id.slice(0, 8)}...
                      </h3>
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(task.status)}`}
                      >
                        {getStatusText(task.status)}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-6 text-sm text-gray-500">
                      <span>
                        创建于 {new Date(task.created_at).toLocaleString()}
                      </span>
                      {task.started_at && (
                        <span>
                          耗时 {formatDuration(task.started_at, task.completed_at)}
                        </span>
                      )}
                      {task.summary && (
                        <span>
                          收益率 {(task.summary.total_return * 100).toFixed(2)}%
                        </span>
                      )}
                    </div>
                    
                    {/* 进度条 */}
                    {task.status === 'running' && (
                      <div className="mt-2">
                        <div className="bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${task.progress * 100}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {(task.progress * 100).toFixed(1)}%
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* 操作按钮 */}
                  <div className="flex gap-2 ml-4">
                    {task.status === 'running' && (
                      <button
                        onClick={(e) => handleCancel(task.task_id, e)}
                        className="px-3 py-1 text-xs font-medium text-red-600 bg-red-100 rounded hover:bg-red-200"
                      >
                        取消
                      </button>
                    )}
                    {!['running'].includes(task.status) && (
                      <button
                        onClick={(e) => handleDelete(task.task_id, e)}
                        className="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded hover:bg-gray-200"
                      >
                        删除
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BacktestDashboard;