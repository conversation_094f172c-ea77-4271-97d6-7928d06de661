<template>
  <div class="batch-order-panel">
    <!-- 批量操作工具栏 -->
    <div class="batch-toolbar">
      <div class="toolbar-left">
        <el-button @click="addOrder" type="primary">
          <el-icon><Plus /></el-icon>
          添加订单
        </el-button>
        
        <el-button @click="importFromFile">
          <el-icon><Upload /></el-icon>
          导入文件
        </el-button>
        
        <el-button @click="loadTemplate">
          <el-icon><Document /></el-icon>
          加载模板
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-button @click="clearAll" :disabled="orders.length === 0">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
        
        <el-button @click="saveTemplate" :disabled="orders.length === 0">
          <el-icon><FolderAdd /></el-icon>
          保存模板
        </el-button>
      </div>
    </div>

    <!-- 批量设置 -->
    <div class="batch-settings">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="执行方式">
            <el-select v-model="batchSettings.executionMode" placeholder="选择执行方式">
              <el-option label="立即执行" value="immediate" />
              <el-option label="定时执行" value="scheduled" />
              <el-option label="条件执行" value="conditional" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="8" v-if="batchSettings.executionMode === 'scheduled'">
          <el-form-item label="执行时间">
            <el-date-picker
              v-model="batchSettings.scheduledTime"
              type="datetime"
              placeholder="选择执行时间"
              format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="执行间隔">
            <el-input-number
              v-model="batchSettings.interval"
              :min="0"
              :max="10000"
              placeholder="毫秒"
            />
            <span class="unit">毫秒</span>
          </el-form-item>
        </el-col>
      </el-row>
    </div>

    <!-- 订单列表 -->
    <div class="orders-container">
      <div class="orders-header">
        <div class="header-actions">
          <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
          <span class="order-count">共 {{ orders.length }} 个订单</span>
        </div>
        
        <div class="batch-actions">
          <el-button size="small" @click="batchEdit" :disabled="selectedOrders.length === 0">
            批量编辑
          </el-button>
          <el-button size="small" @click="batchDelete" :disabled="selectedOrders.length === 0">
            批量删除
          </el-button>
        </div>
      </div>

      <div class="orders-list">
        <div
          v-for="(order, index) in orders"
          :key="order.id"
          class="order-item"
          :class="{ 'order-error': order.hasError }"
        >
          <div class="order-checkbox">
            <el-checkbox v-model="order.selected" @change="updateSelection" />
          </div>
          
          <div class="order-index">{{ index + 1 }}</div>
          
          <div class="order-form">
            <el-row :gutter="12">
              <el-col :span="4">
                <el-input
                  v-model="order.symbol"
                  placeholder="股票代码"
                  @blur="validateOrder(order)"
                />
              </el-col>
              
              <el-col :span="3">
                <el-select v-model="order.side" placeholder="方向">
                  <el-option label="买入" value="buy" />
                  <el-option label="卖出" value="sell" />
                </el-select>
              </el-col>
              
              <el-col :span="3">
                <el-select v-model="order.orderType" placeholder="类型">
                  <el-option label="市价" value="market" />
                  <el-option label="限价" value="limit" />
                  <el-option label="止损" value="stop" />
                </el-select>
              </el-col>
              
              <el-col :span="4">
                <el-input-number
                  v-model="order.quantity"
                  :min="1"
                  placeholder="数量"
                  controls-position="right"
                />
              </el-col>
              
              <el-col :span="4" v-if="order.orderType !== 'market'">
                <el-input-number
                  v-model="order.price"
                  :min="0"
                  :precision="2"
                  placeholder="价格"
                  controls-position="right"
                />
              </el-col>
              
              <el-col :span="4">
                <el-input
                  v-model="order.remark"
                  placeholder="备注"
                />
              </el-col>
              
              <el-col :span="2">
                <el-button
                  size="small"
                  type="danger"
                  @click="removeOrder(index)"
                  :icon="Delete"
                />
              </el-col>
            </el-row>
            
            <!-- 错误提示 -->
            <div v-if="order.hasError" class="order-error-message">
              {{ order.errorMessage }}
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="orders.length === 0" class="empty-orders">
          <el-empty description="暂无订单，点击添加订单开始批量交易" />
        </div>
      </div>
    </div>

    <!-- 提交区域 -->
    <div class="submit-area">
      <div class="submit-summary">
        <div class="summary-item">
          <span class="label">总订单数:</span>
          <span class="value">{{ orders.length }}</span>
        </div>
        <div class="summary-item">
          <span class="label">有效订单:</span>
          <span class="value">{{ validOrders.length }}</span>
        </div>
        <div class="summary-item">
          <span class="label">预计金额:</span>
          <span class="value">{{ formatCurrency(totalAmount) }}</span>
        </div>
      </div>
      
      <div class="submit-actions">
        <el-button @click="validateAll">验证全部</el-button>
        <el-button @click="previewExecution">预览执行</el-button>
        <el-button
          type="primary"
          @click="submitBatchOrders"
          :disabled="validOrders.length === 0"
          :loading="submitting"
        >
          提交批量订单
        </el-button>
      </div>
    </div>

    <!-- 文件导入对话框 -->
    <el-dialog v-model="showImportDialog" title="导入订单文件" width="600px">
      <div class="import-content">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :on-change="handleFileChange"
          accept=".csv,.xlsx,.xls"
          drag
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 CSV、Excel 格式，文件大小不超过 10MB
            </div>
          </template>
        </el-upload>
        
        <div class="import-template">
          <h4>文件格式要求:</h4>
          <p>CSV文件应包含以下列：股票代码,方向,类型,数量,价格,备注</p>
          <el-button text @click="downloadTemplate">下载模板文件</el-button>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmImport">确认导入</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Upload, Document, Delete, FolderAdd, UploadFilled } from '@element-plus/icons-vue'

// 订单接口
interface BatchOrder {
  id: string
  symbol: string
  side: 'buy' | 'sell'
  orderType: 'market' | 'limit' | 'stop'
  quantity: number
  price?: number
  remark?: string
  selected: boolean
  hasError: boolean
  errorMessage?: string
}

// 批量设置接口
interface BatchSettings {
  executionMode: 'immediate' | 'scheduled' | 'conditional'
  scheduledTime?: Date
  interval: number
}

// 响应式数据
const orders = ref<BatchOrder[]>([])
const batchSettings = ref<BatchSettings>({
  executionMode: 'immediate',
  interval: 100
})
const selectAll = ref(false)
const submitting = ref(false)
const showImportDialog = ref(false)
const uploadRef = ref()

// 计算属性
const selectedOrders = computed(() => orders.value.filter(order => order.selected))

const validOrders = computed(() => orders.value.filter(order => !order.hasError))

const totalAmount = computed(() => {
  return validOrders.value.reduce((total, order) => {
    if (order.orderType === 'market') {
      // 市价单需要估算
      return total + (order.quantity * 10) // 假设平均价格
    } else {
      return total + (order.quantity * (order.price || 0))
    }
  }, 0)
})

// 事件定义
const emit = defineEmits<{
  'orders-submitted': [orders: BatchOrder[]]
}>()

// 方法
const addOrder = () => {
  const newOrder: BatchOrder = {
    id: generateId(),
    symbol: '',
    side: 'buy',
    orderType: 'limit',
    quantity: 100,
    price: 0,
    remark: '',
    selected: false,
    hasError: false
  }
  orders.value.push(newOrder)
}

const removeOrder = (index: number) => {
  orders.value.splice(index, 1)
  updateSelection()
}

const clearAll = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有订单吗？', '确认清空', {
      type: 'warning'
    })
    orders.value = []
    selectAll.value = false
  } catch {
    // 用户取消
  }
}

const handleSelectAll = () => {
  orders.value.forEach(order => {
    order.selected = selectAll.value
  })
}

const updateSelection = () => {
  const selectedCount = selectedOrders.value.length
  selectAll.value = selectedCount === orders.value.length && orders.value.length > 0
}

const validateOrder = (order: BatchOrder) => {
  order.hasError = false
  order.errorMessage = ''

  if (!order.symbol) {
    order.hasError = true
    order.errorMessage = '请输入股票代码'
    return
  }

  if (order.quantity <= 0) {
    order.hasError = true
    order.errorMessage = '数量必须大于0'
    return
  }

  if (order.orderType !== 'market' && (!order.price || order.price <= 0)) {
    order.hasError = true
    order.errorMessage = '请输入有效价格'
    return
  }
}

const validateAll = () => {
  orders.value.forEach(validateOrder)
  const errorCount = orders.value.filter(order => order.hasError).length
  
  if (errorCount === 0) {
    ElMessage.success('所有订单验证通过')
  } else {
    ElMessage.warning(`发现 ${errorCount} 个订单有错误，请检查`)
  }
}

const batchEdit = () => {
  // 实现批量编辑逻辑
  ElMessage.info('批量编辑功能开发中')
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedOrders.value.length} 个订单吗？`, '确认删除', {
      type: 'warning'
    })
    
    orders.value = orders.value.filter(order => !order.selected)
    selectAll.value = false
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const previewExecution = () => {
  // 实现执行预览
  ElMessage.info('执行预览功能开发中')
}

const submitBatchOrders = async () => {
  if (validOrders.value.length === 0) {
    ElMessage.warning('没有有效的订单可以提交')
    return
  }

  submitting.value = true
  try {
    emit('orders-submitted', validOrders.value)
  } finally {
    submitting.value = false
  }
}

const importFromFile = () => {
  showImportDialog.value = true
}

const handleFileChange = (file: any) => {
  // 处理文件选择
  console.log('选择文件:', file)
}

const confirmImport = () => {
  // 实现文件导入逻辑
  ElMessage.info('文件导入功能开发中')
  showImportDialog.value = false
}

const downloadTemplate = () => {
  // 下载模板文件
  const csvContent = '股票代码,方向,类型,数量,价格,备注\n000001,buy,limit,100,12.50,示例订单'
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = '批量订单模板.csv'
  link.click()
}

const loadTemplate = () => {
  // 加载预设模板
  ElMessage.info('模板加载功能开发中')
}

const saveTemplate = () => {
  // 保存当前订单为模板
  ElMessage.info('模板保存功能开发中')
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 监听订单变化
watch(orders, () => {
  updateSelection()
}, { deep: true })
</script>

<style scoped>
.batch-order-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
}

.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 8px;
}

.batch-settings {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.unit {
  margin-left: 8px;
  color: #666;
  font-size: 12px;
}

.orders-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #f5f5f5;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.order-count {
  font-size: 12px;
  color: #666;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.orders-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.order-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: white;
  transition: all 0.2s;
}

.order-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.order-error {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.order-checkbox {
  margin-top: 8px;
}

.order-index {
  min-width: 24px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.order-form {
  flex: 1;
}

.order-error-message {
  margin-top: 8px;
  padding: 4px 8px;
  background: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  color: #d32f2f;
  font-size: 12px;
}

.empty-orders {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.submit-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-top: 1px solid #e8e8e8;
  background: #fafafa;
}

.submit-summary {
  display: flex;
  gap: 24px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 12px;
  color: #666;
}

.value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.submit-actions {
  display: flex;
  gap: 8px;
}

.import-content {
  padding: 20px 0;
}

.import-template {
  margin-top: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
}

.import-template h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.import-template p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
}
</style>
