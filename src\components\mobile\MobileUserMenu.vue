<template>
  <div class="mobile-user-menu">
    <!-- 用户信息 -->
    <div class="user-info">
      <div class="user-avatar">
        <el-avatar :size="60" :src="userInfo.avatar">
          <el-icon><User /></el-icon>
        </el-avatar>
      </div>
      <div class="user-details">
        <h3 class="user-name">{{ userInfo.name || '未登录' }}</h3>
        <p class="user-email">{{ userInfo.email || '请登录账户' }}</p>
      </div>
    </div>
    
    <!-- 菜单项 -->
    <div class="menu-items">
      <div
        v-for="item in menuItems"
        :key="item.id"
        class="menu-item"
        @click="handleMenuClick(item)"
      >
        <div class="menu-icon">
          <el-icon :color="item.color">
            <component :is="item.icon" />
          </el-icon>
        </div>
        <span class="menu-text">{{ item.text }}</span>
        <div class="menu-arrow">
          <el-icon>
            <ArrowRight />
          </el-icon>
        </div>
      </div>
    </div>
    
    <!-- 登录/登出按钮 -->
    <div class="auth-actions">
      <el-button
        v-if="!userInfo.isLoggedIn"
        type="primary"
        size="large"
        style="width: 100%"
        @click="handleLogin"
      >
        登录
      </el-button>
      <el-button
        v-else
        size="large"
        style="width: 100%"
        @click="handleLogout"
      >
        退出登录
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  User, ArrowRight, Setting, Document, Wallet,
  DataAnalysis, Bell, QuestionFilled
} from '@element-plus/icons-vue'

interface MenuItem {
  id: string
  text: string
  icon: any
  color: string
  action: string
}

interface UserInfo {
  name?: string
  email?: string
  avatar?: string
  isLoggedIn: boolean
}

const emit = defineEmits<{
  menuAction: [action: string, item?: MenuItem]
}>()

const userInfo = ref<UserInfo>({
  name: '演示用户',
  email: '<EMAIL>',
  isLoggedIn: false
})

const menuItems: MenuItem[] = [
  {
    id: '1',
    text: '个人设置',
    icon: Setting,
    color: '#909399',
    action: 'settings'
  },
  {
    id: '2',
    text: '我的策略',
    icon: Document,
    color: '#67C23A',
    action: 'strategies'
  },
  {
    id: '3',
    text: '资产管理',
    icon: Wallet,
    color: '#E6A23C',
    action: 'assets'
  },
  {
    id: '4',
    text: '交易记录',
    icon: DataAnalysis,
    color: '#409EFF',
    action: 'records'
  },
  {
    id: '5',
    text: '消息通知',
    icon: Bell,
    color: '#F56C6C',
    action: 'notifications'
  },
  {
    id: '6',
    text: '帮助中心',
    icon: QuestionFilled,
    color: '#909399',
    action: 'help'
  }
]

const handleMenuClick = (item: MenuItem) => {
  emit('menuAction', item.action, item)
}

const handleLogin = () => {
  emit('menuAction', 'login')
}

const handleLogout = () => {
  userInfo.value.isLoggedIn = false
  emit('menuAction', 'logout')
}
</script>

<style scoped>
.mobile-user-menu {
  padding: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.user-avatar {
  margin-right: 16px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: #303133;
}

.user-email {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.menu-items {
  margin-bottom: 30px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s;
  cursor: pointer;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #f5f7fa;
  margin: 0 -20px;
  padding: 16px 20px;
}

.menu-icon {
  margin-right: 16px;
}

.menu-text {
  flex: 1;
  font-size: 16px;
  color: #303133;
}

.menu-arrow {
  color: #c0c4cc;
}

.auth-actions {
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
