/**
 * WebSocket连接管理器
 * 提供稳定的WebSocket连接和消息处理
 */

import { ref, reactive } from 'vue'

export interface WebSocketConfig {
  url: string
  protocols?: string[]
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  messageQueueSize?: number
}

export interface ConnectionStats {
  status: 'connecting' | 'connected' | 'disconnected' | 'error'
  reconnectAttempts: number
  lastConnected: Date | null
  lastDisconnected: Date | null
  messagesSent: number
  messagesReceived: number
  bytesTransferred: number
}

export class WebSocketManager {
  private ws: WebSocket | null = null
  private config: Required<WebSocketConfig>
  private reconnectTimer: number | null = null
  private heartbeatTimer: number | null = null
  private messageQueue: any[] = []
  private subscribers = new Map<string, Set<Function>>()
  
  public stats = reactive<ConnectionStats>({
    status: 'disconnected',
    reconnectAttempts: 0,
    lastConnected: null,
    lastDisconnected: null,
    messagesSent: 0,
    messagesReceived: 0,
    bytesTransferred: 0
  })

  constructor(config: WebSocketConfig) {
    this.config = {
      protocols: [],
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      messageQueueSize: 100,
      ...config
    }
  }

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve()
        return
      }

      this.stats.status = 'connecting'
      
      try {
        this.ws = new WebSocket(this.config.url, this.config.protocols)
        
        this.ws.onopen = () => {
          this.stats.status = 'connected'
          this.stats.lastConnected = new Date()
          this.stats.reconnectAttempts = 0
          
          this.startHeartbeat()
          this.flushMessageQueue()
          
          this.emit('connected', null)
          resolve()
        }
        
        this.ws.onmessage = (event) => {
          this.handleMessage(event)
        }
        
        this.ws.onclose = (event) => {
          this.handleClose(event)
        }
        
        this.ws.onerror = (error) => {
          this.handleError(error)
          reject(error)
        }
        
      } catch (error) {
        this.stats.status = 'error'
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.clearTimers()
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect')
      this.ws = null
    }
    
    this.stats.status = 'disconnected'
    this.stats.lastDisconnected = new Date()
  }

  /**
   * 发送消息
   */
  send(data: any): boolean {
    const message = typeof data === 'string' ? data : JSON.stringify(data)
    
    if (this.ws?.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(message)
        this.stats.messagesSent++
        this.stats.bytesTransferred += new Blob([message]).size
        return true
      } catch (error) {
        console.error('发送消息失败:', error)
        this.queueMessage(data)
        return false
      }
    } else {
      this.queueMessage(data)
      return false
    }
  }

  /**
   * 订阅消息
   */
  subscribe(event: string, callback: Function): () => void {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, new Set())
    }
    
    this.subscribers.get(event)!.add(callback)
    
    // 返回取消订阅函数
    return () => {
      this.subscribers.get(event)?.delete(callback)
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(event: string, callback?: Function): void {
    if (callback) {
      this.subscribers.get(event)?.delete(callback)
    } else {
      this.subscribers.delete(event)
    }
  }

  /**
   * 获取连接状态
   */
  getStatus(): string {
    return this.stats.status
  }

  /**
   * 获取统计信息
   */
  getStats(): ConnectionStats {
    return { ...this.stats }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      this.stats.messagesReceived++
      this.stats.bytesTransferred += new Blob([event.data]).size
      
      let data: any
      try {
        data = JSON.parse(event.data)
      } catch {
        data = event.data
      }
      
      // 处理心跳响应
      if (data.type === 'pong') {
        return
      }
      
      // 触发消息事件
      this.emit('message', data)
      
      // 触发特定类型的事件
      if (data.type) {
        this.emit(data.type, data)
      }
      
    } catch (error) {
      console.error('处理消息失败:', error)
    }
  }

  /**
   * 处理连接关闭
   */
  private handleClose(event: CloseEvent): void {
    this.stats.status = 'disconnected'
    this.stats.lastDisconnected = new Date()
    
    this.clearTimers()
    this.emit('disconnected', event)
    
    // 如果不是手动关闭，尝试重连
    if (event.code !== 1000 && this.stats.reconnectAttempts < this.config.maxReconnectAttempts) {
      this.scheduleReconnect()
    }
  }

  /**
   * 处理连接错误
   */
  private handleError(error: Event): void {
    this.stats.status = 'error'
    console.error('WebSocket错误:', error)
    this.emit('error', error)
  }

  /**
   * 计划重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }
    
    this.stats.reconnectAttempts++
    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.stats.reconnectAttempts - 1),
      30000
    )
    
    this.reconnectTimer = window.setTimeout(() => {
      console.log(`尝试第${this.stats.reconnectAttempts}次重连...`)
      this.connect().catch(error => {
        console.error('重连失败:', error)
      })
    }, delay)
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.clearHeartbeat()
    
    this.heartbeatTimer = window.setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.send({ type: 'ping', timestamp: Date.now() })
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * 清除心跳
   */
  private clearHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 清除所有定时器
   */
  private clearTimers(): void {
    this.clearHeartbeat()
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 消息入队
   */
  private queueMessage(data: any): void {
    if (this.messageQueue.length >= this.config.messageQueueSize) {
      this.messageQueue.shift() // 移除最旧的消息
    }
    
    this.messageQueue.push(data)
  }

  /**
   * 刷新消息队列
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      if (!this.send(message)) {
        // 如果发送失败，重新入队
        this.messageQueue.unshift(message)
        break
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data: any): void {
    const callbacks = this.subscribers.get(event)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件回调执行失败 [${event}]:`, error)
        }
      })
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.disconnect()
    this.subscribers.clear()
    this.messageQueue.length = 0
  }
}

// 创建市场数据WebSocket管理器
export const marketWebSocket = new WebSocketManager({
  url: import.meta.env.VITE_WS_MARKET_URL || 'ws://localhost:8000/ws/market',
  reconnectInterval: 3000,
  maxReconnectAttempts: 10,
  heartbeatInterval: 30000,
  messageQueueSize: 50
})

// 自动连接
if (typeof window !== 'undefined') {
  marketWebSocket.connect().catch(error => {
    console.warn('市场数据WebSocket连接失败:', error)
  })
}

export default WebSocketManager
