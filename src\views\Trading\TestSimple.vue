<template>
  <div class="test-simple">
    <h1>测试页面</h1>
    <p>当前时间: {{ currentTime }}</p>
    <p>WebSocket状态: {{ wsStatus }}</p>
    <button @click="testWebSocket">测试WebSocket</button>
    <div v-if="wsMessages.length > 0">
      <h3>WebSocket消息:</h3>
      <ul>
        <li v-for="(msg, index) in wsMessages" :key="index">{{ msg }}</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const currentTime = ref('')
const wsStatus = ref('未连接')
const wsMessages = ref<string[]>([])
let ws: WebSocket | null = null
let timer: number | null = null

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const testWebSocket = () => {
  try {
    const wsUrl = 'ws://localhost:8000/api/v1/ws?token=dev-token-for-testing'
    console.log('尝试连接:', wsUrl)
    
    ws = new WebSocket(wsUrl)
    
    ws.onopen = () => {
      wsStatus.value = '已连接'
      wsMessages.value.push('WebSocket连接成功')
      console.log('WebSocket连接成功')
    }
    
    ws.onmessage = (event) => {
      wsMessages.value.push(`收到消息: ${event.data}`)
      console.log('收到消息:', event.data)
    }
    
    ws.onerror = (error) => {
      wsStatus.value = '连接错误'
      wsMessages.value.push('WebSocket连接错误')
      console.error('WebSocket错误:', error)
    }
    
    ws.onclose = () => {
      wsStatus.value = '连接关闭'
      wsMessages.value.push('WebSocket连接关闭')
      console.log('WebSocket连接关闭')
    }
  } catch (error) {
    console.error('WebSocket创建失败:', error)
    wsStatus.value = '创建失败'
    wsMessages.value.push(`创建失败: ${error}`)
  }
}

onMounted(() => {
  updateTime()
  timer = window.setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
  if (ws) {
    ws.close()
  }
})
</script>

<style scoped>
.test-simple {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  color: #2c3e50;
  margin-bottom: 20px;
}

p {
  margin: 10px 0;
  font-size: 16px;
}

button {
  background: #409eff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin: 10px 0;
}

button:hover {
  background: #66b1ff;
}

ul {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

li {
  margin: 5px 0;
  padding: 5px;
  background: white;
  border-radius: 2px;
}
</style>
