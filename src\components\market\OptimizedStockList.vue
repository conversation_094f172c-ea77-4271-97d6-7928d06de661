<template>
  <div class="optimized-stock-list">
    <!-- 搜索和过滤器 -->
    <div class="list-header">
      <div class="search-section">
        <el-input
          v-model="searchQuery"
          placeholder="搜索股票代码或名称..."
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <div class="filter-section">
        <el-select v-model="sortBy" @change="handleSort" placeholder="排序方式">
          <el-option label="代码" value="symbol" />
          <el-option label="名称" value="name" />
          <el-option label="价格" value="price" />
          <el-option label="涨跌幅" value="change_percent" />
          <el-option label="成交量" value="volume" />
        </el-select>
        
        <el-button @click="toggleSortOrder">
          <el-icon><Sort /></el-icon>
          {{ sortOrder === 'asc' ? '升序' : '降序' }}
        </el-button>
      </div>
      
      <div class="stats-section">
        <span class="stats-text">
          显示 {{ visibleStocks.length }} / {{ totalStocks }} 只股票
        </span>
        <el-button text @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 虚拟滚动股票列表 -->
    <div class="list-container">
      <VirtualList
        ref="virtualListRef"
        :items="visibleStocks"
        :item-height="60"
        :container-height="400"
        :buffer="10"
        key-field="symbol"
      >
        <template #default="{ item, index }">
          <div class="stock-item" @click="selectStock(item)" :class="{ selected: selectedSymbol === item.symbol }">
            <div class="stock-basic">
              <div class="symbol-section">
                <span class="symbol">{{ item.symbol }}</span>
                <span class="name">{{ item.name }}</span>
              </div>
              
              <div class="price-section">
                <span class="price">{{ formatPrice(item.price) }}</span>
                <span class="change" :class="getChangeClass(item.change)">
                  {{ formatChange(item.change) }}
                </span>
                <span class="change-percent" :class="getChangeClass(item.change)">
                  {{ formatPercent(item.change_percent) }}
                </span>
              </div>
            </div>
            
            <div class="stock-details">
              <div class="volume">
                <span class="label">成交量:</span>
                <span class="value">{{ formatVolume(item.volume) }}</span>
              </div>
              
              <div class="market-cap" v-if="item.market_cap">
                <span class="label">市值:</span>
                <span class="value">{{ formatMarketCap(item.market_cap) }}</span>
              </div>
              
              <div class="actions">
                <el-button size="small" type="primary" @click.stop="quickBuy(item)">
                  买入
                </el-button>
                <el-button size="small" @click.stop="quickSell(item)">
                  卖出
                </el-button>
                <el-button size="small" text @click.stop="viewDetail(item)">
                  详情
                </el-button>
              </div>
            </div>
          </div>
        </template>
      </VirtualList>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <el-loading-spinner />
      <span>加载中...</span>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && visibleStocks.length === 0" class="empty-state">
      <el-empty description="没有找到匹配的股票" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, Sort, Refresh } from '@element-plus/icons-vue'
import VirtualList from '@/components/common/VirtualList.vue'
import apiClient from '@/api/optimized-client'
import { debounce } from '@/utils/performance'

// 接口定义
interface Stock {
  symbol: string
  name: string
  price: number
  change: number
  change_percent: number
  volume: number
  market_cap?: number
}

const router = useRouter()

// 响应式数据
const stocks = ref<Stock[]>([])
const searchQuery = ref('')
const sortBy = ref('symbol')
const sortOrder = ref<'asc' | 'desc'>('asc')
const selectedSymbol = ref('')
const loading = ref(false)
const virtualListRef = ref()

// 计算属性
const visibleStocks = computed(() => {
  let filtered = stocks.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(stock => 
      stock.symbol.toLowerCase().includes(query) ||
      stock.name.toLowerCase().includes(query)
    )
  }

  // 排序
  filtered.sort((a, b) => {
    const aValue = a[sortBy.value as keyof Stock]
    const bValue = b[sortBy.value as keyof Stock]
    
    let result = 0
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      result = aValue.localeCompare(bValue)
    } else {
      result = (aValue as number) - (bValue as number)
    }
    
    return sortOrder.value === 'asc' ? result : -result
  })

  return filtered
})

const totalStocks = computed(() => stocks.value.length)

// 防抖搜索
const handleSearch = debounce(() => {
  // 搜索时重置到顶部
  if (virtualListRef.value) {
    virtualListRef.value.scrollToTop()
  }
}, 300)

// 方法
const loadStocks = async () => {
  loading.value = true
  try {
    const response = await apiClient.get<Stock[]>('/market/stocks', {
      cache: true,
      cacheTTL: 30000 // 30秒缓存
    })
    
    if (response.success) {
      stocks.value = response.data
    } else {
      ElMessage.error('加载股票数据失败')
    }
  } catch (error) {
    console.error('加载股票失败:', error)
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}

const refreshData = async () => {
  // 清除缓存并重新加载
  apiClient.clearCache()
  await loadStocks()
  ElMessage.success('数据已刷新')
}

const handleSort = () => {
  if (virtualListRef.value) {
    virtualListRef.value.scrollToTop()
  }
}

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  handleSort()
}

const selectStock = (stock: Stock) => {
  selectedSymbol.value = stock.symbol
  // 触发选择事件
  emit('stock-selected', stock)
}

const quickBuy = (stock: Stock) => {
  ElMessage.info(`快速买入 ${stock.symbol}`)
  router.push(`/trading?action=buy&symbol=${stock.symbol}`)
}

const quickSell = (stock: Stock) => {
  ElMessage.info(`快速卖出 ${stock.symbol}`)
  router.push(`/trading?action=sell&symbol=${stock.symbol}`)
}

const viewDetail = (stock: Stock) => {
  router.push(`/market/detail/${stock.symbol}`)
}

// 格式化函数
const formatPrice = (price: number) => {
  return price.toFixed(2)
}

const formatChange = (change: number) => {
  return (change >= 0 ? '+' : '') + change.toFixed(2)
}

const formatPercent = (percent: number) => {
  return (percent >= 0 ? '+' : '') + percent.toFixed(2) + '%'
}

const formatVolume = (volume: number) => {
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(1) + '亿'
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(1) + '万'
  }
  return volume.toString()
}

const formatMarketCap = (marketCap: number) => {
  if (marketCap >= 100000000) {
    return (marketCap / 100000000).toFixed(1) + '亿'
  } else if (marketCap >= 10000) {
    return (marketCap / 10000).toFixed(1) + '万'
  }
  return marketCap.toString()
}

const getChangeClass = (change: number) => {
  return change >= 0 ? 'positive' : 'negative'
}

// 事件定义
const emit = defineEmits<{
  'stock-selected': [stock: Stock]
}>()

// 生命周期
onMounted(() => {
  loadStocks()
  
  // 预加载相关数据
  apiClient.preload([
    '/market/overview',
    '/market/sectors'
  ])
})

// 监听搜索查询变化
watch(searchQuery, () => {
  handleSearch()
})
</script>

<style scoped>
.optimized-stock-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.list-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.search-section {
  flex: 1;
  max-width: 300px;
}

.filter-section {
  display: flex;
  gap: 8px;
}

.stats-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-text {
  font-size: 12px;
  color: #666;
}

.list-container {
  flex: 1;
  position: relative;
}

.stock-item {
  display: flex;
  flex-direction: column;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.stock-item:hover {
  background: #f5f5f5;
}

.stock-item.selected {
  background: #e6f7ff;
  border-color: #1890ff;
}

.stock-basic {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.symbol-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.symbol {
  font-weight: 600;
  color: #333;
}

.name {
  font-size: 12px;
  color: #666;
}

.price-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.price {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.change, .change-percent {
  font-size: 12px;
}

.positive {
  color: #52c41a;
}

.negative {
  color: #ff4d4f;
}

.stock-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.volume, .market-cap {
  display: flex;
  gap: 4px;
}

.label {
  color: #999;
}

.actions {
  display: flex;
  gap: 4px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  gap: 8px;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
