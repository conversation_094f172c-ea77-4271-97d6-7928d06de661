/**
 * 滚动修复工具
 * 强制启用页面滚动功能
 */

export function enablePageScroll(): void {
  console.log('🔧 启用页面滚动修复...')

  // 等待DOM加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyScrollFix)
  } else {
    applyScrollFix()
  }

  // 在Vue组件挂载后再次应用修复
  setTimeout(applyScrollFix, 1000)
  setTimeout(applyScrollFix, 3000)
}

function applyScrollFix(): void {
  console.log('📜 应用滚动修复...')

  try {
    // 1. 强制设置HTML和Body的滚动属性
    const html = document.documentElement
    const body = document.body

    html.style.setProperty('overflow', 'visible', 'important')
    html.style.setProperty('overflow-y', 'scroll', 'important')
    html.style.setProperty('overflow-x', 'hidden', 'important')
    html.style.setProperty('height', 'auto', 'important')
    html.style.setProperty('min-height', '100vh', 'important')

    body.style.setProperty('overflow', 'visible', 'important')
    body.style.setProperty('overflow-y', 'visible', 'important')
    body.style.setProperty('overflow-x', 'hidden', 'important')
    body.style.setProperty('height', 'auto', 'important')
    body.style.setProperty('min-height', '100vh', 'important')

    // 2. 修复主要容器
    const containers = [
      '#app',
      '#app-wrapper',
      '.default-layout',
      '.layout-content',
      '.market-view-optimized',
      '.market-content'
    ]

    containers.forEach(selector => {
      const elements = document.querySelectorAll(selector)
      elements.forEach((el: Element) => {
        const htmlEl = el as HTMLElement
        htmlEl.style.setProperty('height', 'auto', 'important')
        htmlEl.style.setProperty('overflow', 'visible', 'important')

        if (selector === '.market-view-optimized') {
          htmlEl.style.setProperty('min-height', '100vh', 'important')
        }

        if (selector === '.layout-content') {
          htmlEl.style.setProperty('overflow', 'visible', 'important')
          htmlEl.style.setProperty('overflow-y', 'visible', 'important')
        }
      })
    })

    // 3. 移除可能阻止滚动的样式
    const allElements = document.querySelectorAll('*')
    allElements.forEach((el: Element) => {
      const computed = getComputedStyle(el)
      const htmlEl = el as HTMLElement

      // 移除固定高度限制
      if (computed.height === '100vh' || computed.height === '100%') {
        if (!el.matches('html, body, .market-view-optimized')) {
          htmlEl.style.setProperty('height', 'auto', 'important')
        }
      }

      // 移除overflow: hidden (除了必要的元素)
      if (computed.overflow === 'hidden' && !el.matches('img, video, canvas')) {
        htmlEl.style.setProperty('overflow', 'visible', 'important')
      }
    })

    // 4. 创建强制滚动样式
    let fixStyle = document.getElementById('scroll-force-fix')
    if (!fixStyle) {
      fixStyle = document.createElement('style')
      fixStyle.id = 'scroll-force-fix'
      fixStyle.textContent = `
        /* 强制滚动修复 */
        html {
          overflow-y: scroll !important;
          height: auto !important;
        }

        body {
          overflow: visible !important;
          height: auto !important;
        }

        #app, #app-wrapper {
          height: auto !important;
          overflow: visible !important;
        }

        .default-layout {
          height: auto !important;
          overflow: visible !important;
        }

        .layout-content {
          height: auto !important;
          overflow: visible !important;
        }

        .market-view-optimized {
          height: auto !important;
          min-height: 100vh !important;
          overflow: visible !important;
        }

        .market-content {
          height: auto !important;
          overflow: visible !important;
        }
      `
      document.head.appendChild(fixStyle)
    }

    // 5. 强制重新计算布局
    document.body.offsetHeight

    // 6. 验证修复效果
    const canScroll = document.documentElement.scrollHeight > window.innerHeight
    console.log(`📊 修复后状态: 文档高度=${document.documentElement.scrollHeight}px, 视口高度=${window.innerHeight}px, 可滚动=${canScroll}`)

    if (canScroll) {
      console.log('✅ 滚动修复成功')

      // 测试滚动功能
      setTimeout(() => {
        window.scrollTo(0, 10)
        setTimeout(() => {
          const scrolled = window.pageYOffset > 0
          console.log(`🧪 滚动测试: ${scrolled ? '成功' : '失败'} (位置: ${window.pageYOffset}px)`)
          if (scrolled) {
            window.scrollTo(0, 0) // 滚动回顶部
          }
        }, 100)
      }, 500)
    } else {
      console.log('⚠️  页面内容仍然不足以滚动')
    }

  } catch (error) {
    console.error('❌ 滚动修复失败:', error)
  }
}

// 监听路由变化，重新应用修复
export function watchRouteChanges(): void {
  // Vue Router 路由变化监听
  if (window.history && window.history.pushState) {
    const originalPushState = window.history.pushState
    window.history.pushState = function(...args: any[]) {
      originalPushState.apply(window.history, args)
      setTimeout(applyScrollFix, 100)
    }

    const originalReplaceState = window.history.replaceState
    window.history.replaceState = function(...args: any[]) {
      originalReplaceState.apply(window.history, args)
      setTimeout(applyScrollFix, 100)
    }
  }

  // 监听popstate事件
  window.addEventListener('popstate', () => {
    setTimeout(applyScrollFix, 100)
  })
}

// 自动初始化
if (typeof window !== 'undefined') {
  enablePageScroll()
  watchRouteChanges()
}
