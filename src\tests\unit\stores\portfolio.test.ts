/**
 * 投资组合Store单元测试
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { usePortfolioStore } from '@/stores/modules/portfolio'
import type { Portfolio } from '@/types/portfolio'

// Mock服务
vi.mock('@/services/portfolio.service', () => ({
  portfolioService: {
    getPortfolios: vi.fn(),
    getPositions: vi.fn(),
    getPerformance: vi.fn(),
    getAllocation: vi.fn(),
    createPortfolio: vi.fn(),
    updatePortfolio: vi.fn(),
    deletePortfolio: vi.fn()
  }
}))

vi.mock('@/services/api-manager.service', () => ({
  apiManager: {
    call: vi.fn()
  }
}))

describe('Portfolio Store', () => {
  let portfolioStore: ReturnType<typeof usePortfolioStore>

  const mockPortfolio: Portfolio = {
    id: 'test-portfolio-1',
    name: '测试投资组合',
    description: '用于测试的投资组合',
    totalValue: 1000000,
    totalCost: 800000,
    totalReturn: 200000,
    totalReturnRate: 0.25,
    dayReturn: 5000,
    dayReturnRate: 0.005,
    positions: 5,
    cash: 100000,
    type: 'balanced',
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z'
  }

  beforeEach(() => {
    // 设置Pinia
    setActivePinia(createPinia())
    portfolioStore = usePortfolioStore()
    
    // 清除所有mock
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(portfolioStore.portfolios).toEqual([])
      expect(portfolioStore.currentPortfolio).toBeNull()
      expect(portfolioStore.positions).toEqual([])
      expect(portfolioStore.performance).toBeNull()
      expect(portfolioStore.allocation).toEqual([])
      expect(portfolioStore.isLoading).toBe(false)
      expect(portfolioStore.error).toBeNull()
    })
  })

  describe('计算属性', () => {
    beforeEach(() => {
      portfolioStore.portfolios = [mockPortfolio]
    })

    it('totalPortfolioValue应该计算正确的总价值', () => {
      expect(portfolioStore.totalPortfolioValue).toBe(1000000)
    })

    it('totalPortfolioReturn应该计算正确的总收益', () => {
      expect(portfolioStore.totalPortfolioReturn).toBe(200000)
    })

    it('totalPortfolioReturnRate应该计算正确的总收益率', () => {
      expect(portfolioStore.totalPortfolioReturnRate).toBe(0.25)
    })

    it('activePortfolios应该只返回活跃的投资组合', () => {
      const inactivePortfolio = { ...mockPortfolio, id: 'inactive', status: 'inactive' as const }
      portfolioStore.portfolios = [mockPortfolio, inactivePortfolio]
      
      expect(portfolioStore.activePortfolios).toHaveLength(1)
      expect(portfolioStore.activePortfolios[0].status).toBe('active')
    })

    it('portfolioCount应该返回正确的投资组合数量', () => {
      expect(portfolioStore.portfolioCount).toBe(1)
    })
  })

  describe('Actions', () => {
    describe('fetchPortfolios', () => {
      it('应该成功获取投资组合列表', async () => {
        const { apiManager } = await import('@/services/api-manager.service')
        vi.mocked(apiManager.call).mockResolvedValue([mockPortfolio])

        await portfolioStore.fetchPortfolios()

        expect(portfolioStore.portfolios).toEqual([mockPortfolio])
        expect(portfolioStore.isLoading).toBe(false)
        expect(portfolioStore.error).toBeNull()
      })

      it('应该处理获取投资组合失败的情况', async () => {
        const { apiManager } = await import('@/services/api-manager.service')
        const errorMessage = '网络错误'
        vi.mocked(apiManager.call).mockRejectedValue(new Error(errorMessage))

        await expect(portfolioStore.fetchPortfolios()).rejects.toThrow(errorMessage)
        expect(portfolioStore.error).toBe(errorMessage)
        expect(portfolioStore.isLoading).toBe(false)
      })
    })

    describe('setCurrentPortfolio', () => {
      beforeEach(() => {
        portfolioStore.portfolios = [mockPortfolio]
      })

      it('应该能够设置当前投资组合', async () => {
        const { apiManager } = await import('@/services/api-manager.service')
        vi.mocked(apiManager.call).mockResolvedValue([])

        await portfolioStore.setCurrentPortfolio(mockPortfolio.id)

        expect(portfolioStore.currentPortfolio).toEqual(mockPortfolio)
      })

      it('设置不存在的投资组合ID应该抛出错误', async () => {
        await expect(
          portfolioStore.setCurrentPortfolio('non-existent-id')
        ).rejects.toThrow('投资组合不存在')
      })
    })

    describe('createPortfolio', () => {
      it('应该能够创建新的投资组合', async () => {
        const { apiManager } = await import('@/services/api-manager.service')
        const newPortfolioData = {
          name: '新投资组合',
          description: '新创建的投资组合',
          type: 'aggressive' as const
        }
        const createdPortfolio = { ...mockPortfolio, ...newPortfolioData, id: 'new-portfolio' }
        
        vi.mocked(apiManager.call).mockResolvedValue(createdPortfolio)

        const result = await portfolioStore.createPortfolio(newPortfolioData)

        expect(result).toEqual(createdPortfolio)
        expect(portfolioStore.portfolios).toContain(createdPortfolio)
      })
    })

    describe('updatePortfolio', () => {
      beforeEach(() => {
        portfolioStore.portfolios = [mockPortfolio]
      })

      it('应该能够更新投资组合', async () => {
        const { apiManager } = await import('@/services/api-manager.service')
        const updates = { name: '更新后的名称' }
        const updatedPortfolio = { ...mockPortfolio, ...updates }
        
        vi.mocked(apiManager.call).mockResolvedValue(updatedPortfolio)

        await portfolioStore.updatePortfolio(mockPortfolio.id, updates)

        expect(portfolioStore.portfolios[0].name).toBe(updates.name)
      })
    })

    describe('deletePortfolio', () => {
      beforeEach(() => {
        portfolioStore.portfolios = [mockPortfolio]
        portfolioStore.currentPortfolio = mockPortfolio
      })

      it('应该能够删除投资组合', async () => {
        const { apiManager } = await import('@/services/api-manager.service')
        vi.mocked(apiManager.call).mockResolvedValue(undefined)

        await portfolioStore.deletePortfolio(mockPortfolio.id)

        expect(portfolioStore.portfolios).toHaveLength(0)
        expect(portfolioStore.currentPortfolio).toBeNull()
      })
    })
  })

  describe('错误处理', () => {
    it('应该正确处理未知错误类型', async () => {
      const { apiManager } = await import('@/services/api-manager.service')
      vi.mocked(apiManager.call).mockRejectedValue('字符串错误')

      await expect(portfolioStore.fetchPortfolios()).rejects.toThrow()
      expect(portfolioStore.error).toBe('获取投资组合失败')
    })

    it('应该正确处理Error对象', async () => {
      const { apiManager } = await import('@/services/api-manager.service')
      const error = new Error('具体错误信息')
      vi.mocked(apiManager.call).mockRejectedValue(error)

      await expect(portfolioStore.fetchPortfolios()).rejects.toThrow('具体错误信息')
      expect(portfolioStore.error).toBe('具体错误信息')
    })
  })

  describe('加载状态管理', () => {
    it('在API调用期间应该设置loading状态', async () => {
      const { apiManager } = await import('@/services/api-manager.service')
      let resolvePromise: (value: any) => void
      const promise = new Promise(resolve => {
        resolvePromise = resolve
      })
      vi.mocked(apiManager.call).mockReturnValue(promise)

      const fetchPromise = portfolioStore.fetchPortfolios()
      
      // 在Promise解决之前，loading应该为true
      expect(portfolioStore.isLoading).toBe(true)
      
      // 解决Promise
      resolvePromise!([mockPortfolio])
      await fetchPromise
      
      // Promise解决后，loading应该为false
      expect(portfolioStore.isLoading).toBe(false)
    })
  })
})
