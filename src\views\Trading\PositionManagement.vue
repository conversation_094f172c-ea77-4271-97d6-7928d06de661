<template>
  <div class="position-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">持仓管理</h1>
          <div class="page-subtitle">
            <span>实时监控投资组合表现</span>
            <el-tag v-if="wsConnected" type="success" size="small">
              <el-icon><Connection /></el-icon>
              实时更新
            </el-tag>
            <el-tag v-else type="danger" size="small">
              <el-icon><Close /></el-icon>
              连接断开
            </el-tag>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            :loading="loading"
            @click="refreshAll"
            type="primary"
            :icon="Refresh"
          >
            刷新数据
          </el-button>
          <el-button
            @click="exportPositions"
            :icon="Download"
          >
            导出持仓
          </el-button>
          <el-dropdown @command="handleMoreActions">
            <el-button :icon="MoreFilled">
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="clearAll" :icon="Delete">一键清仓</el-dropdown-item>
                <el-dropdown-item command="rebalance" :icon="Refresh">资产再平衡</el-dropdown-item>
                <el-dropdown-item command="riskAnalysis" :icon="Warning">风险分析</el-dropdown-item>
                <el-dropdown-item command="performance" :icon="TrendCharts">绩效分析</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button
            @click="$router.push('/trading/terminal')"
            type="success"
            :icon="Plus"
          >
            快速交易
          </el-button>
        </div>
      </div>
    </div>

    <!-- 账户概览卡片 -->
    <div class="overview-section">
      <div class="overview-grid">
        <div class="overview-card total-assets">
          <div class="card-content">
            <div class="card-info">
              <div class="card-label">总资产</div>
              <div class="card-value">{{ formatCurrency(accountSummary.totalAssets) }}</div>
              <div class="card-trend">
                <span :class="getTrendClass(accountSummary.totalAssetsChange)">
                  {{ formatPnl(accountSummary.totalAssetsChange) }}
                  ({{ formatPercent(accountSummary.totalAssetsChangePercent) }})
                </span>
              </div>
            </div>
            <div class="card-icon">
              <el-icon><Wallet /></el-icon>
            </div>
          </div>
        </div>

        <div class="overview-card available-cash">
          <div class="card-content">
            <div class="card-info">
              <div class="card-label">可用资金</div>
              <div class="card-value">{{ formatCurrency(accountSummary.availableCash) }}</div>
              <div class="card-trend">
                <span class="trend-text">
                  占比 {{ formatPercent(accountSummary.cashRatio) }}
                </span>
              </div>
            </div>
            <div class="card-icon">
              <el-icon><Money /></el-icon>
            </div>
          </div>
        </div>

        <div class="overview-card today-pnl">
          <div class="card-content">
            <div class="card-info">
              <div class="card-label">今日盈亏</div>
              <div class="card-value" :class="getPnlClass(accountSummary.todayPnl)">
                {{ formatPnl(accountSummary.todayPnl) }}
              </div>
              <div class="card-trend">
                <span :class="getPnlClass(accountSummary.todayPnl)">
                  ({{ formatPercent(accountSummary.todayPnlPercent) }})
                </span>
              </div>
            </div>
            <div class="card-icon" :class="accountSummary.todayPnl >= 0 ? 'up' : 'down'">
              <el-icon><TrendCharts /></el-icon>
            </div>
          </div>
        </div>

        <div class="overview-card total-pnl">
          <div class="card-content">
            <div class="card-info">
              <div class="card-label">总盈亏</div>
              <div class="card-value" :class="getPnlClass(accountSummary.totalPnl)">
                {{ formatPnl(accountSummary.totalPnl) }}
              </div>
              <div class="card-trend">
                <span :class="getPnlClass(accountSummary.totalPnl)">
                  ({{ formatPercent(totalPnlPercent) }})
                </span>
              </div>
            </div>
            <div class="card-icon" :class="accountSummary.totalPnl >= 0 ? 'up' : 'down'">
              <el-icon><DataAnalysis /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 持仓统计和分析 -->
    <div class="statistics-section">
      <el-tabs v-model="activeStatsTab" type="card" class="stats-tabs">
        <el-tab-pane label="基础统计" name="basic">
          <div class="statistics-grid">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><PieChart /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ positions.length }}</div>
                <div class="stat-label">持仓股票</div>
                <div class="stat-change">较昨日 +2</div>
              </div>
            </div>
            
            <div class="stat-card profit">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ profitablePositions }}</div>
                <div class="stat-label">盈利股票</div>
                <div class="stat-change profit">胜率 {{ winRate }}%</div>
              </div>
            </div>
            
            <div class="stat-card loss">
              <div class="stat-icon">
                <el-icon><Bottom /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ losingPositions }}</div>
                <div class="stat-label">亏损股票</div>
                <div class="stat-change loss">平均亏损 {{ avgLossPercent }}%</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatCurrency(totalMarketValue) }}</div>
                <div class="stat-label">总市值</div>
                <div class="stat-change">仓位使用率 {{ positionUtilization }}%</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ sharpeRatio }}</div>
                <div class="stat-label">夏普比率</div>
                <div class="stat-change">风险调整收益</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ maxDrawdown }}%</div>
                <div class="stat-label">最大回撤</div>
                <div class="stat-change warning">需要关注</div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="行业分析" name="industry">
          <div class="industry-analysis">
            <div class="industry-chart-container">
              <div class="chart-section">
                <h3>行业分布</h3>
                <div class="industry-pie-chart">
                  <div class="pie-chart-placeholder">
                    <div class="pie-segment" v-for="(industry, index) in industryDistribution" :key="industry.name"
                         :style="{ '--segment-color': getIndustryColor(index) }">
                      <div class="segment-info">
                        <span class="segment-label">{{ industry.name }}</span>
                        <span class="segment-value">{{ industry.percentage }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="industry-details">
                <h3>行业详情</h3>
                <div class="industry-list">
                  <div class="industry-item" v-for="(industry, index) in industryDistribution" :key="industry.name">
                    <div class="industry-header">
                      <div class="industry-name">
                        <div class="color-indicator" :style="{ backgroundColor: getIndustryColor(index) }"></div>
                        <span>{{ industry.name }}</span>
                      </div>
                      <div class="industry-stats">
                        <span class="percentage">{{ industry.percentage }}%</span>
                        <span class="value">{{ formatCurrency(industry.value) }}</span>
                      </div>
                    </div>
                    <div class="industry-metrics">
                      <div class="metric">
                        <span class="metric-label">持仓数</span>
                        <span class="metric-value">{{ industry.count }}</span>
                      </div>
                      <div class="metric">
                        <span class="metric-label">盈亏</span>
                        <span class="metric-value" :class="getPnlClass(industry.pnl)">{{ formatPnl(industry.pnl) }}</span>
                      </div>
                      <div class="metric">
                        <span class="metric-label">收益率</span>
                        <span class="metric-value" :class="getPnlClass(industry.pnl)">{{ industry.returnRate }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="风险分析" name="risk">
          <div class="risk-analysis">
            <div class="risk-metrics-grid">
              <div class="risk-card">
                <div class="risk-header">
                  <h4>集中度风险</h4>
                  <el-tag :type="concentrationRisk.level === '高' ? 'danger' : concentrationRisk.level === '中' ? 'warning' : 'success'" size="small">{{ concentrationRisk.level }}</el-tag>
                </div>
                <div class="risk-content">
                  <div class="risk-indicator">
                    <div class="indicator-value">{{ concentrationRisk.score }}/100</div>
                    <div class="indicator-bar">
                      <div class="bar-fill" :style="{ width: concentrationRisk.score + '%', backgroundColor: getRiskColor(concentrationRisk.level) }"></div>
                    </div>
                  </div>
                  <div class="risk-description">{{ concentrationRisk.description }}</div>
                </div>
              </div>
              
              <div class="risk-card">
                <div class="risk-header">
                  <h4>流动性风险</h4>
                  <el-tag :type="liquidityRisk.level === '高' ? 'danger' : liquidityRisk.level === '中' ? 'warning' : 'success'" size="small">{{ liquidityRisk.level }}</el-tag>
                </div>
                <div class="risk-content">
                  <div class="risk-indicator">
                    <div class="indicator-value">{{ liquidityRisk.score }}/100</div>
                    <div class="indicator-bar">
                      <div class="bar-fill" :style="{ width: liquidityRisk.score + '%', backgroundColor: getRiskColor(liquidityRisk.level) }"></div>
                    </div>
                  </div>
                  <div class="risk-description">{{ liquidityRisk.description }}</div>
                </div>
              </div>
              
              <div class="risk-card">
                <div class="risk-header">
                  <h4>市场风险</h4>
                  <el-tag :type="marketRisk.level === '高' ? 'danger' : marketRisk.level === '中' ? 'warning' : 'success'" size="small">{{ marketRisk.level }}</el-tag>
                </div>
                <div class="risk-content">
                  <div class="risk-indicator">
                    <div class="indicator-value">{{ marketRisk.score }}/100</div>
                    <div class="indicator-bar">
                      <div class="bar-fill" :style="{ width: marketRisk.score + '%', backgroundColor: getRiskColor(marketRisk.level) }"></div>
                    </div>
                  </div>
                  <div class="risk-description">{{ marketRisk.description }}</div>
                </div>
              </div>
            </div>
            
            <div class="risk-suggestions">
              <h4>风险建议</h4>
              <div class="suggestions-list">
                <div class="suggestion-item" v-for="suggestion in riskSuggestions" :key="suggestion.id">
                  <div class="suggestion-type">
                    <el-tag :type="suggestion.priority === '高' ? 'danger' : suggestion.priority === '中' ? 'warning' : 'info'" size="small">{{ suggestion.priority }}</el-tag>
                  </div>
                  <div class="suggestion-content">
                    <div class="suggestion-title">{{ suggestion.title }}</div>
                    <div class="suggestion-description">{{ suggestion.description }}</div>
                  </div>
                  <div class="suggestion-action">
                    <el-button size="small" @click="applySuggestion(suggestion)">应用</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="绩效分析" name="performance">
          <div class="performance-analysis">
            <div class="performance-overview">
              <div class="performance-metrics">
                <div class="metric-card">
                  <div class="metric-title">累计收益率</div>
                  <div class="metric-value" :class="getPnlClass(totalReturnRate)">{{ totalReturnRate.toFixed(2) }}%</div>
                  <div class="metric-compare">同期沪深300: +12.5%</div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">年化收益率</div>
                  <div class="metric-value" :class="getPnlClass(annualizedReturn)">{{ annualizedReturn.toFixed(2) }}%</div>
                  <div class="metric-compare">同期基准: +8.2%</div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">波动率</div>
                  <div class="metric-value">{{ volatility.toFixed(2) }}%</div>
                  <div class="metric-compare">市场平均: 18.5%</div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">信息比率</div>
                  <div class="metric-value" :class="informationRatio >= 0 ? 'pnl-up' : 'pnl-down'">{{ informationRatio.toFixed(2) }}</div>
                  <div class="metric-compare">优秀水平: >0.5</div>
                </div>
              </div>
            </div>
            
            <div class="performance-chart">
              <h4>收益曲线对比</h4>
              <div class="chart-placeholder">
                <div class="chart-line portfolio">
                  <span class="line-label">组合收益</span>
                  <div class="line-data">{{ totalReturnRate.toFixed(2) }}%</div>
                </div>
                <div class="chart-line benchmark">
                  <span class="line-label">基准收益</span>
                  <div class="line-data">12.50%</div>
                </div>
                <div class="chart-legend">
                  <div class="legend-item">
                    <div class="legend-color portfolio"></div>
                    <span>投资组合</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color benchmark"></div>
                    <span>沪深300基准</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="performance-attribution">
              <h4>收益归因分析</h4>
              <div class="attribution-table">
                <div class="attribution-header">
                  <span>来源</span>
                  <span>贡献度</span>
                  <span>收益率</span>
                </div>
                <div class="attribution-row" v-for="attr in attributionData" :key="attr.source">
                  <span class="source">{{ attr.source }}</span>
                  <span class="contribution" :class="getPnlClass(attr.contribution)">{{ attr.contribution.toFixed(2) }}%</span>
                  <span class="return-rate" :class="getPnlClass(attr.returnRate)">{{ attr.returnRate.toFixed(2) }}%</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 筛选和操作栏 -->
    <div class="filter-section">
      <div class="filter-content">
        <div class="filter-left">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索股票代码或名称"
            size="default"
            style="width: 240px"
            clearable
            @input="handleSearchInput"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select
            v-model="filterPnlType"
            placeholder="盈亏筛选"
            size="default"
            style="width: 120px"
            @change="handleFilterChange"
          >
            <el-option label="全部" value="" />
            <el-option label="盈利" value="profit" />
            <el-option label="亏损" value="loss" />
            <el-option label="持平" value="neutral" />
          </el-select>
          
          <el-select
            v-model="sortField"
            placeholder="排序字段"
            size="default"
            style="width: 140px"
            @change="handleSortChange"
          >
            <el-option label="默认" value="" />
            <el-option label="盈亏金额" value="unrealizedPnl" />
            <el-option label="盈亏比例" value="unrealizedPnlPercent" />
            <el-option label="持仓市值" value="marketValue" />
            <el-option label="持仓数量" value="size" />
          </el-select>
          
          <el-button-group>
            <el-button
              :type="sortOrder === 'asc' ? 'primary' : 'default'"
              size="default"
              @click="setSortOrder('asc')"
            >
              <el-icon><Sort /></el-icon>
              升序
            </el-button>
            <el-button
              :type="sortOrder === 'desc' ? 'primary' : 'default'"
              size="default"
              @click="setSortOrder('desc')"
            >
              <el-icon><SortDown /></el-icon>
              降序
            </el-button>
          </el-button-group>
        </div>
        
        <div class="filter-right">
          <el-checkbox v-model="showOnlyProfit" @change="handleFilterChange">
            只看盈利
          </el-checkbox>
          <el-checkbox v-model="autoRefresh" @change="handleAutoRefreshChange">
            自动刷新
          </el-checkbox>
          <span class="result-count">
            共 {{ filteredPositions.length }} 条记录
          </span>
        </div>
      </div>
    </div>

    <!-- 持仓列表 -->
    <div class="table-section">
      <div v-loading="loading" class="table-container">
        <el-table
          ref="tableRef"
          :data="paginatedPositions"
          style="width: 100%"
          stripe
          border
          :height="tableHeight"
          @sort-change="handleTableSort"
          @selection-change="handleSelectionChange"
        >
          <!-- 选择列 -->
          <el-table-column
            type="selection"
            width="50"
            fixed="left"
          />
          
          <!-- 股票代码 -->
          <el-table-column
            prop="symbol"
            label="股票代码"
            width="100"
            fixed="left"
            sortable="custom"
          >
            <template #default="{ row }">
              <div class="symbol-cell">
                <span class="symbol-code">{{ row.symbol }}</span>
                <span class="symbol-name">{{ getStockName(row.symbol) }}</span>
              </div>
            </template>
          </el-table-column>

          <!-- 持仓数量 -->
          <el-table-column
            prop="size"
            label="持仓数量"
            width="100"
            align="right"
            sortable="custom"
          >
            <template #default="{ row }">
              <span class="number-value">{{ formatNumber(row.size) }}</span>
            </template>
          </el-table-column>

          <!-- 成本价 -->
          <el-table-column
            prop="avgPrice"
            label="成本价"
            width="100"
            align="right"
            sortable="custom"
          >
            <template #default="{ row }">
              <span class="price-value">{{ formatPrice(row.avgPrice) }}</span>
            </template>
          </el-table-column>

          <!-- 现价 -->
          <el-table-column
            prop="markPrice"
            label="现价"
            width="100"
            align="right"
            sortable="custom"
          >
            <template #default="{ row }">
              <span 
                class="price-value"
                :class="getPriceChangeClass(row.markPrice - row.avgPrice)"
              >
                {{ formatPrice(row.markPrice) }}
              </span>
            </template>
          </el-table-column>

          <!-- 市值 -->
          <el-table-column
            prop="marketValue"
            label="市值"
            width="120"
            align="right"
            sortable="custom"
          >
            <template #default="{ row }">
              <span class="currency-value">{{ formatCurrency(row.markPrice * row.size) }}</span>
            </template>
          </el-table-column>

          <!-- 浮动盈亏 -->
          <el-table-column
            prop="unrealizedPnl"
            label="浮动盈亏"
            width="140"
            align="right"
            sortable="custom"
          >
            <template #default="{ row }">
              <div class="pnl-cell">
                <div class="pnl-amount" :class="getPnlClass(row.unrealizedPnl)">
                  {{ formatPnl(row.unrealizedPnl) }}
                </div>
                <div class="pnl-percent" :class="getPnlClass(row.unrealizedPnl)">
                  ({{ formatPercent(row.unrealizedPnlPercent) }})
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 持仓占比 -->
          <el-table-column
            label="持仓占比"
            width="100"
            align="right"
          >
            <template #default="{ row }">
              <div class="ratio-cell">
                <span class="ratio-value">
                  {{ formatPercent(getPositionRatio(row)) }}
                </span>
                <div class="ratio-bar">
                  <div 
                    class="ratio-fill"
                    :style="{ width: `${Math.min(getPositionRatio(row), 100)}%` }"
                  />
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 更新时间 -->
          <el-table-column
            prop="updateTime"
            label="更新时间"
            width="120"
            align="center"
          >
            <template #default="{ row }">
              <span class="time-value">{{ formatTime(row.updateTime) }}</span>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column
            label="操作"
            width="160"
            fixed="right"
          >
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="buyStock(row.symbol, getStockName(row.symbol))"
                >
                  买入
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="sellStock(row)"
                >
                  卖出
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  text
                  @click="viewDetail(row)"
                >
                  详情
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态 -->
        <div v-if="!loading && filteredPositions.length === 0" class="empty-state">
          <el-empty description="暂无持仓数据">
            <el-button type="primary" @click="$router.push('/trading/terminal')">
              开始交易
            </el-button>
          </el-empty>
        </div>
      </div>

      <!-- 批量操作栏 -->
      <div v-if="selectedPositions.length > 0" class="batch-actions">
        <div class="batch-info">
          已选择 {{ selectedPositions.length }} 项
        </div>
        <div class="batch-buttons">
          <el-button @click="batchSell" type="danger">
            批量卖出
          </el-button>
          <el-button @click="batchExport">
            导出选中
          </el-button>
          <el-button @click="clearSelection" text>
            取消选择
          </el-button>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :small="false"
          :disabled="loading"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredPositions.length"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentPageChange"
        />
      </div>
    </div>

    <!-- 持仓详情弹窗 -->
    <el-dialog 
      v-model="showDetailDialog" 
      title="持仓详情" 
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedPosition" class="position-detail">
        <div class="detail-grid">
          <div class="detail-item">
            <label>股票代码</label>
            <span>{{ selectedPosition.symbol }}</span>
          </div>
          <div class="detail-item">
            <label>股票名称</label>
            <span>{{ getStockName(selectedPosition.symbol) }}</span>
          </div>
          <div class="detail-item">
            <label>持仓数量</label>
            <span>{{ formatNumber(selectedPosition.size) }}</span>
          </div>
          <div class="detail-item">
            <label>成本价</label>
            <span>{{ formatPrice(selectedPosition.avgPrice) }}</span>
          </div>
          <div class="detail-item">
            <label>现价</label>
            <span :class="getPriceChangeClass(selectedPosition.markPrice - selectedPosition.avgPrice)">
              {{ formatPrice(selectedPosition.markPrice) }}
            </span>
          </div>
          <div class="detail-item">
            <label>总成本</label>
            <span>{{ formatCurrency(selectedPosition.avgPrice * selectedPosition.size) }}</span>
          </div>
          <div class="detail-item">
            <label>市值</label>
            <span>{{ formatCurrency(selectedPosition.markPrice * selectedPosition.size) }}</span>
          </div>
          <div class="detail-item">
            <label>浮动盈亏</label>
            <span :class="getPnlClass(selectedPosition.unrealizedPnl)">
              {{ formatPnl(selectedPosition.unrealizedPnl) }}
              ({{ formatPercent(selectedPosition.unrealizedPnlPercent) }})
            </span>
          </div>
          <div class="detail-item">
            <label>持仓占比</label>
            <span>{{ formatPercent(getPositionRatio(selectedPosition)) }}</span>
          </div>
          <div class="detail-item">
            <label>更新时间</label>
            <span>{{ formatDateTime(selectedPosition.updateTime) }}</span>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="buyStock(selectedPosition?.symbol || '', getStockName(selectedPosition?.symbol || ''))">
            买入
          </el-button>
          <el-button type="danger" @click="sellStock(selectedPosition!)">
            卖出
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Connection,
  Close,
  Refresh,
  Download,
  Plus,
  Wallet, 
  Money, 
  TrendCharts, 
  DataAnalysis, 
  Search,
  PieChart,
  Bottom,
  MoreFilled,
  ArrowDown,
  Delete,
  Warning,
  Sort,
  SortDown
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { usePositions } from '@/composables/trading/usePositions'
import { usePagination } from '@/composables/data/usePagination'
import { formatCurrency, formatPrice, formatNumber, formatPercent } from '@/utils/formatters'
import { debounce } from 'lodash-es'
import type { Position } from '@/types/trading'

const router = useRouter()

// 使用持仓管理组合函数
const {
  loading,
  positions,
  accountSummary,
  totalPnl,
  totalPnlPercent,
  totalMarketValue,
  profitablePositions,
  losingPositions,
  wsConnected,
  enhancedStats,
  industryDistribution,
  riskIndicators,
  riskSuggestions,
  performanceAttribution,
  fetchPositions,
  fetchAccountSummary,
  buyStock,
  sellStock,
  refreshAll,
  exportPositions: exportPositionsData,
  subscribePositionUpdates,
  unsubscribePositionUpdates,
  getStockName,
  applyRiskSuggestion,
  updateEnhancedStats
} = usePositions()

// 本地状态
const searchKeyword = ref('')
const filterPnlType = ref('')
const showOnlyProfit = ref(false)
const autoRefresh = ref(false)
const sortField = ref('')
const sortOrder = ref<'asc' | 'desc'>('desc')
const showDetailDialog = ref(false)
const selectedPosition = ref<Position | null>(null)
const selectedPositions = ref<Position[]>([])
const tableRef = ref()
const tableHeight = ref(500)
const activeStatsTab = ref('basic')

// 计算属性 - 使用 composable 中的数据
const sharpeRatio = computed(() => enhancedStats.sharpeRatio)
const maxDrawdown = computed(() => enhancedStats.maxDrawdown)
const winRate = computed(() => enhancedStats.winRate)
const avgLossPercent = computed(() => enhancedStats.avgLossPercent)
const positionUtilization = computed(() => enhancedStats.positionUtilization)
const totalReturnRate = computed(() => enhancedStats.totalReturnRate)
const annualizedReturn = computed(() => enhancedStats.annualizedReturn)
const volatility = computed(() => enhancedStats.volatility)
const informationRatio = computed(() => enhancedStats.informationRatio)

// 风险指标计算属性
const concentrationRisk = computed(() => riskIndicators.concentration)
const liquidityRisk = computed(() => riskIndicators.liquidity)
const marketRisk = computed(() => riskIndicators.market)

// 绩效归因数据
const attributionData = computed(() => performanceAttribution)

// 使用 composable 中的 getStockName 方法

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const filteredPositions = computed(() => {
  let result = [...positions.value]
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(pos => 
      pos.symbol.toLowerCase().includes(keyword) ||
      getStockName(pos.symbol).toLowerCase().includes(keyword)
    )
  }
  
  // 盈亏类型过滤
  if (filterPnlType.value) {
    switch (filterPnlType.value) {
      case 'profit':
        result = result.filter(pos => pos.unrealizedPnl > 0)
        break
      case 'loss':
        result = result.filter(pos => pos.unrealizedPnl < 0)
        break
      case 'neutral':
        result = result.filter(pos => pos.unrealizedPnl === 0)
        break
    }
  }
  
  // 只看盈利过滤
  if (showOnlyProfit.value) {
    result = result.filter(pos => pos.unrealizedPnl > 0)
  }
  
  // 排序
  if (sortField.value) {
    result.sort((a, b) => {
      let aVal: number
      let bVal: number
      
      switch (sortField.value) {
        case 'marketValue':
          aVal = a.markPrice * a.size
          bVal = b.markPrice * b.size
          break
        default:
          aVal = a[sortField.value as keyof Position] as number
          bVal = b[sortField.value as keyof Position] as number
      }
      
      if (sortOrder.value === 'asc') {
        return aVal > bVal ? 1 : -1
      } else {
        return aVal < bVal ? 1 : -1
      }
    })
  }
  
  return result
})

const paginatedPositions = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredPositions.value.slice(start, end)
})

// 方法 - getStockName 已从 composable 中获取

const formatPnl = (value: number) => {
  const sign = value >= 0 ? '+' : ''
  return `${sign}${formatCurrency(value)}`
}

const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleTimeString()
}

const formatDateTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString()
}

const getTrendClass = (value: number) => {
  if (value > 0) return 'trend-up'
  if (value < 0) return 'trend-down'
  return 'trend-neutral'
}

const getPriceChangeClass = (change: number) => {
  if (change > 0) return 'price-up'
  if (change < 0) return 'price-down'
  return 'price-neutral'
}

const getPnlClass = (pnl: number) => {
  if (pnl > 0) return 'pnl-up'
  if (pnl < 0) return 'pnl-down'
  return 'pnl-neutral'
}

const getPositionRatio = (position: Position) => {
  const positionValue = position.markPrice * position.size
  return totalMarketValue.value > 0 ? (positionValue / totalMarketValue.value) * 100 : 0
}

const getIndustryColor = (index: number) => {
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#C71585', '#FF8C00']
  return colors[index % colors.length]
}

const getRiskColor = (level: string) => {
  const colors = {
    '低': '#67C23A',
    '中': '#E6A23C', 
    '高': '#F56C6C'
  }
  return colors[level as keyof typeof colors] || '#909399'
}

const applySuggestion = async (suggestion: any) => {
  await applyRiskSuggestion(suggestion)
}

const handleSearchInput = debounce(() => {
  pagination.value.currentPage = 1
}, 300)

const handleFilterChange = () => {
  pagination.value.currentPage = 1
}

const handleSortChange = () => {
  pagination.value.currentPage = 1
}

const setSortOrder = (order: 'asc' | 'desc') => {
  sortOrder.value = order
  handleSortChange()
}

const handleTableSort = ({ prop, order }: any) => {
  sortField.value = prop || ''
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
  handleSortChange()
}

const handleSelectionChange = (selection: Position[]) => {
  selectedPositions.value = selection
}

const handlePageSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
}

const handleCurrentPageChange = (page: number) => {
  pagination.value.currentPage = page
}

const handleAutoRefreshChange = (enabled: boolean) => {
  if (enabled) {
    // 启动自动刷新
    startAutoRefresh()
  } else {
    // 停止自动刷新
    stopAutoRefresh()
  }
}

let autoRefreshTimer: NodeJS.Timeout | null = null

const startAutoRefresh = () => {
  autoRefreshTimer = setInterval(() => {
    refreshAll()
  }, 30000) // 30秒刷新一次
}

const stopAutoRefresh = () => {
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer)
    autoRefreshTimer = null
  }
}

const handleMoreActions = (command: string) => {
  switch (command) {
    case 'clearAll':
      clearAllPositions()
      break
    case 'rebalance':
      ElMessage.info('资产再平衡功能开发中')
      break
    case 'riskAnalysis':
      ElMessage.info('风险分析功能开发中')
      break
    case 'performance':
      ElMessage.info('绩效分析功能开发中')
      break
  }
}

const clearAllPositions = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有持仓吗？此操作不可撤销。',
      '确认清仓',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('清仓订单已提交')
    await refreshAll()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清仓失败')
    }
  }
}

const exportPositions = async () => {
  try {
    await exportPositionsData(filteredPositions.value)
    ElMessage.success('持仓数据导出成功')
  } catch (error) {
    ElMessage.error('持仓数据导出失败')
  }
}

const batchSell = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要卖出选中的 ${selectedPositions.value.length} 只股票吗？`,
      '批量卖出确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('批量卖出订单已提交')
    clearSelection()
    await refreshAll()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量卖出失败')
    }
  }
}

const batchExport = async () => {
  try {
    await exportPositionsData(selectedPositions.value)
    ElMessage.success('选中数据导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const clearSelection = () => {
  tableRef.value?.clearSelection()
  selectedPositions.value = []
}

const viewDetail = (position: Position) => {
  selectedPosition.value = position
  showDetailDialog.value = true
}

// ======= 监听窗口大小，自动调整表格高度 =======
const updateTableHeight = () => {
  const windowHeight = window.innerHeight
  tableHeight.value = Math.max(400, windowHeight - 600)
}

// 生命周期
onMounted(() => {
  refreshAll()
  subscribePositionUpdates()
  updateEnhancedStats() // 更新增强统计数据

  updateTableHeight()
  window.addEventListener('resize', updateTableHeight)
})

onUnmounted(() => {
  unsubscribePositionUpdates()
  stopAutoRefresh()
  window.removeEventListener('resize', updateTableHeight)
})

// 监听筛选条件变化更新分页总数
watch(
  filteredPositions,
  (newData) => {
    pagination.value.total = newData.length
  },
  { immediate: true }
)
</script>

<style scoped>
.position-management-page {
  min-height: calc(100vh - 64px);
  background-color: #f5f5f5;
  padding: 20px;
}

/* 页面头部样式 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.page-subtitle {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 概览卡片样式 */
.overview-section {
  margin-bottom: 20px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.overview-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #409EFF;
}

.overview-card.total-assets {
  border-left-color: #409EFF;
}

.overview-card.available-cash {
  border-left-color: #67C23A;
}

.overview-card.today-pnl {
  border-left-color: #E6A23C;
}

.overview-card.total-pnl {
  border-left-color: #F56C6C;
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-info {
  flex: 1;
}

.card-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.card-trend {
  font-size: 14px;
}

.card-icon {
  font-size: 32px;
  color: #409EFF;
  opacity: 0.8;
}

.card-icon.up {
  color: #67C23A;
}

.card-icon.down {
  color: #F56C6C;
}

/* 统计和分析区域样式 */
.statistics-section {
  margin-bottom: 20px;
}

.stats-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-card.profit {
  border-left: 4px solid #67C23A;
}

.stat-card.loss {
  border-left: 4px solid #F56C6C;
}

.stat-icon {
  font-size: 24px;
  color: #409EFF;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.stat-change {
  font-size: 12px;
  color: #909399;
}

.stat-change.profit {
  color: #67C23A;
}

.stat-change.loss {
  color: #F56C6C;
}

.stat-change.warning {
  color: #E6A23C;
}

/* 行业分析样式 */
.industry-analysis {
  margin-top: 20px;
}

.industry-chart-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.chart-section h3,
.industry-details h3 {
  color: #333;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
}

.industry-pie-chart {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.pie-chart-placeholder {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: conic-gradient(
    #409EFF 0deg 126deg,
    #67C23A 126deg 216deg,
    #E6A23C 216deg 288deg,
    #F56C6C 288deg 342deg,
    #909399 342deg 360deg
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.pie-chart-placeholder::after {
  content: '';
  position: absolute;
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 50%;
}

.industry-list {
  max-height: 300px;
  overflow-y: auto;
}

.industry-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.industry-item:hover {
  border-color: #409EFF;
  background: #f8f9ff;
}

.industry-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.industry-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #333;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.industry-stats {
  display: flex;
  gap: 12px;
  align-items: center;
}

.percentage {
  font-weight: 600;
  color: #409EFF;
}

.value {
  color: #666;
  font-size: 14px;
}

.industry-metrics {
  display: flex;
  gap: 16px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.metric-label {
  font-size: 12px;
  color: #909399;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 风险分析样式 */
.risk-analysis {
  margin-top: 20px;
}

.risk-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.risk-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s;
}

.risk-card:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.risk-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.risk-header h4 {
  margin: 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.risk-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.risk-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
}

.indicator-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  min-width: 60px;
}

.indicator-bar {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s;
}

.risk-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.risk-suggestions {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.risk-suggestions h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
}

.suggestion-type {
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  font-size: 14px;
}

.suggestion-description {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

.suggestion-action {
  flex-shrink: 0;
}

/* 绩效分析样式 */
.performance-analysis {
  margin-top: 20px;
}

.performance-overview {
  margin-bottom: 24px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.metric-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  transition: all 0.3s;
}

.metric-card:hover {
  border-color: #409EFF;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.metric-compare {
  font-size: 12px;
  color: #909399;
}

.performance-chart {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.performance-chart h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.chart-placeholder {
  height: 200px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  position: relative;
}

.chart-line {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.chart-line.portfolio {
  color: #409EFF;
}

.chart-line.benchmark {
  color: #67C23A;
}

.line-label {
  font-weight: 600;
}

.line-data {
  font-size: 18px;
  font-weight: 700;
}

.chart-legend {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.portfolio {
  background: #409EFF;
}

.legend-color.benchmark {
  background: #67C23A;
}

.performance-attribution {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
}

.performance-attribution h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.attribution-table {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attribution-header {
  display: grid;
  grid-template-columns: 1fr 100px 100px;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 2px solid #e4e7ed;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.attribution-row {
  display: grid;
  grid-template-columns: 1fr 100px 100px;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  align-items: center;
}

.source {
  color: #333;
  font-weight: 500;
}

.contribution,
.return-rate {
  text-align: right;
  font-weight: 600;
}

/* 筛选区域样式 */
.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.result-count {
  font-size: 14px;
  color: #666;
}

/* 表格区域样式 */
.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-container {
  padding: 20px;
}

.symbol-cell {
  display: flex;
  flex-direction: column;
}

.symbol-code {
  font-weight: 600;
  color: #333;
}

.symbol-name {
  font-size: 12px;
  color: #666;
}

.pnl-cell {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.pnl-amount {
  font-weight: 600;
}

.pnl-percent {
  font-size: 12px;
}

.ratio-cell {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.ratio-bar {
  width: 60px;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.ratio-fill {
  height: 100%;
  background: #409EFF;
  transition: width 0.3s ease;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.number-value,
.price-value,
.currency-value,
.time-value {
  font-family: 'JetBrains Mono', monospace;
}

/* 价格和盈亏颜色 */
.price-up, .pnl-up, .trend-up {
  color: #f56c6c;
}

.price-down, .pnl-down, .trend-down {
  color: #67c23a;
}

.price-neutral, .pnl-neutral, .trend-neutral {
  color: #909399;
}

.trend-text {
  color: #666;
}

/* 批量操作栏 */
.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e8e8e8;
}

.batch-info {
  font-size: 14px;
  color: #666;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-section {
  padding: 20px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: center;
}

/* 空状态样式 */
.empty-state {
  padding: 60px 0;
  text-align: center;
}

/* 详情弹窗样式 */
.position-detail {
  padding: 20px 0;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item label {
  font-weight: 500;
  color: #666;
}

.detail-item span {
  font-weight: 600;
  color: #333;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .position-management-page {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .statistics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .industry-chart-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .risk-metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .performance-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .attribution-header,
  .attribution-row {
    grid-template-columns: 1fr 80px 80px;
    gap: 8px;
  }
  
  .filter-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-left {
    width: 100%;
    justify-content: space-between;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .batch-actions {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table--border td) {
  border-right: 1px solid #ebeef5;
}

:deep(.el-empty) {
  padding: 40px 0;
}
</style> 