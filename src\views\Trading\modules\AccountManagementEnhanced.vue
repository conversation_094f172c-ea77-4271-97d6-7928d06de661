<template>
  <div class="account-management-enhanced">
    <div class="module-header">
      <h2>账户管理</h2>
      <p class="module-description">管理您的交易账户、资金划转和账户信息</p>
    </div>

    <div class="account-content">
      <!-- 账户概览 -->
      <div class="account-overview">
        <div class="overview-cards">
          <div class="overview-card">
            <div class="card-icon">
              <el-icon><Wallet /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">总资产</div>
              <div class="card-value">¥{{ formatMoney(currentAccount.totalAssets) }}</div>
              <div class="card-change positive">+¥1,234.56</div>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="card-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">可用资金</div>
              <div class="card-value">¥{{ formatMoney(currentAccount.availableFunds) }}</div>
              <div class="card-change">可用于交易</div>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="card-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">持仓市值</div>
              <div class="card-value">¥{{ formatMoney(currentAccount.totalAssets - currentAccount.availableFunds) }}</div>
              <div class="card-change positive">+2.34%</div>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="card-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">今日盈亏</div>
              <div class="card-value positive">+¥567.89</div>
              <div class="card-change positive">+1.15%</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能区域 -->
      <div class="function-area">
        <el-tabs v-model="activeTab" type="card">
          <!-- 资金划转 -->
          <el-tab-pane label="资金划转" name="transfer">
            <div class="transfer-section">
              <el-tabs v-model="transferType" type="border-card">
                <el-tab-pane label="银证转账" name="bank-stock">
                  <div class="transfer-form">
                    <el-form :model="bankTransferForm" label-width="100px">
                      <el-form-item label="转账方向">
                        <el-radio-group v-model="bankTransferForm.direction">
                          <el-radio label="bank-to-stock">银行转证券</el-radio>
                          <el-radio label="stock-to-bank">证券转银行</el-radio>
                        </el-radio-group>
                      </el-form-item>
                      
                      <el-form-item label="银行账户">
                        <el-select v-model="bankTransferForm.bankAccount" style="width: 100%">
                          <el-option label="工商银行 ****1234" value="icbc_1234" />
                          <el-option label="建设银行 ****5678" value="ccb_5678" />
                          <el-option label="招商银行 ****9012" value="cmb_9012" />
                        </el-select>
                      </el-form-item>
                      
                      <el-form-item label="转账金额">
                        <el-input-number 
                          v-model="bankTransferForm.amount" 
                          :min="1" 
                          :max="1000000"
                          style="width: 100%"
                          placeholder="请输入转账金额"
                        />
                        <div class="transfer-tips">
                          <span v-if="bankTransferForm.direction === 'bank-to-stock'">
                            银行可用余额: ¥50,000.00
                          </span>
                          <span v-else>
                            证券可用资金: ¥{{ formatMoney(currentAccount.availableFunds) }}
                          </span>
                        </div>
                      </el-form-item>
                      
                      <el-form-item label="交易密码">
                        <el-input 
                          v-model="bankTransferForm.password" 
                          type="password" 
                          placeholder="请输入交易密码"
                          show-password
                        />
                      </el-form-item>
                      
                      <el-form-item>
                        <el-button 
                          type="primary" 
                          @click="handleBankTransfer"
                          :loading="transferLoading"
                          style="width: 100%"
                        >
                          确认转账
                        </el-button>
                      </el-form-item>
                    </el-form>
                  </div>
                </el-tab-pane>
                
                <el-tab-pane label="快速转账" name="quick-transfer">
                  <div class="quick-transfer">
                    <div class="quick-amounts">
                      <div class="amount-title">快速金额</div>
                      <div class="amount-buttons">
                        <el-button 
                          v-for="amount in quickAmounts" 
                          :key="amount"
                          @click="setQuickAmount(amount)"
                          :type="bankTransferForm.amount === amount ? 'primary' : 'default'"
                        >
                          ¥{{ formatMoney(amount) }}
                        </el-button>
                      </div>
                    </div>
                    
                    <div class="transfer-preview">
                      <div class="preview-title">转账预览</div>
                      <div class="preview-content">
                        <div class="preview-row">
                          <span>转账方向:</span>
                          <span>{{ bankTransferForm.direction === 'bank-to-stock' ? '银行→证券' : '证券→银行' }}</span>
                        </div>
                        <div class="preview-row">
                          <span>转账金额:</span>
                          <span class="amount">¥{{ formatMoney(bankTransferForm.amount || 0) }}</span>
                        </div>
                        <div class="preview-row">
                          <span>手续费:</span>
                          <span>免费</span>
                        </div>
                        <div class="preview-row total">
                          <span>实际到账:</span>
                          <span class="total-amount">¥{{ formatMoney(bankTransferForm.amount || 0) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-tab-pane>
          
          <!-- 账户设置 -->
          <el-tab-pane label="账户设置" name="settings">
            <div class="settings-section">
              <div class="setting-groups">
                <div class="setting-group">
                  <div class="group-title">
                    <el-icon><Lock /></el-icon>
                    安全设置
                  </div>
                  <div class="setting-items">
                    <div class="setting-item">
                      <div class="item-info">
                        <div class="item-title">登录密码</div>
                        <div class="item-desc">用于登录系统的密码</div>
                      </div>
                      <el-button type="primary" plain>修改</el-button>
                    </div>
                    
                    <div class="setting-item">
                      <div class="item-info">
                        <div class="item-title">交易密码</div>
                        <div class="item-desc">用于交易和资金操作的密码</div>
                      </div>
                      <el-button type="primary" plain>修改</el-button>
                    </div>
                    
                    <div class="setting-item">
                      <div class="item-info">
                        <div class="item-title">手机绑定</div>
                        <div class="item-desc">138****5678</div>
                      </div>
                      <el-button type="primary" plain>更换</el-button>
                    </div>
                  </div>
                </div>
                
                <div class="setting-group">
                  <div class="group-title">
                    <el-icon><Setting /></el-icon>
                    交易设置
                  </div>
                  <div class="setting-items">
                    <div class="setting-item">
                      <div class="item-info">
                        <div class="item-title">默认交易类型</div>
                        <div class="item-desc">新订单的默认类型</div>
                      </div>
                      <el-select v-model="tradingSettings.defaultOrderType" style="width: 120px">
                        <el-option label="限价单" value="limit" />
                        <el-option label="市价单" value="market" />
                      </el-select>
                    </div>
                    
                    <div class="setting-item">
                      <div class="item-info">
                        <div class="item-title">风险提醒</div>
                        <div class="item-desc">大额交易时显示风险提醒</div>
                      </div>
                      <el-switch v-model="tradingSettings.riskWarning" />
                    </div>
                    
                    <div class="setting-item">
                      <div class="item-info">
                        <div class="item-title">声音提醒</div>
                        <div class="item-desc">成交时播放提示音</div>
                      </div>
                      <el-switch v-model="tradingSettings.soundAlert" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 账户信息 -->
          <el-tab-pane label="账户信息" name="info">
            <div class="info-section">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="账户ID">{{ currentAccount.id }}</el-descriptions-item>
                <el-descriptions-item label="账户名称">{{ currentAccount.name }}</el-descriptions-item>
                <el-descriptions-item label="账户类型">
                  <el-tag :type="currentAccount.type === 'real' ? 'danger' : 'success'">
                    {{ currentAccount.type === 'real' ? '实盘账户' : '模拟账户' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="账户状态">
                  <el-tag :type="currentAccount.status === 'active' ? 'success' : 'warning'">
                    {{ currentAccount.status === 'active' ? '正常' : '异常' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="开户时间">2024-01-15 10:30:00</el-descriptions-item>
                <el-descriptions-item label="最后登录">{{ new Date().toLocaleString() }}</el-descriptions-item>
                <el-descriptions-item label="风险等级">
                  <el-tag type="warning">稳健型</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="客户经理">张经理 (138-0000-1234)</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Wallet, Money, TrendCharts, Calendar, Lock, Setting } from '@element-plus/icons-vue'

// Props
interface Props {
  currentAccount: {
    id: string
    type: string
    name: string
    availableFunds: number
    totalAssets: number
    status: string
  }
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  fundsTransfer: [transferData: any]
  settingsChange: [settings: any]
}>()

// 响应式数据
const activeTab = ref('transfer')
const transferType = ref('bank-stock')
const transferLoading = ref(false)

const bankTransferForm = reactive({
  direction: 'bank-to-stock',
  amount: 0,
  bankAccount: 'icbc_1234',
  password: ''
})

const tradingSettings = reactive({
  defaultOrderType: 'limit',
  riskWarning: true,
  soundAlert: true
})

const quickAmounts = [1000, 5000, 10000, 20000, 50000]

// 计算属性
const formatMoney = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

// 方法
const setQuickAmount = (amount: number) => {
  bankTransferForm.amount = amount
}

const handleBankTransfer = async () => {
  if (!bankTransferForm.amount || bankTransferForm.amount <= 0) {
    ElMessage.warning('请输入有效的转账金额')
    return
  }
  
  if (!bankTransferForm.password) {
    ElMessage.warning('请输入交易密码')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确认${bankTransferForm.direction === 'bank-to-stock' ? '银行转证券' : '证券转银行'} ¥${formatMoney(bankTransferForm.amount)}？`,
      '确认转账',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    transferLoading.value = true
    
    // 模拟转账延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const transferData = {
      type: bankTransferForm.direction,
      amount: bankTransferForm.amount,
      bankAccount: bankTransferForm.bankAccount,
      timestamp: new Date().toISOString()
    }
    
    emit('fundsTransfer', transferData)
    
    ElMessage.success('转账成功')
    
    // 重置表单
    bankTransferForm.amount = 0
    bankTransferForm.password = ''
    
  } catch (error) {
    ElMessage.info('已取消转账')
  } finally {
    transferLoading.value = false
  }
}
</script>

<style scoped lang="scss">
.account-management-enhanced {
  height: 100%;
  overflow-y: auto;
}

.module-header {
  margin-bottom: 24px;
  
  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: #303133;
  }
  
  .module-description {
    margin: 0;
    color: #606266;
    font-size: 14px;
  }
}

.account-overview {
  margin-bottom: 32px;
  
  .overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    
    .overview-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 16px;
      
      .card-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
      }
      
      .card-content {
        flex: 1;
        
        .card-title {
          font-size: 14px;
          color: #909399;
          margin-bottom: 8px;
        }
        
        .card-value {
          font-size: 24px;
          font-weight: 700;
          color: #303133;
          margin-bottom: 4px;
          
          &.positive {
            color: #f56c6c;
          }
        }
        
        .card-change {
          font-size: 12px;
          color: #606266;
          
          &.positive {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

.function-area {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .transfer-section {
    padding: 20px;
    
    .transfer-form {
      max-width: 500px;
      
      .transfer-tips {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }
    }
    
    .quick-transfer {
      .quick-amounts {
        margin-bottom: 24px;
        
        .amount-title {
          font-weight: 600;
          margin-bottom: 12px;
          color: #303133;
        }
        
        .amount-buttons {
          display: flex;
          gap: 12px;
          flex-wrap: wrap;
        }
      }
      
      .transfer-preview {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 16px;
        
        .preview-title {
          font-weight: 600;
          margin-bottom: 12px;
          color: #303133;
        }
        
        .preview-content {
          .preview-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            &.total {
              border-top: 1px solid #e4e7ed;
              padding-top: 8px;
              font-weight: 600;
              
              .total-amount {
                color: #409eff;
                font-size: 16px;
              }
            }
            
            .amount {
              color: #303133;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
  
  .settings-section {
    padding: 20px;
    
    .setting-groups {
      display: flex;
      flex-direction: column;
      gap: 32px;
      
      .setting-group {
        .group-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 16px;
        }
        
        .setting-items {
          display: flex;
          flex-direction: column;
          gap: 16px;
          
          .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            
            .item-info {
              .item-title {
                font-weight: 500;
                color: #303133;
                margin-bottom: 4px;
              }
              
              .item-desc {
                font-size: 14px;
                color: #606266;
              }
            }
          }
        }
      }
    }
  }
  
  .info-section {
    padding: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }
  
  .quick-amounts {
    .amount-buttons {
      justify-content: center;
    }
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 12px;
  }
}
</style>
