<template>
  <div class="alert-notifications">
    <div class="header">
      <h1 class="title">告警通知</h1>
      <div class="filters">
        <el-select v-model="selectedLevel" placeholder="告警级别" size="small" @change="fetchAlerts">
          <el-option label="全部" value="" />
          <el-option label="高危" value="high" />
          <el-option label="中等" value="medium" />
          <el-option label="低危" value="low" />
        </el-select>
        <el-select v-model="selectedStatus" placeholder="状态" size="small" @change="fetchAlerts">
          <el-option label="全部" value="" />
          <el-option label="未处理" value="unread" />
          <el-option label="已读" value="read" />
          <el-option label="已处理" value="handled" />
        </el-select>
        <el-button type="primary" size="small" @click="markAllAsRead">
          全部标为已读
        </el-button>
      </div>
    </div>

    <el-card v-loading="loading" class="notifications-card">
      <div v-if="alerts.length === 0" class="empty-state">
        <el-empty description="暂无告警通知" />
      </div>
      
      <div v-else class="alerts-list">
        <div 
          v-for="alert in alerts" 
          :key="alert.id"
          class="alert-item"
          :class="[
            `alert-${alert.level}`, 
            { 'unread': alert.status === 'unread' }
          ]"
          @click="handleAlertClick(alert)"
        >
          <div class="alert-icon">
            <el-icon :size="20">
              <WarningFilled v-if="alert.level === 'high'" />
              <Warning v-else-if="alert.level === 'medium'" />
              <InfoFilled v-else />
            </el-icon>
          </div>
          
          <div class="alert-content">
            <div class="alert-header">
              <h3 class="alert-title">{{ alert.title }}</h3>
              <span class="alert-level">{{ getLevelLabel(alert.level) }}</span>
            </div>
            
            <p class="alert-message">{{ alert.message }}</p>
            
            <div class="alert-meta">
              <span class="alert-time">{{ formatTime(alert.created_at) }}</span>
              <span class="alert-status">{{ getStatusLabel(alert.status) }}</span>
            </div>
          </div>
          
          <div class="alert-actions">
            <el-button 
              v-if="alert.status === 'unread'" 
              type="text" 
              size="small"
              @click.stop="markAsRead(alert.id)"
            >
              标为已读
            </el-button>
            
            <el-button 
              v-if="alert.status !== 'handled'" 
              type="text" 
              size="small"
              @click.stop="markAsHandled(alert.id)"
            >
              标为已处理
            </el-button>
          </div>
        </div>
      </div>
      
      <div v-if="alerts.length > 0" class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="prev, pager, next, sizes, total"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { WarningFilled, Warning, InfoFilled } from '@element-plus/icons-vue'

interface Alert {
  id: string
  title: string
  message: string
  level: 'high' | 'medium' | 'low'
  status: 'unread' | 'read' | 'handled'
  created_at: string
}

const loading = ref(false)
const alerts = ref<Alert[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const selectedLevel = ref('')
const selectedStatus = ref('')

const mockAlerts: Alert[] = [
  {
    id: '1',
    title: '风险预警',
    message: '账户持仓风险度达到80%，请注意风险控制',
    level: 'high',
    status: 'unread',
    created_at: '2024-01-15 10:30:00'
  },
  {
    id: '2',
    title: '价格异动',
    message: 'AAPL股价下跌超过5%，触发预设告警',
    level: 'medium',
    status: 'read',
    created_at: '2024-01-15 09:15:00'
  },
  {
    id: '3',
    title: '系统通知',
    message: '交易系统将于今晚22:00-23:00进行维护升级',
    level: 'low',
    status: 'handled',
    created_at: '2024-01-14 16:45:00'
  }
]

const fetchAlerts = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    let filteredAlerts = [...mockAlerts]
    
    if (selectedLevel.value) {
      filteredAlerts = filteredAlerts.filter(alert => alert.level === selectedLevel.value)
    }
    
    if (selectedStatus.value) {
      filteredAlerts = filteredAlerts.filter(alert => alert.status === selectedStatus.value)
    }
    
    alerts.value = filteredAlerts
    total.value = filteredAlerts.length
    
  } catch (error) {
    ElMessage.error('获取告警通知失败')
    console.error('Failed to fetch alerts:', error)
  } finally {
    loading.value = false
  }
}

const handleAlertClick = (alert: Alert) => {
  if (alert.status === 'unread') {
    markAsRead(alert.id)
  }
}

const markAsRead = async (alertId: string) => {
  try {
    const alertIndex = alerts.value.findIndex(a => a.id === alertId)
    if (alertIndex !== -1) {
      alerts.value[alertIndex].status = 'read'
      ElMessage.success('已标记为已读')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const markAsHandled = async (alertId: string) => {
  try {
    const alertIndex = alerts.value.findIndex(a => a.id === alertId)
    if (alertIndex !== -1) {
      alerts.value[alertIndex].status = 'handled'
      ElMessage.success('已标记为已处理')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const markAllAsRead = async () => {
  try {
    alerts.value.forEach(alert => {
      if (alert.status === 'unread') {
        alert.status = 'read'
      }
    })
    ElMessage.success('已全部标记为已读')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchAlerts()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchAlerts()
}

const getLevelLabel = (level: string) => {
  const labels = {
    high: '高危',
    medium: '中等',
    low: '低危'
  }
  return labels[level] || level
}

const getStatusLabel = (status: string) => {
  const labels = {
    unread: '未读',
    read: '已读', 
    handled: '已处理'
  }
  return labels[status] || status
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchAlerts()
})
</script>

<style scoped>
.alert-notifications {
  padding: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.notifications-card {
  min-height: 400px;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.alert-item:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.alert-item.unread {
  border-left: 4px solid #409eff;
  background-color: #f0f9ff;
}

.alert-high .alert-icon {
  color: #f56c6c;
}

.alert-medium .alert-icon {
  color: #e6a23c;
}

.alert-low .alert-icon {
  color: #409eff;
}

.alert-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.alert-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.alert-level {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background-color: #f0f2f5;
}

.alert-high .alert-level {
  background-color: #fee;
  color: #f56c6c;
}

.alert-medium .alert-level {
  background-color: #fef6ec;
  color: #e6a23c;
}

.alert-low .alert-level {
  background-color: #ecf5ff;
  color: #409eff;
}

.alert-message {
  color: #606266;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.alert-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.alert-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.empty-state {
  padding: 60px 0;
}

.pagination {
  margin-top: 24px;
  text-align: center;
}
</style> 