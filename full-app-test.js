/**
 * 测试完整Vue应用加载功能
 */

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class FullAppTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.issues = [];
  }

  async init() {
    console.log('🚀 启动完整应用测试...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    this.page = await this.browser.newPage();
    
    // 监听控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`[${type.toUpperCase()}] ${text}`);
      
      if (type === 'error') {
        this.issues.push({
          type: 'console_error',
          message: text,
          timestamp: new Date().toISOString()
        });
      }
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      console.error('❌ 页面错误:', error.message);
      this.issues.push({
        type: 'page_error',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });
    });

    // 监听网络请求
    this.page.on('response', response => {
      if (!response.ok()) {
        console.warn(`⚠️ HTTP ${response.status()}: ${response.url()}`);
      }
    });
  }

  async takeScreenshot(name) {
    const filename = `full-app-${name}-${Date.now()}.png`;
    const filepath = path.join(__dirname, 'test-screenshots', filename);
    
    const dir = path.dirname(filepath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    await this.page.screenshot({ path: filepath, fullPage: true });
    console.log(`📸 截图: ${filename}`);
    return filepath;
  }

  async loadFullApp() {
    console.log('\n🔄 测试完整应用加载...');
    
    // 1. 导航到首页
    await this.page.goto('http://localhost:5173', { waitUntil: 'networkidle0' });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 截图初始状态
    await this.takeScreenshot('01-initial');
    
    // 2. 点击"加载完整应用"按钮
    console.log('查找"加载完整应用"按钮...');
    
    // 等待按钮出现
    await this.page.waitForFunction(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      return buttons.some(btn => btn.textContent.includes('加载完整应用'));
    }, { timeout: 10000 });
    
    const loadFullAppButton = await this.page.evaluateHandle(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      return buttons.find(btn => btn.textContent.includes('加载完整应用'));
    });
    
    if (loadFullAppButton.asElement()) {
      console.log('✅ 找到"加载完整应用"按钮，点击...');
      await loadFullAppButton.asElement().click();
      
      // 等待应用加载
      console.log('等待完整应用加载...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      // 截图加载后状态
      await this.takeScreenshot('02-full-app-loaded');
      
      return true;
    } else {
      console.log('❌ 未找到"加载完整应用"按钮');
      return false;
    }
  }

  async testFullAppNavigation() {
    console.log('\n🧭 测试完整应用导航...');
    
    // 查找各种可能的导航元素
    const navSelectors = [
      '.navbar-nav',
      '.nav-menu', 
      '.sidebar-nav',
      '.top-navbar',
      'nav',
      '.navigation',
      '.menu',
      '.el-menu',
      '.router-link',
      'a[href^="/"]'
    ];
    
    let foundNav = false;
    for (const selector of navSelectors) {
      const navElements = await this.page.$$(selector);
      if (navElements.length > 0) {
        console.log(`✅ 找到导航元素: ${selector} (${navElements.length}个)`);
        foundNav = true;
      }
    }
    
    if (!foundNav) {
      console.log('❌ 未找到任何导航元素');
      return false;
    }
    
    // 获取所有可能的导航链接
    const navLinks = await this.page.evaluate(() => {
      const links = Array.from(document.querySelectorAll('a[href], .nav-item, .menu-item, .router-link'));
      return links.map(link => ({
        text: link.textContent.trim(),
        href: link.getAttribute('href') || link.getAttribute('to'),
        className: link.className,
        visible: link.offsetParent !== null
      })).filter(link => link.text && link.visible && link.text.length > 0);
    });
    
    console.log(`找到 ${navLinks.length} 个导航链接:`);
    navLinks.forEach((link, index) => {
      console.log(`  ${index + 1}. "${link.text}" -> ${link.href || '无链接'}`);
    });
    
    // 测试前几个导航链接
    for (let i = 0; i < Math.min(navLinks.length, 6); i++) {
      await this.testNavigationLink(navLinks[i], i + 1);
    }
    
    return true;
  }

  async testNavigationLink(link, index) {
    try {
      console.log(`\n📍 测试导航 ${index}: "${link.text}"`);
      
      // 尝试点击链接
      const linkElement = await this.page.evaluateHandle((linkText) => {
        const elements = Array.from(document.querySelectorAll('a, .nav-item, .menu-item, .router-link'));
        return elements.find(el => el.textContent.trim() === linkText);
      }, link.text);
      
      if (linkElement.asElement()) {
        await linkElement.asElement().click();
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 截图
        const pageName = link.text.toLowerCase().replace(/[^a-z0-9]/g, '-');
        await this.takeScreenshot(`nav-${index}-${pageName}`);
        
        // 检查页面变化
        const currentUrl = this.page.url();
        const pageTitle = await this.page.title();
        
        console.log(`  URL: ${currentUrl}`);
        console.log(`  标题: ${pageTitle}`);
        
        // 检查页面内容
        const pageInfo = await this.page.evaluate(() => {
          const body = document.body;
          const main = document.querySelector('main, .main-content, .content, .page-content');
          
          return {
            bodyTextLength: body.textContent.trim().length,
            hasMainContent: main !== null,
            mainContentLength: main ? main.textContent.trim().length : 0,
            hasError: body.textContent.toLowerCase().includes('error') ||
                     body.textContent.toLowerCase().includes('404') ||
                     body.textContent.includes('开发中') ||
                     body.textContent.includes('正在开发'),
            hasTable: document.querySelector('table, .el-table') !== null,
            hasChart: document.querySelector('.chart, .echarts, canvas') !== null,
            hasForm: document.querySelector('form, .el-form') !== null,
            hasButtons: document.querySelectorAll('button').length
          };
        });
        
        console.log(`  内容长度: ${pageInfo.bodyTextLength} 字符`);
        console.log(`  主要内容: ${pageInfo.hasMainContent ? '有' : '无'}`);
        console.log(`  表格: ${pageInfo.hasTable ? '有' : '无'}`);
        console.log(`  图表: ${pageInfo.hasChart ? '有' : '无'}`);
        console.log(`  表单: ${pageInfo.hasForm ? '有' : '无'}`);
        console.log(`  按钮数: ${pageInfo.hasButtons}`);
        
        if (pageInfo.hasError) {
          console.log('  ⚠️ 页面显示开发中或错误信息');
        }
        
        // 测试页面交互
        await this.testPageInteraction(link.text, pageInfo);
        
      } else {
        console.log('  ❌ 无法点击该导航项');
      }
      
    } catch (error) {
      console.log(`  ❌ 导航测试失败: ${error.message}`);
    }
  }

  async testPageInteraction(pageName, pageInfo) {
    console.log(`  🔧 测试 "${pageName}" 页面交互...`);
    
    try {
      // 测试按钮
      if (pageInfo.hasButtons > 0) {
        const buttons = await this.page.$$('button:not([disabled])');
        const testCount = Math.min(buttons.length, 2);
        
        for (let i = 0; i < testCount; i++) {
          try {
            const buttonText = await buttons[i].evaluate(el => el.textContent.trim());
            console.log(`    点击按钮: "${buttonText}"`);
            
            await buttons[i].click();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 检查是否有弹窗
            const hasModal = await this.page.$('.el-dialog, .modal, .popup, .el-message-box');
            if (hasModal) {
              console.log(`      ✅ 触发了弹窗/对话框`);
              
              // 尝试关闭弹窗
              const closeBtn = await this.page.$('.el-dialog__close, .modal-close, .close, .el-message-box__close');
              if (closeBtn) {
                await closeBtn.click();
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            }
          } catch (error) {
            console.log(`      ❌ 按钮测试失败: ${error.message}`);
          }
        }
      }
      
      // 测试输入框
      const inputs = await this.page.$$('input:not([disabled]), textarea:not([disabled])');
      if (inputs.length > 0) {
        const testInput = inputs[0];
        try {
          const placeholder = await testInput.evaluate(el => el.placeholder || '输入框');
          console.log(`    测试输入框: "${placeholder}"`);
          
          await testInput.click();
          await testInput.clear();
          await testInput.type('测试数据');
          await new Promise(resolve => setTimeout(resolve, 500));
          
          const value = await testInput.evaluate(el => el.value);
          console.log(`      输入值: "${value}"`);
        } catch (error) {
          console.log(`      ❌ 输入框测试失败: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.log(`    ❌ 页面交互测试失败: ${error.message}`);
    }
  }

  async testApiConnection() {
    console.log('\n🔧 测试API连接...');
    
    try {
      // 查找"测试API"按钮
      const testApiButton = await this.page.evaluateHandle(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        return buttons.find(btn => btn.textContent.includes('测试API'));
      });
      
      if (testApiButton.asElement()) {
        console.log('✅ 找到"测试API"按钮，点击...');
        await testApiButton.asElement().click();
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 检查API测试结果
        const apiResult = await this.page.evaluate(() => {
          const statusElement = document.querySelector('#status, .status');
          return statusElement ? statusElement.textContent.trim() : '无状态信息';
        });
        
        console.log(`API测试结果: ${apiResult}`);
        
        await this.takeScreenshot('api-test-result');
        
      } else {
        console.log('❌ 未找到"测试API"按钮');
      }
    } catch (error) {
      console.log(`❌ API测试失败: ${error.message}`);
    }
  }

  async generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      issues: this.issues,
      summary: {
        totalIssues: this.issues.length,
        consoleErrors: this.issues.filter(i => i.type === 'console_error').length,
        pageErrors: this.issues.filter(i => i.type === 'page_error').length
      }
    };
    
    const reportPath = path.join(__dirname, `full-app-test-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 完整应用测试报告:');
    console.log(`总问题数: ${report.summary.totalIssues}`);
    console.log(`控制台错误: ${report.summary.consoleErrors}`);
    console.log(`页面错误: ${report.summary.pageErrors}`);
    console.log(`详细报告: ${reportPath}`);
    
    return report;
  }

  async runTest() {
    try {
      await this.init();
      
      // 1. 加载完整应用
      const loaded = await this.loadFullApp();
      
      if (loaded) {
        // 2. 测试导航
        await this.testFullAppNavigation();
        
        // 3. 测试API连接
        await this.testApiConnection();
      }
      
      // 4. 生成报告
      await this.generateReport();
      
      console.log('\n🎉 完整应用测试完成！');
      
    } catch (error) {
      console.error('❌ 测试失败:', error);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// 运行测试
const tester = new FullAppTester();
tester.runTest().catch(console.error);
