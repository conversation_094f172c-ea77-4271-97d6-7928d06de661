<template>
  <div class="trading-terminal-enhanced">
    <!-- 交易模式切换 -->
    <div class="mode-switcher">
      <el-radio-group v-model="tradingMode" size="large" @change="handleModeChange">
        <el-radio-button label="simulation">
          <el-icon><Cpu /></el-icon>
          模拟交易
        </el-radio-button>
        <el-radio-button label="real">
          <el-icon><Money /></el-icon>
          实盘交易
        </el-radio-button>
      </el-radio-group>
      
      <div class="mode-info">
        <el-alert
          v-if="tradingMode === 'simulation'"
          title="当前为模拟交易模式，使用虚拟资金进行交易练习"
          type="info"
          :closable="false"
          show-icon
        />
        <el-alert
          v-else
          title="当前为实盘交易模式，请谨慎操作，交易将产生实际资金变动"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <!-- 主交易界面 -->
    <div class="terminal-layout">
      <!-- 左侧：股票搜索和行情 -->
      <div class="left-panel">
        <div class="stock-search">
          <el-autocomplete
            v-model="selectedSymbol"
            :fetch-suggestions="searchStocks"
            placeholder="搜索股票代码或名称"
            style="width: 100%"
            @select="handleSymbolSelect"
          >
            <template #default="{ item }">
              <div class="stock-suggestion">
                <span class="stock-code">{{ item.symbol }}</span>
                <span class="stock-name">{{ item.name }}</span>
                <span class="stock-price" :class="getPriceClass(item.changePercent)">
                  {{ formatPrice(item.currentPrice) }}
                </span>
              </div>
            </template>
          </el-autocomplete>
        </div>

        <!-- 当前股票信息 -->
        <div v-if="currentStock" class="stock-info">
          <div class="stock-header">
            <h3>{{ currentStock.symbol }} {{ currentStock.name }}</h3>
            <el-tag :type="currentStock.changePercent >= 0 ? 'danger' : 'success'">
              {{ currentStock.changePercent >= 0 ? '涨' : '跌' }}
            </el-tag>
          </div>
          
          <div class="price-info">
            <div class="current-price" :class="getPriceClass(currentStock.changePercent)">
              ¥{{ formatPrice(currentStock.currentPrice) }}
            </div>
            <div class="price-change">
              <span :class="getPriceClass(currentStock.changePercent)">
                {{ currentStock.changePercent >= 0 ? '+' : '' }}{{ currentStock.changePercent.toFixed(2) }}%
              </span>
              <span :class="getPriceClass(currentStock.changePercent)">
                {{ currentStock.changePercent >= 0 ? '+' : '' }}{{ formatPrice(currentStock.change) }}
              </span>
            </div>
          </div>

          <!-- 五档行情 -->
          <div class="market-depth">
            <div class="depth-header">五档行情</div>
            <div class="depth-content">
              <div v-for="(item, index) in marketDepth.sell" :key="`sell-${index}`" class="depth-row sell">
                <span class="label">卖{{ 5 - index }}</span>
                <span class="price">{{ formatPrice(item.price) }}</span>
                <span class="volume">{{ item.volume }}</span>
              </div>
              <div class="current-price-row">
                <span class="current-price-label">现价</span>
                <span class="current-price-value" :class="getPriceClass(currentStock.changePercent)">
                  {{ formatPrice(currentStock.currentPrice) }}
                </span>
              </div>
              <div v-for="(item, index) in marketDepth.buy" :key="`buy-${index}`" class="depth-row buy">
                <span class="label">买{{ index + 1 }}</span>
                <span class="price">{{ formatPrice(item.price) }}</span>
                <span class="volume">{{ item.volume }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：交易面板 -->
      <div class="right-panel">
        <div class="trading-panel">
          <el-tabs v-model="activeTab" type="card">
            <el-tab-pane label="买入" name="buy">
              <div class="trading-form">
                <el-form :model="buyForm" label-width="80px">
                  <el-form-item label="交易类型">
                    <el-radio-group v-model="buyForm.type">
                      <el-radio label="limit">限价单</el-radio>
                      <el-radio label="market">市价单</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  
                  <el-form-item label="数量">
                    <el-input-number
                      v-model="buyForm.quantity"
                      :min="100"
                      :step="100"
                      style="width: 100%"
                      placeholder="请输入交易数量"
                    />
                    <div class="quantity-tips">
                      <span>最小: 100股</span>
                    </div>
                  </el-form-item>
                  
                  <el-form-item v-if="buyForm.type === 'limit'" label="价格">
                    <el-input-number
                      v-model="buyForm.price"
                      :precision="2"
                      :step="0.01"
                      :min="0.01"
                      style="width: 100%"
                      placeholder="请输入交易价格"
                    />
                    <div class="price-tips">
                      <el-button-group size="small">
                        <el-button @click="setPrice('current', 'buy')">现价</el-button>
                        <el-button @click="setPrice('buy1', 'buy')">买一</el-button>
                        <el-button @click="setPrice('sell1', 'buy')">卖一</el-button>
                      </el-button-group>
                    </div>
                  </el-form-item>
                  
                  <el-form-item label="金额预估">
                    <div class="amount-info">
                      <div class="amount-row">
                        <span>交易金额:</span>
                        <span class="amount">¥{{ formatMoney(getBuyEstimatedAmount()) }}</span>
                      </div>
                      <div class="amount-row">
                        <span>手续费:</span>
                        <span class="fee">¥{{ formatMoney(getBuyEstimatedFee()) }}</span>
                      </div>
                      <div class="amount-row total">
                        <span>需要资金:</span>
                        <span class="total-amount">¥{{ formatMoney(getBuyTotalAmount()) }}</span>
                      </div>
                    </div>
                  </el-form-item>
                  
                  <el-form-item>
                    <el-button 
                      type="danger" 
                      style="width: 100%"
                      size="large"
                      @click="submitBuyOrder"
                      :disabled="!canBuy"
                    >
                      买入 {{ currentStock?.name || '' }}
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="卖出" name="sell">
              <div class="trading-form">
                <el-form :model="sellForm" label-width="80px">
                  <el-form-item label="交易类型">
                    <el-radio-group v-model="sellForm.type">
                      <el-radio label="limit">限价单</el-radio>
                      <el-radio label="market">市价单</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  
                  <el-form-item label="数量">
                    <el-input-number
                      v-model="sellForm.quantity"
                      :min="100"
                      :step="100"
                      :max="availableQuantity"
                      style="width: 100%"
                      placeholder="请输入交易数量"
                    />
                    <div class="quantity-tips">
                      <span>最小: 100股</span>
                      <span>可卖: {{ availableQuantity }}股</span>
                    </div>
                  </el-form-item>
                  
                  <el-form-item v-if="sellForm.type === 'limit'" label="价格">
                    <el-input-number
                      v-model="sellForm.price"
                      :precision="2"
                      :step="0.01"
                      :min="0.01"
                      style="width: 100%"
                      placeholder="请输入交易价格"
                    />
                    <div class="price-tips">
                      <el-button-group size="small">
                        <el-button @click="setPrice('current', 'sell')">现价</el-button>
                        <el-button @click="setPrice('buy1', 'sell')">买一</el-button>
                        <el-button @click="setPrice('sell1', 'sell')">卖一</el-button>
                      </el-button-group>
                    </div>
                  </el-form-item>
                  
                  <el-form-item label="金额预估">
                    <div class="amount-info">
                      <div class="amount-row">
                        <span>交易金额:</span>
                        <span class="amount">¥{{ formatMoney(getSellEstimatedAmount()) }}</span>
                      </div>
                      <div class="amount-row">
                        <span>手续费:</span>
                        <span class="fee">¥{{ formatMoney(getSellEstimatedFee()) }}</span>
                      </div>
                      <div class="amount-row total">
                        <span>到账金额:</span>
                        <span class="total-amount">¥{{ formatMoney(getSellTotalAmount()) }}</span>
                      </div>
                    </div>
                  </el-form-item>
                  
                  <el-form-item>
                    <el-button 
                      type="success" 
                      style="width: 100%"
                      size="large"
                      @click="submitSellOrder"
                      :disabled="!canSell"
                    >
                      卖出 {{ currentStock?.name || '' }}
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 账户信息 -->
        <div class="account-panel">
          <div class="account-info">
            <div class="info-row">
              <span>账户类型:</span>
              <el-tag :type="tradingMode === 'real' ? 'danger' : 'success'">
                {{ tradingMode === 'real' ? '实盘账户' : '模拟账户' }}
              </el-tag>
            </div>
            <div class="info-row">
              <span>可用资金:</span>
              <span class="funds">¥{{ formatMoney(account?.availableFunds || 0) }}</span>
            </div>
            <div class="info-row">
              <span>总资产:</span>
              <span class="assets">¥{{ formatMoney(account?.totalAssets || 0) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Cpu, Money } from '@element-plus/icons-vue'

// Props
interface Props {
  account: {
    id: string
    type: string
    name: string
    availableFunds: number
    totalAssets: number
  }
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  orderPlaced: [orderData: any]
  accountSwitch: [accountType: 'simulation' | 'real']
}>()

// 响应式数据
const tradingMode = ref<'simulation' | 'real'>('simulation')
const selectedSymbol = ref('')
const activeTab = ref('buy')
const availableQuantity = ref(1000) // 模拟可卖数量

const buyForm = reactive({
  type: 'limit',
  quantity: 100,
  price: 0
})

const sellForm = reactive({
  type: 'limit',
  quantity: 100,
  price: 0
})

const currentStock = ref({
  symbol: '000001',
  name: '平安银行',
  currentPrice: 12.45,
  change: 0.23,
  changePercent: 1.88,
  volume: *********,
  turnover: **********
})

const marketDepth = reactive({
  sell: [
    { price: 12.48, volume: 1200 },
    { price: 12.47, volume: 2300 },
    { price: 12.46, volume: 1800 },
    { price: 12.45, volume: 3200 },
    { price: 12.44, volume: 2100 }
  ],
  buy: [
    { price: 12.43, volume: 2800 },
    { price: 12.42, volume: 1900 },
    { price: 12.41, volume: 3100 },
    { price: 12.40, volume: 2200 },
    { price: 12.39, volume: 1600 }
  ]
})

// 计算属性
const formatPrice = (price: number) => price.toFixed(2)
const formatMoney = (amount: number) => amount.toFixed(2)

const getPriceClass = (changePercent: number) => {
  if (changePercent > 0) return 'price-up'
  if (changePercent < 0) return 'price-down'
  return 'price-neutral'
}

const getBuyEstimatedAmount = () => {
  if (!buyForm.quantity || !currentStock.value) return 0
  const price = buyForm.type === 'market' ? currentStock.value.currentPrice : buyForm.price
  return buyForm.quantity * price
}

const getBuyEstimatedFee = () => {
  const feeRate = 0.0003
  const fee = getBuyEstimatedAmount() * feeRate
  return Math.max(fee, 5)
}

const getBuyTotalAmount = () => {
  return getBuyEstimatedAmount() + getBuyEstimatedFee()
}

const getSellEstimatedAmount = () => {
  if (!sellForm.quantity || !currentStock.value) return 0
  const price = sellForm.type === 'market' ? currentStock.value.currentPrice : sellForm.price
  return sellForm.quantity * price
}

const getSellEstimatedFee = () => {
  const feeRate = 0.0003
  const fee = getSellEstimatedAmount() * feeRate
  return Math.max(fee, 5)
}

const getSellTotalAmount = () => {
  return getSellEstimatedAmount() - getSellEstimatedFee()
}

const canBuy = computed(() => {
  if (!currentStock.value || !buyForm.quantity) return false
  if (buyForm.type === 'limit' && !buyForm.price) return false
  if (props.account) {
    return getBuyTotalAmount() <= props.account.availableFunds
  }
  return true
})

const canSell = computed(() => {
  if (!currentStock.value || !sellForm.quantity) return false
  if (sellForm.type === 'limit' && !sellForm.price) return false
  return sellForm.quantity <= availableQuantity.value
})

// 方法
const handleModeChange = (mode: 'simulation' | 'real') => {
  emit('accountSwitch', mode)
  ElMessage.info(`已切换到${mode === 'simulation' ? '模拟' : '实盘'}交易模式`)
}

const searchStocks = (queryString: string, callback: Function) => {
  const mockStocks = [
    { symbol: '000001', name: '平安银行', currentPrice: 12.45, changePercent: 1.88 },
    { symbol: '000002', name: '万科A', currentPrice: 18.67, changePercent: -0.53 },
    { symbol: '600000', name: '浦发银行', currentPrice: 8.92, changePercent: 2.31 },
    { symbol: '600036', name: '招商银行', currentPrice: 35.78, changePercent: 1.24 }
  ]
  
  const results = queryString 
    ? mockStocks.filter(stock => 
        stock.symbol.includes(queryString) || 
        stock.name.includes(queryString)
      )
    : mockStocks
  
  callback(results)
}

const handleSymbolSelect = (item: any) => {
  currentStock.value = {
    ...item,
    volume: Math.floor(Math.random() * *********),
    turnover: Math.floor(Math.random() * **********),
    change: item.currentPrice * item.changePercent / 100
  }
  
  updateMarketDepth()
  updateFormPrices()
}

const updateMarketDepth = () => {
  const basePrice = currentStock.value.currentPrice
  
  for (let i = 0; i < 5; i++) {
    marketDepth.sell[i] = {
      price: basePrice + (i + 1) * 0.01,
      volume: Math.floor(Math.random() * 5000) + 1000
    }
  }
  
  for (let i = 0; i < 5; i++) {
    marketDepth.buy[i] = {
      price: basePrice - (i + 1) * 0.01,
      volume: Math.floor(Math.random() * 5000) + 1000
    }
  }
}

const updateFormPrices = () => {
  if (currentStock.value) {
    if (buyForm.type === 'limit') {
      buyForm.price = currentStock.value.currentPrice
    }
    if (sellForm.type === 'limit') {
      sellForm.price = currentStock.value.currentPrice
    }
  }
}

const setPrice = (type: 'current' | 'buy1' | 'sell1', side: 'buy' | 'sell') => {
  if (!currentStock.value) return
  
  let price = currentStock.value.currentPrice
  
  switch (type) {
    case 'current':
      price = currentStock.value.currentPrice
      break
    case 'buy1':
      price = marketDepth.buy[0]?.price || currentStock.value.currentPrice - 0.01
      break
    case 'sell1':
      price = marketDepth.sell[0]?.price || currentStock.value.currentPrice + 0.01
      break
  }
  
  if (side === 'buy') {
    buyForm.price = price
  } else {
    sellForm.price = price
  }
}

const submitBuyOrder = () => {
  if (!currentStock.value) {
    ElMessage.warning('请先选择股票')
    return
  }
  
  const orderData = {
    side: 'buy',
    type: buyForm.type,
    quantity: buyForm.quantity,
    price: buyForm.type === 'limit' ? buyForm.price : currentStock.value.currentPrice,
    symbol: currentStock.value.symbol,
    stockName: currentStock.value.name,
    estimatedAmount: getBuyEstimatedAmount(),
    estimatedFee: getBuyEstimatedFee(),
    totalAmount: getBuyTotalAmount(),
    mode: tradingMode.value,
    timestamp: new Date().toISOString()
  }
  
  emit('orderPlaced', orderData)
  ElMessage.success('买入订单提交成功')
}

const submitSellOrder = () => {
  if (!currentStock.value) {
    ElMessage.warning('请先选择股票')
    return
  }
  
  const orderData = {
    side: 'sell',
    type: sellForm.type,
    quantity: sellForm.quantity,
    price: sellForm.type === 'limit' ? sellForm.price : currentStock.value.currentPrice,
    symbol: currentStock.value.symbol,
    stockName: currentStock.value.name,
    estimatedAmount: getSellEstimatedAmount(),
    estimatedFee: getSellEstimatedFee(),
    totalAmount: getSellTotalAmount(),
    mode: tradingMode.value,
    timestamp: new Date().toISOString()
  }
  
  emit('orderPlaced', orderData)
  ElMessage.success('卖出订单提交成功')
}

// 监听器
watch(() => currentStock.value, updateFormPrices, { immediate: true })

watch(() => buyForm.type, (newType) => {
  if (newType === 'limit' && currentStock.value) {
    buyForm.price = currentStock.value.currentPrice
  }
})

watch(() => sellForm.type, (newType) => {
  if (newType === 'limit' && currentStock.value) {
    sellForm.price = currentStock.value.currentPrice
  }
})

watch(() => props.account?.type, (newType) => {
  tradingMode.value = newType === 'real' ? 'real' : 'simulation'
}, { immediate: true })

// 生命周期
onMounted(() => {
  updateMarketDepth()
  
  // 模拟实时行情更新
  setInterval(() => {
    if (currentStock.value) {
      const change = (Math.random() - 0.5) * 0.1
      currentStock.value.currentPrice += change
      currentStock.value.change += change
      currentStock.value.changePercent = (currentStock.value.change / (currentStock.value.currentPrice - currentStock.value.change)) * 100
      
      updateMarketDepth()
    }
  }, 5000)
})
</script>

<style scoped lang="scss">
.trading-terminal-enhanced {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mode-switcher {
  .el-radio-group {
    margin-bottom: 12px;
    
    .el-radio-button {
      .el-icon {
        margin-right: 6px;
      }
    }
  }
}

.terminal-layout {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 20px;
  overflow: hidden;
}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  .stock-search {
    .stock-suggestion {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .stock-code {
        font-weight: 600;
        color: #303133;
      }
      
      .stock-name {
        color: #606266;
        flex: 1;
        margin-left: 12px;
      }
      
      .stock-price {
        font-weight: 600;
        
        &.price-up { color: #f56c6c; }
        &.price-down { color: #67c23a; }
        &.price-neutral { color: #909399; }
      }
    }
  }
  
  .stock-info {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .stock-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
    
    .price-info {
      margin-bottom: 20px;
      
      .current-price {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 8px;
        
        &.price-up { color: #f56c6c; }
        &.price-down { color: #67c23a; }
        &.price-neutral { color: #909399; }
      }
      
      .price-change {
        display: flex;
        gap: 16px;
        font-size: 16px;
        font-weight: 600;
        
        span {
          &.price-up { color: #f56c6c; }
          &.price-down { color: #67c23a; }
          &.price-neutral { color: #909399; }
        }
      }
    }
    
    .market-depth {
      .depth-header {
        font-weight: 600;
        margin-bottom: 12px;
        color: #303133;
      }
      
      .depth-content {
        .depth-row {
          display: grid;
          grid-template-columns: 40px 80px 1fr;
          gap: 12px;
          padding: 4px 0;
          font-size: 14px;
          
          &.sell {
            .price { color: #67c23a; }
          }
          
          &.buy {
            .price { color: #f56c6c; }
          }
          
          .label {
            color: #909399;
          }
          
          .volume {
            text-align: right;
            color: #606266;
          }
        }
        
        .current-price-row {
          display: grid;
          grid-template-columns: 40px 80px 1fr;
          gap: 12px;
          padding: 8px 0;
          border-top: 1px solid #e4e7ed;
          border-bottom: 1px solid #e4e7ed;
          margin: 8px 0;
          
          .current-price-label {
            color: #303133;
            font-weight: 600;
          }
          
          .current-price-value {
            font-weight: 600;
            
            &.price-up { color: #f56c6c; }
            &.price-down { color: #67c23a; }
            &.price-neutral { color: #909399; }
          }
        }
      }
    }
  }
}

.right-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  .trading-panel {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex: 1;
    
    .trading-form {
      .el-form-item {
        margin-bottom: 20px;
      }
      
      .quantity-tips {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }
      
      .price-tips {
        margin-top: 8px;
        
        .el-button-group {
          width: 100%;
          
          .el-button {
            flex: 1;
          }
        }
      }
      
      .amount-info {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 12px;
        
        .amount-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 14px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          &.total {
            border-top: 1px solid #e4e7ed;
            padding-top: 8px;
            font-weight: 600;
            
            .total-amount {
              color: #409eff;
              font-size: 16px;
            }
          }
          
          .amount {
            color: #303133;
            font-weight: 500;
          }
          
          .fee {
            color: #f56c6c;
          }
        }
      }
      
      .el-button {
        font-weight: 600;
        
        &[type="danger"] {
          background: #f56c6c;
          border-color: #f56c6c;
          
          &:hover {
            background: #f78989;
            border-color: #f78989;
          }
        }
        
        &[type="success"] {
          background: #67c23a;
          border-color: #67c23a;
          
          &:hover {
            background: #85ce61;
            border-color: #85ce61;
          }
        }
      }
    }
  }
  
  .account-panel {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .account-info {
      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        font-size: 14px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .funds {
          color: #67c23a;
          font-weight: 600;
        }
        
        .assets {
          color: #409eff;
          font-weight: 600;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .terminal-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
  
  .right-panel {
    order: -1;
  }
}

@media (max-width: 768px) {
  .mode-switcher {
    .el-radio-group {
      width: 100%;
      display: flex;
      
      .el-radio-button {
        flex: 1;
      }
    }
  }
}
</style>
