/**
 * 安全拦截器
 * 为API请求添加安全头部和验证
 */

import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import { getCSRFToken, generateSecureToken, sanitizeURL } from '@/utils/security'
import { getToken } from '@/utils/auth'

/**
 * 请求安全拦截器
 */
export const requestSecurityInterceptor = (config: AxiosRequestConfig): AxiosRequestConfig => {
  // 1. 添加安全头部
  config.headers = {
    ...config.headers,
    // 防止CSRF攻击
    'X-Requested-With': 'XMLHttpRequest',
    // 内容类型安全
    'Content-Type': config.headers?.['Content-Type'] || 'application/json',
    // 缓存控制
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  }

  // 2. 添加CSRF Token
  const csrfToken = getCSRFToken()
  if (csrfToken) {
    config.headers['X-CSRF-Token'] = csrfToken
  }

  // 3. 添加请求ID用于追踪
  const requestId = generateSecureToken(16)
  config.headers['X-Request-ID'] = requestId

  // 4. 添加时间戳防止重放攻击
  config.headers['X-Timestamp'] = Date.now().toString()

  // 5. 验证URL安全性
  if (config.url) {
    const baseURL = config.baseURL || ''
    const fullURL = config.url.startsWith('http') ? config.url : baseURL + config.url
    const sanitizedURL = sanitizeURL(fullURL)

    if (!sanitizedURL) {
      throw new Error('Invalid or unsafe URL detected')
    }
  }

  // 6. 验证请求参数
  if (config.params) {
    config.params = sanitizeRequestParams(config.params)
  }

  // 7. 验证请求体
  if (config.data) {
    config.data = sanitizeRequestData(config.data)
  }

  // 8. 设置超时时间
  if (!config.timeout) {
    config.timeout = 30000 // 30秒超时
  }

  return config
}

/**
 * 响应安全拦截器
 */
export const responseSecurityInterceptor = (response: AxiosResponse): AxiosResponse => {
  // 1. 验证响应头部安全性
  validateResponseHeaders(response.headers)

  // 2. 验证响应数据
  if (response.data) {
    response.data = sanitizeResponseData(response.data)
  }

  // 3. 检查安全警告
  checkSecurityWarnings(response)

  return response
}

/**
 * 响应错误安全拦截器
 */
export const responseErrorSecurityInterceptor = (error: any) => {
  // 1. 记录安全相关错误
  if (error.response) {
    const status = error.response.status
    const url = error.config?.url || 'unknown'

    // 记录可疑的错误状态
    if ([401, 403, 429].includes(status)) {
      console.warn(`Security alert: ${status} response from ${url}`)
    }

    // 检查是否为安全攻击
    if (status === 418) { // I'm a teapot - 通常用于检测自动化工具
      console.error('Potential bot detection triggered')
    }
  }

  // 2. 清理错误信息中的敏感数据
  if (error.response?.data) {
    error.response.data = sanitizeErrorData(error.response.data)
  }

  return Promise.reject(error)
}

/**
 * 清理请求参数
 */
const sanitizeRequestParams = (params: Record<string, any>): Record<string, any> => {
  const sanitized: Record<string, any> = {}

  for (const [key, value] of Object.entries(params)) {
    // 验证参数名
    if (typeof key !== 'string' || key.includes('<') || key.includes('>')) {
      console.warn(`Suspicious parameter key detected: ${key}`)
      continue
    }

    // 清理参数值
    if (typeof value === 'string') {
      // 移除潜在的脚本注入
      const cleanValue = value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')

      sanitized[key] = cleanValue
    } else if (typeof value === 'number' || typeof value === 'boolean') {
      sanitized[key] = value
    } else if (Array.isArray(value)) {
      sanitized[key] = value.map(item =>
        typeof item === 'string' ? sanitizeString(item) : item
      )
    } else if (value && typeof value === 'object') {
      sanitized[key] = sanitizeRequestParams(value)
    } else {
      sanitized[key] = value
    }
  }

  return sanitized
}

/**
 * 清理请求数据
 */
const sanitizeRequestData = (data: any): any => {
  if (typeof data === 'string') {
    return sanitizeString(data)
  }

  if (Array.isArray(data)) {
    return data.map(item => sanitizeRequestData(item))
  }

  if (data && typeof data === 'object') {
    return sanitizeRequestParams(data)
  }

  return data
}

/**
 * 清理响应数据
 */
const sanitizeResponseData = (data: any): any => {
  if (typeof data === 'string') {
    return sanitizeString(data)
  }

  if (Array.isArray(data)) {
    return data.map(item => sanitizeResponseData(item))
  }

  if (data && typeof data === 'object') {
    const sanitized: Record<string, any> = {}

    for (const [key, value] of Object.entries(data)) {
      // 过滤敏感字段
      if (isSensitiveField(key)) {
        sanitized[key] = '[FILTERED]'
      } else {
        sanitized[key] = sanitizeResponseData(value)
      }
    }

    return sanitized
  }

  return data
}

/**
 * 清理错误数据
 */
const sanitizeErrorData = (data: any): any => {
  if (typeof data === 'string') {
    // 移除可能的路径信息和敏感数据
    return data
      .replace(/\/[a-zA-Z0-9_\-\/]+\.(js|ts|vue|php|py)/g, '[PATH]')
      .replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g, '[IP]')
      .replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, '[EMAIL]')
  }

  if (data && typeof data === 'object') {
    const sanitized: Record<string, any> = {}

    for (const [key, value] of Object.entries(data)) {
      if (isSensitiveField(key)) {
        sanitized[key] = '[FILTERED]'
      } else {
        sanitized[key] = sanitizeErrorData(value)
      }
    }

    return sanitized
  }

  return data
}

/**
 * 清理字符串
 */
const sanitizeString = (str: string): string => {
  return str
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .replace(/data:text\/html/gi, '')
    .replace(/vbscript:/gi, '')
}

/**
 * 检查是否为敏感字段
 */
const isSensitiveField = (fieldName: string): boolean => {
  const sensitiveFields = [
    'password',
    'token',
    'secret',
    'key',
    'private',
    'credential',
    'auth',
    'session',
    'cookie',
    'csrf'
  ]

  const lowerFieldName = fieldName.toLowerCase()
  return sensitiveFields.some(sensitive => lowerFieldName.includes(sensitive))
}

/**
 * 验证响应头部
 */
const validateResponseHeaders = (headers: Record<string, any>): void => {
  // 检查安全头部
  const securityHeaders = [
    'x-frame-options',
    'x-content-type-options',
    'x-xss-protection',
    'strict-transport-security',
    'content-security-policy'
  ]

  const missingHeaders = securityHeaders.filter(header => !headers[header])

  if (missingHeaders.length > 0) {
    // 只在开发环境显示安全头部警告
    if (import.meta.env.DEV) {
      console.warn('Missing security headers:', missingHeaders)
    }
  }

  // 检查可疑的头部
  for (const [key, value] of Object.entries(headers)) {
    if (typeof value === 'string') {
      if (value.includes('<script>') || value.includes('javascript:')) {
        console.error(`Suspicious header detected: ${key} = ${value}`)
      }
    }
  }
}

/**
 * 检查安全警告
 */
const checkSecurityWarnings = (response: AxiosResponse): void => {
  // 检查响应时间异常
  const responseTime = Date.now() - parseInt(response.config.headers?.['X-Timestamp'] || '0')
  if (responseTime > 30000) {
    console.warn(`Slow response detected: ${responseTime}ms`)
  }

  // 检查响应大小异常
  const responseSize = JSON.stringify(response.data).length
  if (responseSize > 10 * 1024 * 1024) { // 10MB
    console.warn(`Large response detected: ${responseSize} bytes`)
  }

  // 检查重定向
  if (response.status >= 300 && response.status < 400) {
    const location = response.headers.location
    if (location) {
      const sanitizedLocation = sanitizeURL(location)
      if (!sanitizedLocation) {
        console.error('Unsafe redirect detected:', location)
      }
    }
  }
}

/**
 * 生成安全的请求配置
 */
export const createSecureRequestConfig = (config: Partial<AxiosRequestConfig> = {}): AxiosRequestConfig => {
  return {
    timeout: 30000,
    withCredentials: false, // 默认不发送cookie，除非明确需要
    validateStatus: (status) => status >= 200 && status < 300,
    maxRedirects: 3,
    maxContentLength: 10 * 1024 * 1024, // 10MB
    ...config,
    headers: {
      'X-Requested-With': 'XMLHttpRequest',
      'Cache-Control': 'no-cache',
      ...config.headers
    }
  }
}

/**
 * 安全拦截器配置
 */
export const securityInterceptorConfig = {
  request: requestSecurityInterceptor,
  response: responseSecurityInterceptor,
  responseError: responseErrorSecurityInterceptor,
  createSecureConfig: createSecureRequestConfig
}
