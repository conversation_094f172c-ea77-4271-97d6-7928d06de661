# Production Environment Configuration Example
# Copy this file to .env.production and fill in the actual values

# API Configuration
VITE_API_BASE_URL=https://api.your-domain.com
VITE_WS_BASE_URL=wss://api.your-domain.com

# Feature Flags
VITE_USE_MOCK=false
VITE_SHOW_SQL_EDITOR=true
VITE_ENABLE_DEVTOOLS=false

# External Services
VITE_SENTRY_DSN=https://<EMAIL>/project-id
VITE_SENTRY_ENVIRONMENT=production
VITE_SENTRY_TRACES_SAMPLE_RATE=0.1

# CDN Configuration
VITE_CDN_BASE_URL=https://cdn.your-domain.com

# Analytics
VITE_GA_TRACKING_ID=UA-XXXXXXXXX-X
VITE_GTM_ID=GTM-XXXXXXX

# Security
VITE_ENABLE_CSP=true
VITE_CSP_REPORT_URI=https://api.your-domain.com/csp-report

# API Keys (public keys only!)
VITE_MAPBOX_TOKEN=your-mapbox-public-token
VITE_STRIPE_PUBLIC_KEY=pk_live_your-stripe-public-key

# Build Options
VITE_BUILD_COMPRESS=true
VITE_BUILD_LEGACY=false
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_REPORT=false

# Feature Configuration
VITE_MAX_UPLOAD_SIZE=10485760  # 10MB
VITE_WEBSOCKET_RECONNECT_INTERVAL=5000
VITE_API_TIMEOUT=30000

# UI Configuration
VITE_APP_TITLE=量化投资平台
VITE_APP_DESCRIPTION=专业的量化投资解决方案
VITE_DEFAULT_LOCALE=zh-CN
VITE_THEME_COLOR=#1890ff