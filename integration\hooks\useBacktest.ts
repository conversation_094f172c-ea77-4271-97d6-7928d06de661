/**
 * React Hook for backtest management
 * 提供回测相关的状态管理和操作
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { apiClient, WebSocketManager, BacktestRequest, BacktestResult } from '../api-client';

export interface UseBacktestOptions {
  autoRefresh?: boolean;
  refreshInterval?: number; // 毫秒
  enableWebSocket?: boolean;
  clientId?: string;
}

export interface BacktestState {
  tasks: BacktestResult[];
  loading: boolean;
  error: string | null;
  creating: boolean;
  selectedTask: BacktestResult | null;
}

export function useBacktest(options: UseBacktestOptions = {}) {
  const {
    autoRefresh = true,
    refreshInterval = 5000,
    enableWebSocket = true,
    clientId = 'default-client',
  } = options;

  const [state, setState] = useState<BacktestState>({
    tasks: [],
    loading: false,
    error: null,
    creating: false,
    selectedTask: null,
  });

  const wsRef = useRef<WebSocketManager | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 更新状态的辅助函数
  const updateState = useCallback((updates: Partial<BacktestState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // 获取回测任务列表
  const fetchTasks = useCallback(async () => {
    updateState({ loading: true, error: null });
    
    try {
      const response = await apiClient.listBacktests();
      
      if (response.success && response.data) {
        updateState({ tasks: response.data.tasks, loading: false });
      } else {
        updateState({ 
          error: response.error?.message || '获取任务列表失败',
          loading: false 
        });
      }
    } catch (error) {
      updateState({
        error: error instanceof Error ? error.message : '网络错误',
        loading: false,
      });
    }
  }, [updateState]);

  // 创建新的回测任务
  const createBacktest = useCallback(async (request: BacktestRequest) => {
    updateState({ creating: true, error: null });
    
    try {
      const response = await apiClient.createBacktest(request);
      
      if (response.success && response.data) {
        updateState({ creating: false });
        // 刷新任务列表
        await fetchTasks();
        return response.data.task_id;
      } else {
        updateState({
          error: response.error?.message || '创建回测任务失败',
          creating: false,
        });
        return null;
      }
    } catch (error) {
      updateState({
        error: error instanceof Error ? error.message : '网络错误',
        creating: false,
      });
      return null;
    }
  }, [updateState, fetchTasks]);

  // 取消回测任务
  const cancelBacktest = useCallback(async (taskId: string) => {
    try {
      const response = await apiClient.cancelBacktest(taskId);
      
      if (response.success) {
        // 更新本地状态
        updateState({
          tasks: state.tasks.map(task => 
            task.task_id === taskId 
              ? { ...task, status: 'cancelled' as const }
              : task
          )
        });
        return true;
      } else {
        updateState({ error: response.error?.message || '取消任务失败' });
        return false;
      }
    } catch (error) {
      updateState({
        error: error instanceof Error ? error.message : '网络错误',
      });
      return false;
    }
  }, [state.tasks, updateState]);

  // 删除回测任务
  const deleteBacktest = useCallback(async (taskId: string) => {
    try {
      const response = await apiClient.deleteBacktest(taskId);
      
      if (response.success) {
        // 从本地状态移除
        updateState({
          tasks: state.tasks.filter(task => task.task_id !== taskId),
          selectedTask: state.selectedTask?.task_id === taskId ? null : state.selectedTask,
        });
        return true;
      } else {
        updateState({ error: response.error?.message || '删除任务失败' });
        return false;
      }
    } catch (error) {
      updateState({
        error: error instanceof Error ? error.message : '网络错误',
      });
      return false;
    }
  }, [state.tasks, state.selectedTask, updateState]);

  // 获取特定任务的详细状态
  const getTaskStatus = useCallback(async (taskId: string) => {
    try {
      const response = await apiClient.getBacktestStatus(taskId);
      
      if (response.success && response.data) {
        // 更新本地状态中的对应任务
        updateState({
          tasks: state.tasks.map(task => 
            task.task_id === taskId ? response.data! : task
          ),
          selectedTask: state.selectedTask?.task_id === taskId 
            ? response.data! 
            : state.selectedTask,
        });
        return response.data;
      } else {
        updateState({ error: response.error?.message || '获取任务状态失败' });
        return null;
      }
    } catch (error) {
      updateState({
        error: error instanceof Error ? error.message : '网络错误',
      });
      return null;
    }
  }, [state.tasks, state.selectedTask, updateState]);

  // 获取回测结果
  const getResults = useCallback(async (taskId: string) => {
    try {
      const response = await apiClient.getBacktestResults(taskId);
      
      if (response.success) {
        return response.data;
      } else {
        updateState({ error: response.error?.message || '获取结果失败' });
        return null;
      }
    } catch (error) {
      updateState({
        error: error instanceof Error ? error.message : '网络错误',
      });
      return null;
    }
  }, [updateState]);

  // 获取回测报告
  const getReport = useCallback(async (taskId: string, format: 'json' | 'html' = 'json') => {
    try {
      const response = await apiClient.getBacktestReport(taskId, format);
      
      if (response.success) {
        return response.data;
      } else {
        updateState({ error: response.error?.message || '获取报告失败' });
        return null;
      }
    } catch (error) {
      updateState({
        error: error instanceof Error ? error.message : '网络错误',
      });
      return null;
    }
  }, [updateState]);

  // 比较多个回测结果
  const compareBacktests = useCallback(async (taskIds: string[]) => {
    try {
      const response = await apiClient.compareBacktests(taskIds);
      
      if (response.success) {
        return response.data;
      } else {
        updateState({ error: response.error?.message || '比较结果失败' });
        return null;
      }
    } catch (error) {
      updateState({
        error: error instanceof Error ? error.message : '网络错误',
      });
      return null;
    }
  }, [updateState]);

  // 选择任务
  const selectTask = useCallback((task: BacktestResult | null) => {
    updateState({ selectedTask: task });
  }, [updateState]);

  // 清除错误
  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  // WebSocket 设置
  useEffect(() => {
    if (enableWebSocket) {
      wsRef.current = apiClient.createWebSocket(clientId);
      
      const ws = wsRef.current;
      
      // 设置消息处理器
      ws.onMessage('backtest_progress', (data) => {
        updateState({
          tasks: state.tasks.map(task => 
            task.task_id === data.task_id 
              ? { ...task, progress: data.progress, status: data.status }
              : task
          )
        });
      });

      ws.onMessage('backtest_status', (data) => {
        updateState({
          tasks: state.tasks.map(task => 
            task.task_id === data.task_id 
              ? { ...task, ...data }
              : task
          )
        });
      });

      ws.onMessage('backtest_cancelled', (data) => {
        updateState({
          tasks: state.tasks.map(task => 
            task.task_id === data.task_id 
              ? { ...task, status: 'cancelled' as const }
              : task
          )
        });
      });

      // 连接WebSocket
      ws.connect()
        .then(() => {
          console.log('WebSocket connected for backtest updates');
          // 订阅回测相关主题
          ws.subscribe(['backtest_progress', 'backtest_status']);
        })
        .catch(console.error);

      return () => {
        ws.disconnect();
      };
    }
  }, [enableWebSocket, clientId, state.tasks, updateState]);

  // 自动刷新设置
  useEffect(() => {
    if (autoRefresh && !enableWebSocket) {
      intervalRef.current = setInterval(fetchTasks, refreshInterval);
      
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [autoRefresh, enableWebSocket, refreshInterval, fetchTasks]);

  // 初始加载
  useEffect(() => {
    fetchTasks();
  }, [fetchTasks]);

  // 清理
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (wsRef.current) {
        wsRef.current.disconnect();
      }
    };
  }, []);

  return {
    // 状态
    tasks: state.tasks,
    loading: state.loading,
    error: state.error,
    creating: state.creating,
    selectedTask: state.selectedTask,
    
    // 操作
    createBacktest,
    cancelBacktest,
    deleteBacktest,
    getTaskStatus,
    getResults,
    getReport,
    compareBacktests,
    selectTask,
    clearError,
    refreshTasks: fetchTasks,
    
    // WebSocket相关
    wsManager: wsRef.current,
  };
}

export default useBacktest;