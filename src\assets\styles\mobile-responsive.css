/**
 * 移动端响应式优化样式
 * 主要解决移动端文字过小的问题
 */

/* 基础移动端媒体查询 */
@media (max-width: 768px) {
  /* 全局文字大小调整 */
  body {
    font-size: 16px !important;
    line-height: 1.5;
  }

  /* Element Plus 组件文字大小调整 */
  .el-button {
    font-size: 16px !important;
    min-height: 44px; /* 符合移动端触摸标准 */
    padding: 12px 16px !important;
  }

  .el-button--small {
    font-size: 14px !important;
    min-height: 40px;
    padding: 10px 14px !important;
  }

  .el-input__inner {
    font-size: 16px !important;
    min-height: 44px;
    padding: 12px 16px !important;
  }

  .el-textarea__inner {
    font-size: 16px !important;
    padding: 12px 16px !important;
  }

  .el-select .el-input__inner {
    font-size: 16px !important;
  }

  .el-form-item__label {
    font-size: 16px !important;
    line-height: 1.4;
  }

  .el-table {
    font-size: 14px !important;
  }

  .el-table th,
  .el-table td {
    font-size: 14px !important;
    padding: 12px 8px !important;
  }

  /* 导航和菜单 */
  .el-menu-item {
    font-size: 16px !important;
    min-height: 48px;
    line-height: 48px !important;
  }

  .el-submenu__title {
    font-size: 16px !important;
    min-height: 48px;
    line-height: 48px !important;
  }

  /* 标签页 */
  .el-tabs__item {
    font-size: 16px !important;
    padding: 0 16px !important;
    height: 44px !important;
    line-height: 44px !important;
  }

  /* 卡片和面板 */
  .el-card__header {
    font-size: 18px !important;
    font-weight: 600;
  }

  .el-card__body {
    font-size: 16px !important;
  }

  /* 对话框 */
  .el-dialog__title {
    font-size: 20px !important;
  }

  .el-dialog__body {
    font-size: 16px !important;
  }

  /* 消息提示 */
  .el-message {
    font-size: 16px !important;
  }

  .el-notification {
    font-size: 16px !important;
  }

  /* 分页器 */
  .el-pagination {
    font-size: 16px !important;
  }

  .el-pagination .el-pager li {
    font-size: 16px !important;
    min-width: 44px;
    height: 44px;
    line-height: 44px;
  }

  /* 面包屑 */
  .el-breadcrumb {
    font-size: 16px !important;
  }

  /* 步骤条 */
  .el-steps .el-step__title {
    font-size: 16px !important;
  }

  .el-steps .el-step__description {
    font-size: 14px !important;
  }

  /* 时间线 */
  .el-timeline-item__title {
    font-size: 16px !important;
  }

  .el-timeline-item__content {
    font-size: 14px !important;
  }

  /* 标签 */
  .el-tag {
    font-size: 14px !important;
    padding: 6px 12px !important;
  }

  /* 徽章 */
  .el-badge__content {
    font-size: 12px !important;
    min-width: 20px;
    height: 20px;
    line-height: 20px;
  }

  /* 工具提示 */
  .el-tooltip__popper {
    font-size: 14px !important;
  }

  /* 气泡确认框 */
  .el-popconfirm {
    font-size: 16px !important;
  }

  /* 抽屉 */
  .el-drawer__title {
    font-size: 20px !important;
  }

  .el-drawer__body {
    font-size: 16px !important;
  }
}

/* 超小屏幕优化 (手机竖屏) */
@media (max-width: 480px) {
  body {
    font-size: 18px !important;
  }

  .el-button {
    font-size: 18px !important;
    min-height: 48px;
    padding: 14px 18px !important;
  }

  .el-input__inner {
    font-size: 18px !important;
    min-height: 48px;
    padding: 14px 18px !important;
  }

  .el-form-item__label {
    font-size: 18px !important;
  }

  .el-menu-item {
    font-size: 18px !important;
    min-height: 52px;
    line-height: 52px !important;
  }

  .el-tabs__item {
    font-size: 18px !important;
    height: 48px !important;
    line-height: 48px !important;
  }

  .el-card__header {
    font-size: 20px !important;
  }

  .el-card__body {
    font-size: 18px !important;
  }

  .el-dialog__title {
    font-size: 22px !important;
  }

  .el-dialog__body {
    font-size: 18px !important;
  }
}

/* 自定义组件响应式优化 */
@media (max-width: 768px) {
  /* 交易终端 */
  .trading-terminal {
    font-size: 16px !important;
  }

  .trading-terminal .price {
    font-size: 18px !important;
    font-weight: 600;
  }

  .trading-terminal .change {
    font-size: 16px !important;
  }

  /* 市场数据 */
  .market-data {
    font-size: 16px !important;
  }

  .market-data .symbol {
    font-size: 18px !important;
    font-weight: 600;
  }

  .market-data .price {
    font-size: 20px !important;
  }

  /* 策略卡片 */
  .strategy-card {
    font-size: 16px !important;
  }

  .strategy-card .title {
    font-size: 18px !important;
    font-weight: 600;
  }

  .strategy-card .description {
    font-size: 14px !important;
  }

  /* 图表标题和标签 */
  .chart-container {
    font-size: 14px !important;
  }

  .chart-title {
    font-size: 18px !important;
    font-weight: 600;
  }

  /* 数据表格 */
  .data-table {
    font-size: 14px !important;
  }

  .data-table .header {
    font-size: 16px !important;
    font-weight: 600;
  }

  /* 状态指示器 */
  .status-indicator {
    font-size: 14px !important;
  }

  /* 数值显示 */
  .number-display {
    font-size: 18px !important;
    font-weight: 600;
  }

  /* 百分比显示 */
  .percentage-display {
    font-size: 16px !important;
  }
}

/* 确保触摸目标足够大 */
@media (max-width: 768px) {
  /* 最小触摸目标 44px */
  button,
  .el-button,
  .el-link,
  .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* 表单控件 */
  input,
  select,
  textarea,
  .el-input,
  .el-select,
  .el-textarea {
    min-height: 44px;
  }

  /* 导航链接 */
  .nav-link,
  .menu-item {
    min-height: 48px;
    padding: 12px 16px;
  }
}
