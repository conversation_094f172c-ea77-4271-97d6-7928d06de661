/**
 * 调试测试 - 检查具体问题
 */

import puppeteer from 'puppeteer';

class DebugTester {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async init() {
    console.log('🔍 启动调试测试...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    this.page = await this.browser.newPage();
    
    // 监听所有控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`[${type.toUpperCase()}] ${text}`);
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      console.error('❌ 页面错误:', error.message);
      console.error('   堆栈:', error.stack);
    });
  }

  async debugPageStructure() {
    console.log('\n🔍 调试页面结构...');
    
    try {
      // 导航到首页
      await this.page.goto('http://localhost:5173', { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });
      
      // 等待页面完全加载
      await new Promise(resolve => setTimeout(resolve, 8000));
      
      // 检查页面结构
      const pageStructure = await this.page.evaluate(() => {
        const app = document.querySelector('#app');
        
        return {
          hasApp: app !== null,
          appChildren: app ? app.children.length : 0,
          appInnerHTML: app ? app.innerHTML.substring(0, 500) : 'No app element',
          bodyClasses: document.body.className,
          title: document.title,
          url: window.location.href,
          
          // 检查具体的布局元素
          hasProfessionalLayout: document.querySelector('.professional-layout') !== null,
          hasTopNavbar: document.querySelector('.top-navbar') !== null,
          hasNavbarNav: document.querySelector('.navbar-nav') !== null,
          hasMainContent: document.querySelector('.main-content') !== null,
          
          // 检查路由视图
          hasRouterView: document.querySelector('router-view') !== null,
          
          // 检查所有可见元素
          allVisibleElements: Array.from(document.querySelectorAll('*')).filter(el => 
            el.offsetParent !== null && el.textContent.trim().length > 0
          ).slice(0, 10).map(el => ({
            tag: el.tagName,
            class: el.className,
            text: el.textContent.trim().substring(0, 50)
          }))
        };
      });
      
      console.log('📋 页面结构分析:');
      console.log(`   #app元素存在: ${pageStructure.hasApp}`);
      console.log(`   #app子元素数: ${pageStructure.appChildren}`);
      console.log(`   页面标题: ${pageStructure.title}`);
      console.log(`   当前URL: ${pageStructure.url}`);
      console.log(`   .professional-layout: ${pageStructure.hasProfessionalLayout}`);
      console.log(`   .top-navbar: ${pageStructure.hasTopNavbar}`);
      console.log(`   .navbar-nav: ${pageStructure.hasNavbarNav}`);
      console.log(`   .main-content: ${pageStructure.hasMainContent}`);
      
      console.log('\n📄 App内容预览:');
      console.log(pageStructure.appInnerHTML);
      
      console.log('\n👁️ 可见元素:');
      pageStructure.allVisibleElements.forEach((el, index) => {
        console.log(`   ${index + 1}. <${el.tag}> ${el.class} "${el.text}"`);
      });
      
      // 检查Vue应用状态
      const vueAppStatus = await this.page.evaluate(() => {
        // 检查Vue是否已挂载
        const app = document.querySelector('#app');
        const hasVueInstance = app && app.__vue_app__;
        
        return {
          hasVueInstance,
          vueVersion: window.Vue ? window.Vue.version : 'Vue not found',
          routerExists: window.$router !== undefined,
          currentRoute: window.$route ? window.$route.path : 'No route'
        };
      });
      
      console.log('\n🔧 Vue应用状态:');
      console.log(`   Vue实例: ${vueAppStatus.hasVueInstance}`);
      console.log(`   Vue版本: ${vueAppStatus.vueVersion}`);
      console.log(`   路由器: ${vueAppStatus.routerExists}`);
      console.log(`   当前路由: ${vueAppStatus.currentRoute}`);
      
      // 截图
      await this.page.screenshot({ 
        path: `debug-page-structure-${Date.now()}.png`, 
        fullPage: true 
      });
      console.log('📸 已保存调试截图');
      
      return pageStructure;
      
    } catch (error) {
      console.error('❌ 调试失败:', error.message);
      return null;
    }
  }

  async testRouterNavigation() {
    console.log('\n🧭 测试路由导航...');
    
    try {
      // 尝试手动导航到仪表盘
      console.log('尝试导航到 /dashboard');
      await this.page.goto('http://localhost:5173/dashboard', { 
        waitUntil: 'networkidle0',
        timeout: 15000 
      });
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const dashboardPage = await this.page.evaluate(() => {
        return {
          url: window.location.href,
          title: document.title,
          hasContent: document.body.textContent.trim().length > 100,
          contentPreview: document.body.textContent.trim().substring(0, 200)
        };
      });
      
      console.log('📊 仪表盘页面:');
      console.log(`   URL: ${dashboardPage.url}`);
      console.log(`   标题: ${dashboardPage.title}`);
      console.log(`   有内容: ${dashboardPage.hasContent}`);
      console.log(`   内容预览: ${dashboardPage.contentPreview}`);
      
      // 尝试导航到市场页面
      console.log('\n尝试导航到 /market');
      await this.page.goto('http://localhost:5173/market', { 
        waitUntil: 'networkidle0',
        timeout: 15000 
      });
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const marketPage = await this.page.evaluate(() => {
        return {
          url: window.location.href,
          title: document.title,
          hasContent: document.body.textContent.trim().length > 100,
          contentPreview: document.body.textContent.trim().substring(0, 200)
        };
      });
      
      console.log('📈 市场页面:');
      console.log(`   URL: ${marketPage.url}`);
      console.log(`   标题: ${marketPage.title}`);
      console.log(`   有内容: ${marketPage.hasContent}`);
      console.log(`   内容预览: ${marketPage.contentPreview}`);
      
    } catch (error) {
      console.error('❌ 路由测试失败:', error.message);
    }
  }

  async runDebug() {
    try {
      await this.init();
      
      // 1. 调试页面结构
      await this.debugPageStructure();
      
      // 2. 测试路由导航
      await this.testRouterNavigation();
      
      console.log('\n🎯 调试测试完成！');
      
    } catch (error) {
      console.error('❌ 调试失败:', error);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// 运行调试
const tester = new DebugTester();
tester.runDebug().catch(console.error);
