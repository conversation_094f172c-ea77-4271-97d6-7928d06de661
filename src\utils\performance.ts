/**
 * 性能优化工具函数
 * 用于提升应用交互性能和用户体验
 */

/**
 * 防抖函数 - 优化频繁触发的事件
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @param immediate 是否立即执行
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

/**
 * 节流函数 - 限制函数执行频率
 * @param func 要节流的函数
 * @param limit 时间间隔（毫秒）
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 延迟执行函数 - 优化首屏加载
 * @param func 要延迟执行的函数
 * @param delay 延迟时间（毫秒）
 */
export function defer(func: () => void, delay = 0): void {
  setTimeout(func, delay)
}

/**
 * 空闲时执行 - 利用浏览器空闲时间
 * @param func 要执行的函数
 * @param timeout 超时时间（毫秒）
 */
export function requestIdleCallback(func: () => void, timeout = 5000): void {
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(func, { timeout })
  } else {
    // 降级处理
    setTimeout(func, 1)
  }
}

/**
 * 分片执行大量任务 - 避免阻塞主线程
 * @param tasks 任务数组
 * @param chunkSize 每次执行的任务数量
 * @param delay 每次执行间隔（毫秒）
 */
export async function executeInChunks<T>(
  tasks: T[],
  processor: (item: T) => void | Promise<void>,
  chunkSize = 10,
  delay = 5
): Promise<void> {
  for (let i = 0; i < tasks.length; i += chunkSize) {
    const chunk = tasks.slice(i, i + chunkSize)
    
    for (const task of chunk) {
      await processor(task)
    }
    
    // 让出控制权，避免阻塞
    if (i + chunkSize < tasks.length) {
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
}

/**
 * 懒加载图片 - 优化图片加载性能
 * @param img 图片元素
 * @param src 图片源地址
 */
export function lazyLoadImage(img: HTMLImageElement, src: string): void {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const target = entry.target as HTMLImageElement
        target.src = src
        target.classList.remove('lazy')
        observer.unobserve(target)
      }
    })
  })
  
  img.classList.add('lazy')
  observer.observe(img)
}

/**
 * 预加载关键资源
 * @param urls 资源URL数组
 * @param type 资源类型
 */
export function preloadResources(urls: string[], type: 'script' | 'style' | 'image' = 'script'): void {
  urls.forEach(url => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = url
    
    switch (type) {
      case 'script':
        link.as = 'script'
        break
      case 'style':
        link.as = 'style'
        break
      case 'image':
        link.as = 'image'
        break
    }
    
    document.head.appendChild(link)
  })
}

/**
 * 内存优化 - 清理不需要的引用
 */
export class MemoryManager {
  private static refs = new Set<any>()
  
  static addRef(ref: any): void {
    this.refs.add(ref)
  }
  
  static removeRef(ref: any): void {
    this.refs.delete(ref)
  }
  
  static cleanup(): void {
    this.refs.clear()
    
    // 强制垃圾回收（如果可用）
    if ('gc' in window && typeof window.gc === 'function') {
      window.gc()
    }
  }
}

/**
 * 性能监控
 */
export class PerformanceMonitor {
  private static marks = new Map<string, number>()
  
  static mark(name: string): void {
    this.marks.set(name, performance.now())
  }
  
  static measure(name: string, startMark: string): number {
    const startTime = this.marks.get(startMark)
    if (!startTime) {
      console.warn(`Start mark "${startMark}" not found`)
      return 0
    }
    
    const duration = performance.now() - startTime
    console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`)
    return duration
  }
  
  static getMemoryUsage(): any {
    if ('memory' in performance) {
      return (performance as any).memory
    }
    return null
  }
  
  static getFID(): Promise<number> {
    return new Promise((resolve) => {
      let firstInput = true
      
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (firstInput && entry.name === 'first-input') {
            firstInput = false
            resolve((entry as any).processingStart - entry.startTime)
            observer.disconnect()
          }
        }
      })
      
      observer.observe({ entryTypes: ['first-input'] })
    })
  }
}

/**
 * 组件懒加载工具
 */
export function defineAsyncComponent(loader: () => Promise<any>) {
  return () => ({
    component: loader(),
    loading: () => import('@/components/common/SkeletonLoader.vue'),
    error: () => import('@/components/common/ErrorBoundary.vue'),
    delay: 200,
    timeout: 10000
  })
}

/**
 * 优化事件监听器
 */
export class OptimizedEventListener {
  private static listeners = new Map<string, Set<EventListener>>()
  
  static add(
    element: Element | Window | Document,
    event: string,
    handler: EventListener,
    options?: AddEventListenerOptions
  ): void {
    const key = `${event}_${element.constructor.name}`
    
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set())
    }
    
    this.listeners.get(key)!.add(handler)
    element.addEventListener(event, handler, { passive: true, ...options })
  }
  
  static remove(
    element: Element | Window | Document,
    event: string,
    handler: EventListener
  ): void {
    const key = `${event}_${element.constructor.name}`
    const handlers = this.listeners.get(key)
    
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.listeners.delete(key)
      }
    }
    
    element.removeEventListener(event, handler)
  }
  
  static cleanup(): void {
    this.listeners.clear()
  }
}

/**
 * 虚拟滚动优化
 */
export function createVirtualScroll(
  container: HTMLElement,
  itemHeight: number,
  items: any[],
  renderItem: (item: any, index: number) => HTMLElement
) {
  const containerHeight = container.clientHeight
  const visibleCount = Math.ceil(containerHeight / itemHeight) + 2
  let scrollTop = 0
  
  const render = throttle(() => {
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(startIndex + visibleCount, items.length)
    
    container.innerHTML = ''
    container.style.height = `${items.length * itemHeight}px`
    container.style.paddingTop = `${startIndex * itemHeight}px`
    
    for (let i = startIndex; i < endIndex; i++) {
      const element = renderItem(items[i], i)
      container.appendChild(element)
    }
  }, 16)
  
  container.addEventListener('scroll', (e) => {
    scrollTop = (e.target as HTMLElement).scrollTop
    render()
  }, { passive: true })
  
  render()
}
