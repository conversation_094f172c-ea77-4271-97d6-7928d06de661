<template>
  <div class="security-settings">
    <div class="settings-section">
      <h3 class="section-title">
        <el-icon><Lock /></el-icon>
        安全设置
      </h3>
      <p class="section-description">管理您的账户安全设置</p>

      <!-- 密码修改 -->
      <el-card class="security-card" shadow="never">
        <template #header>
          <span>修改密码</span>
        </template>
        
        <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="120px">
          <el-form-item label="当前密码" prop="currentPassword">
            <el-input v-model="passwordForm.currentPassword" type="password" show-password />
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input v-model="passwordForm.newPassword" type="password" show-password />
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="changePassword" :loading="changing">
              修改密码
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 两步验证 -->
      <el-card class="security-card" shadow="never">
        <template #header>
          <span>两步验证</span>
        </template>
        
        <div class="two-factor-section">
          <div class="two-factor-status">
            <el-tag :type="twoFactorEnabled ? 'success' : 'warning'">
              {{ twoFactorEnabled ? '已启用' : '未启用' }}
            </el-tag>
          </div>
          <el-button 
            :type="twoFactorEnabled ? 'danger' : 'primary'"
            @click="toggleTwoFactor"
            :loading="toggling"
          >
            {{ twoFactorEnabled ? '禁用' : '启用' }}两步验证
          </el-button>
        </div>
      </el-card>

      <!-- 登录设备 -->
      <el-card class="security-card" shadow="never">
        <template #header>
          <span>登录设备</span>
        </template>
        
        <el-table :data="loginDevices" stripe>
          <el-table-column prop="device" label="设备" />
          <el-table-column prop="location" label="位置" />
          <el-table-column prop="lastLogin" label="最后登录" />
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag :type="row.current ? 'success' : 'info'">
                {{ row.current ? '当前设备' : '其他设备' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{ row }">
              <el-button 
                v-if="!row.current" 
                link 
                type="danger" 
                @click="logoutDevice(row)"
              >
                注销
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Lock } from '@element-plus/icons-vue'

const passwordFormRef = ref()
const changing = ref(false)
const toggling = ref(false)
const twoFactorEnabled = ref(false)

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const loginDevices = ref([
  {
    id: '1',
    device: 'Chrome on Windows',
    location: '北京市',
    lastLogin: '2025-07-29 14:30:00',
    current: true
  },
  {
    id: '2',
    device: 'Safari on iPhone',
    location: '上海市',
    lastLogin: '2025-07-28 09:15:00',
    current: false
  }
])

const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    changing.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('密码修改成功')
    passwordFormRef.value.resetFields()
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    changing.value = false
  }
}

const toggleTwoFactor = async () => {
  toggling.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    twoFactorEnabled.value = !twoFactorEnabled.value
    ElMessage.success(`两步验证已${twoFactorEnabled.value ? '启用' : '禁用'}`)
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    toggling.value = false
  }
}

const logoutDevice = async (device: any) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const index = loginDevices.value.findIndex(d => d.id === device.id)
    if (index > -1) {
      loginDevices.value.splice(index, 1)
      ElMessage.success('设备已注销')
    }
  } catch (error) {
    ElMessage.error('注销失败')
  }
}
</script>

<style scoped>
.security-settings {
  max-width: 800px;
}

.settings-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.section-description {
  color: #666;
  font-size: 14px;
  margin: 0 0 24px 0;
}

.security-card {
  margin-bottom: 24px;
  border: 1px solid #e6e6e6;
}

.two-factor-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
}
</style>
