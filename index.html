<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台</title>

    <!-- 性能优化：DNS预解析 -->
    <link rel="dns-prefetch" href="//localhost:8000">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">

    <!-- 性能优化：预连接 -->
    <link rel="preconnect" href="//localhost:8000">

    <!-- 性能优化：预加载关键资源 -->
    <link rel="preload" href="/src/main.ts" as="script" crossorigin="anonymous">
    <link rel="preload" href="/src/App.vue" as="fetch" crossorigin="anonymous">

    <!-- 安全头：内容安全策略 -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self' ws://localhost:8000 http://localhost:8000 ws://localhost:5173 http://localhost:5173;">

    <!-- 注意：X-Frame-Options等安全头应该在服务器端设置，而不是在meta标签中 -->
    <!-- 这些meta标签会导致控制台警告，已移除 -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">

    <!-- 缓存策略优化 -->
    <meta http-equiv="Cache-Control" content="public, max-age=31536000">
    <meta name="format-detection" content="telephone=no">

    <!-- PWA支持 -->
    <meta name="theme-color" content="#409EFF">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <!-- 性能优化：关键CSS内联 -->
    <style>
      /* 关键CSS - 首屏渲染优化 */
      #app {
        width: 100%;
        height: 100vh;
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      /* 加载动画 */
      .app-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e4e7ed;
        border-top: 4px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 首屏加载动画 -->
      <div class="app-loading">
        <div class="loading-spinner"></div>
      </div>
    </div>

    <!-- 性能优化：模块预加载 -->
    <script type="module" src="/src/main.ts"></script>

    <!-- 性能监控脚本 -->
    <script>
      // 首屏性能监控
      window.addEventListener('load', function() {
        // 移除加载动画
        const loading = document.querySelector('.app-loading');
        if (loading) {
          loading.style.opacity = '0';
          setTimeout(() => loading.remove(), 300);
        }

        // 性能数据收集
        if ('performance' in window && performance.getEntriesByType) {
          try {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData && perfData.loadEventEnd && perfData.navigationStart) {
              const loadTime = perfData.loadEventEnd - perfData.navigationStart;
              if (loadTime > 0) {
                console.log('📊 页面加载时间:', loadTime + 'ms');

                // 如果加载时间超过3秒，记录警告
                if (loadTime > 3000) {
                  console.warn('⚠️ 页面加载较慢，建议优化');
                }
              }
            }
          } catch (error) {
            console.log('📊 性能监控初始化失败:', error.message);
          }
        }
      });

      // 错误监控
      window.addEventListener('error', function(e) {
        console.error('🚨 页面错误:', e.error || e.message);
      });

      // 未处理的Promise错误
      window.addEventListener('unhandledrejection', function(e) {
        console.error('🚨 未处理的Promise错误:', e.reason);
      });
    </script>
  </body>
</html>
