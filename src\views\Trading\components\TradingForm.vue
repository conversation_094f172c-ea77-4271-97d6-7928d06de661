<template>
  <div class="trading-form">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
      <el-form-item label="交易类型" prop="type">
        <el-radio-group v-model="form.type">
          <el-radio label="limit">限价单</el-radio>
          <el-radio label="market">市价单</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="数量" prop="quantity">
        <el-input-number
          v-model="form.quantity"
          :min="100"
          :step="100"
          :max="maxQuantity"
          style="width: 100%"
          placeholder="请输入交易数量"
        />
        <div class="quantity-tips">
          <span>最小: 100股</span>
          <span v-if="side === 'sell' && availableQuantity">
            可卖: {{ availableQuantity }}股
          </span>
        </div>
      </el-form-item>

      <el-form-item v-if="form.type === 'limit'" label="价格" prop="price">
        <el-input-number
          v-model="form.price"
          :precision="2"
          :step="0.01"
          :min="0.01"
          style="width: 100%"
          placeholder="请输入交易价格"
        />
        <div class="price-tips">
          <el-button-group size="small">
            <el-button @click="setPrice('current')">现价</el-button>
            <el-button @click="setPrice('buy1')">买一</el-button>
            <el-button @click="setPrice('sell1')">卖一</el-button>
          </el-button-group>
        </div>
      </el-form-item>

      <el-form-item label="金额预估">
        <div class="amount-info">
          <div class="amount-row">
            <span>交易金额:</span>
            <span class="amount">¥{{ formatMoney(estimatedAmount) }}</span>
          </div>
          <div class="amount-row">
            <span>手续费:</span>
            <span class="fee">¥{{ formatMoney(estimatedFee) }}</span>
          </div>
          <div class="amount-row total">
            <span>{{ side === 'buy' ? '需要资金:' : '到账金额:' }}</span>
            <span class="total-amount">¥{{ formatMoney(totalAmount) }}</span>
          </div>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button 
          type="primary" 
          :class="side === 'buy' ? 'buy-button' : 'sell-button'"
          style="width: 100%"
          size="large"
          @click="submitOrder"
          :loading="submitting"
          :disabled="!canSubmit"
        >
          {{ side === 'buy' ? '买入' : '卖出' }}
          {{ stock?.name || '' }}
        </el-button>
      </el-form-item>

      <div class="account-info">
        <div class="info-row">
          <span>账户类型:</span>
          <el-tag :type="mode === 'real' ? 'danger' : 'success'">
            {{ mode === 'real' ? '实盘账户' : '模拟账户' }}
          </el-tag>
        </div>
        <div class="info-row">
          <span>可用资金:</span>
          <span class="funds">¥{{ formatMoney(account?.availableFunds || 0) }}</span>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// Props
interface Props {
  mode: 'simulation' | 'real'
  stock: {
    symbol: string
    name: string
    currentPrice: number
  } | null
  account: {
    availableFunds: number
  } | null
  side: 'buy' | 'sell'
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  submit: [orderData: any]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)

const form = reactive({
  type: 'limit',
  quantity: 100,
  price: 0
})

const availableQuantity = ref(1000) // 模拟可卖数量

// 表单验证规则
const rules: FormRules = {
  quantity: [
    { required: true, message: '请输入交易数量', trigger: 'blur' },
    { type: 'number', min: 100, message: '最小交易数量为100股', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入交易价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '价格必须大于0.01', trigger: 'blur' }
  ]
}

// 计算属性
const maxQuantity = computed(() => {
  if (props.side === 'sell') {
    return availableQuantity.value
  }
  if (props.side === 'buy' && props.account && props.stock) {
    const maxByFunds = Math.floor(props.account.availableFunds / props.stock.currentPrice / 100) * 100
    return Math.max(100, maxByFunds)
  }
  return 10000
})

const estimatedAmount = computed(() => {
  if (!form.quantity || !props.stock) return 0
  
  const price = form.type === 'market' ? props.stock.currentPrice : form.price
  return form.quantity * price
})

const estimatedFee = computed(() => {
  // 简化的手续费计算：万分之三，最低5元
  const feeRate = 0.0003
  const fee = estimatedAmount.value * feeRate
  return Math.max(fee, 5)
})

const totalAmount = computed(() => {
  if (props.side === 'buy') {
    return estimatedAmount.value + estimatedFee.value
  } else {
    return estimatedAmount.value - estimatedFee.value
  }
})

const canSubmit = computed(() => {
  if (!props.stock || !form.quantity) return false
  if (form.type === 'limit' && !form.price) return false
  if (props.side === 'buy' && props.account) {
    return totalAmount.value <= props.account.availableFunds
  }
  if (props.side === 'sell') {
    return form.quantity <= availableQuantity.value
  }
  return true
})

// 方法
const formatMoney = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

const setPrice = (type: 'current' | 'buy1' | 'sell1') => {
  if (!props.stock) return
  
  switch (type) {
    case 'current':
      form.price = props.stock.currentPrice
      break
    case 'buy1':
      form.price = props.stock.currentPrice - 0.01
      break
    case 'sell1':
      form.price = props.stock.currentPrice + 0.01
      break
  }
}

const submitOrder = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const orderData = {
      symbol: props.stock?.symbol,
      side: props.side,
      type: form.type,
      quantity: form.quantity,
      price: form.type === 'limit' ? form.price : props.stock?.currentPrice,
      estimatedAmount: estimatedAmount.value,
      estimatedFee: estimatedFee.value,
      totalAmount: totalAmount.value
    }
    
    // 模拟提交延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('submit', orderData)
    
    // 重置表单
    form.quantity = 100
    if (form.type === 'limit') {
      form.price = props.stock?.currentPrice || 0
    }
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

// 监听股票变化，自动设置价格
watch(() => props.stock, (newStock) => {
  if (newStock && form.type === 'limit') {
    form.price = newStock.currentPrice
  }
}, { immediate: true })

// 监听交易类型变化
watch(() => form.type, (newType) => {
  if (newType === 'limit' && props.stock) {
    form.price = props.stock.currentPrice
  }
})
</script>

<style scoped lang="scss">
.trading-form {
  .quantity-tips {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
  
  .price-tips {
    margin-top: 8px;
    
    .el-button-group {
      width: 100%;
      
      .el-button {
        flex: 1;
      }
    }
  }
  
  .amount-info {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    
    .amount-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 14px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      &.total {
        border-top: 1px solid #e4e7ed;
        padding-top: 8px;
        font-weight: 600;
        
        .total-amount {
          color: #409eff;
          font-size: 16px;
        }
      }
      
      .amount {
        color: #303133;
        font-weight: 500;
      }
      
      .fee {
        color: #f56c6c;
      }
    }
  }
  
  .buy-button {
    background: #f56c6c;
    border-color: #f56c6c;
    
    &:hover {
      background: #f78989;
      border-color: #f78989;
    }
  }
  
  .sell-button {
    background: #67c23a;
    border-color: #67c23a;
    
    &:hover {
      background: #85ce61;
      border-color: #85ce61;
    }
  }
  
  .account-info {
    background: #f0f9ff;
    border-radius: 6px;
    padding: 12px;
    margin-top: 16px;
    
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .funds {
        color: #67c23a;
        font-weight: 600;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .trading-form {
    .price-tips {
      .el-button-group {
        .el-button {
          padding: 8px 12px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
