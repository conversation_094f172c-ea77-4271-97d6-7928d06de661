import { httpClient } from './http'
import type {
  ApiResponse,
  ListResponse,
  BacktestData,
  QueryParams
} from '@/types/api'
import type {
  Backtest,
  BacktestConfig,
  BacktestResult,
  BacktestReport,
  BacktestProgress,
  OptimizationResult,
  Strategy
} from '@/types/backtest'
import type { PaginationRequest, PaginationResponse } from '@/types'

const BASE_URL = '/backtest'

// 配置
const USE_MOCK = import.meta.env.VITE_USE_MOCK === 'true' || import.meta.env.DEV

export interface BacktestCreateRequest {
  name: string
  strategyId: string
  symbol: string
  startDate: string
  endDate: string
  initialCash: number
  parameters?: Record<string, any>
}

export interface BacktestUpdateRequest {
  name?: string
  isFavorite?: boolean
}

// 生成完整的模拟BacktestData
function createMockBacktestData(
  id: string,
  name: string,
  strategyName: string,
  symbol: string,
  status: BacktestData['status'] = 'COMPLETED'
): BacktestData {
  const baseReturn = Math.random() * 0.3 + 0.05 // 5% - 35%
  const initialCash = Math.floor(Math.random() * 400000) + 100000 // 10万-50万
  
  return {
    id,
    name,
    strategy: strategyName,
    strategyId: id.replace('backtest', 'strategy'),
    strategyName,
    symbol,
    startDate: '2024-01-01',
    endDate: '2024-06-30',
    initialCash,
    status,
    totalReturn: Math.floor(initialCash * baseReturn),
    totalReturnPercent: +(baseReturn * 100).toFixed(2),
    annualReturn: +(baseReturn * 2).toFixed(2),
    maxDrawdown: Math.floor(initialCash * 0.08),
    maxDrawdownPercent: +(Math.random() * 10 + 2).toFixed(1),
    sharpeRatio: +(Math.random() * 1.5 + 1).toFixed(2),
    sortinoRatio: +(Math.random() * 1.8 + 1.2).toFixed(2),
    calmarRatio: +(Math.random() * 2 + 2.5).toFixed(1),
    winRate: +(Math.random() * 30 + 50).toFixed(1),
    profitFactor: +(Math.random() * 0.8 + 1.1).toFixed(1),
    totalTrades: Math.floor(Math.random() * 100 + 50),
    progress: status === 'COMPLETED' ? 100 : Math.floor(Math.random() * 50 + 40),
    isFavorite: Math.random() > 0.7,
    createTime: '2024-07-01T10:00:00Z',
    updateTime: '2024-07-01T15:30:00Z'
  }
}

/**
 * 回测相关API接口
 */
export const backtestApi = {
  // ============ 回测管理 ============

  /**
   * 获取回测列表
   */
  getBacktests: async (params?: QueryParams): Promise<ListResponse<BacktestData>> => {
    if (USE_MOCK) {
      const mockBacktests: BacktestData[] = [
        createMockBacktestData('backtest-001', '双均线策略回测', '双均线策略', '000001'),
        createMockBacktestData('backtest-002', 'RSI反转策略回测', 'RSI反转策略', '600036'),
        createMockBacktestData('backtest-003', '多因子选股回测', '多因子选股策略', '000858', 'RUNNING')
      ]

      return {
        data: mockBacktests,
        total: mockBacktests.length,
        page: params?.page || 1,
        pageSize: params?.pageSize || 20,
        hasNext: false
      }
    }

    try {
      const endpoint = '/api/v1/backtest'
      const response = await httpClient.get(endpoint, { params })
      return (response.data as any).data ?? response.data
    } catch (error) {
      console.warn('回测列表API调用失败，使用模拟数据:', error)
      
      const fallbackMockBacktests: BacktestData[] = [
        createMockBacktestData('backtest-001', '双均线策略回测', '双均线策略', '000001'),
        createMockBacktestData('backtest-002', 'MACD策略回测', 'MACD策略', '000002'),
        createMockBacktestData('backtest-003', 'RSI策略回测', 'RSI策略', '000858')
      ]

      return {
        data: fallbackMockBacktests,
        total: fallbackMockBacktests.length,
        page: params?.page || 1,
        pageSize: params?.pageSize || 20,
        hasNext: false
      }
    }
  },

  /**
   * 获取单个回测详情
   */
  getBacktest: async (id: string): Promise<ApiResponse<BacktestData>> => {
    if (USE_MOCK) {
      const mockBacktest = createMockBacktestData(id, '模拟回测详情', '模拟策略', '000001')
      
      return {
        success: true,
        message: '获取成功',
        code: 200,
        timestamp: Date.now(),
        data: mockBacktest
      }
    }

    try {
      const endpoint = `/api/v1/backtest/${id}`
      const response = await httpClient.get(endpoint)
      return response.data
    } catch (error) {
      console.warn(`回测详情API调用失败(${id})，使用模拟数据:`, error)
      
      const mockBacktest = createMockBacktestData(id, '回测详情（离线）', '离线策略', '000001')
      
      return {
        success: true,
        message: '获取成功（模拟数据）',
        code: 200,
        timestamp: Date.now(),
        data: mockBacktest
      }
    }
  },

  /**
   * 创建回测任务
   */
  createBacktest: async (data: BacktestCreateRequest): Promise<ApiResponse<BacktestData>> => {
    if (USE_MOCK) {
      const newId = `backtest-${Date.now()}`
      const mockBacktest = createMockBacktestData(newId, data.name, '新建策略', data.symbol, 'PENDING')
      
      return {
        success: true,
        message: '创建成功',
        code: 200,
        timestamp: Date.now(),
        data: mockBacktest
      }
    }

    try {
      const endpoint = '/api/v1/backtest'
      const response = await httpClient.post(endpoint, data)
      return response.data
    } catch (error) {
      console.warn('创建回测API调用失败，使用模拟数据:', error)
      
      const mockBacktest = createMockBacktestData('backtest-new', data.name, '新建策略', data.symbol, 'PENDING')
      
      return {
        success: true,
        message: '创建成功（模拟）',
        code: 200,
        timestamp: Date.now(),
        data: mockBacktest
      }
    }
  },

  /**
   * 更新回测
   */
  updateBacktest: async (id: string, data: BacktestUpdateRequest): Promise<ApiResponse<BacktestData>> => {
    if (USE_MOCK) {
      const mockBacktest = createMockBacktestData(id, data.name || '更新的回测', '更新策略', '000001')
      if (data.isFavorite !== undefined) {
        mockBacktest.isFavorite = data.isFavorite
      }
      
      return {
        success: true,
        message: '更新成功',
        code: 200,
        timestamp: Date.now(),
        data: mockBacktest
      }
    }

    try {
      const endpoint = `/api/v1/backtest/${id}`
      const response = await httpClient.put(endpoint, data)
      return response.data
    } catch (error) {
      console.warn(`更新回测API调用失败(${id})，使用模拟数据:`, error)
      
      const mockBacktest = createMockBacktestData(id, data.name || '更新的回测', '更新策略', '000001')
      
      return {
        success: true,
        message: '更新成功（模拟）',
        code: 200,
        timestamp: Date.now(),
        data: mockBacktest
      }
    }
  },

  /**
   * 删除回测
   */
  deleteBacktest: async (id: string): Promise<ApiResponse<void>> => {
    if (USE_MOCK) {
      return {
        success: true,
        message: '删除成功',
        code: 200,
        timestamp: Date.now()
      }
    }

    try {
      const endpoint = `/api/v1/backtest/${id}`
      const response = await httpClient.delete(endpoint)
      return response.data
    } catch (error) {
      console.warn(`删除回测API调用失败(${id})，返回模拟成功:`, error)
      return {
        success: true,
        message: '删除成功（模拟）',
        code: 200,
        timestamp: Date.now()
      }
    }
  },

  // ============ 回测执行控制 ============

  /**
   * 开始回测
   */
  startBacktest: async (id: string): Promise<ApiResponse<void>> => {
    if (USE_MOCK) {
      return {
        success: true,
        message: '回测已启动',
        code: 200,
        timestamp: Date.now()
      }
    }

    const endpoint = `/api/v1/backtest/${id}/start`
    const response = await httpClient.post(endpoint)
    return response.data
  },

  /**
   * 停止回测
   */
  stopBacktest: async (id: string): Promise<ApiResponse<void>> => {
    if (USE_MOCK) {
      return {
        success: true,
        message: '回测已停止',
        code: 200,
        timestamp: Date.now()
      }
    }

    const endpoint = `/api/v1/backtest/${id}/stop`
    const response = await httpClient.post(endpoint)
    return response.data
  },

  /**
   * 获取回测进度
   */
  getBacktestProgress: async (id: string): Promise<BacktestProgress> => {
    if (USE_MOCK) {
      return {
        backtestId: id,
        progress: Math.floor(Math.random() * 100),
        status: 'running',
        message: '正在执行回测...',
        currentDate: '2024-03-15',
        estimatedTimeRemaining: Math.floor(Math.random() * 300) + 60
      }
    }

    const endpoint = `/api/v1/backtest/${id}/progress`
    const response = await httpClient.get(endpoint)
    return response.data
  },

  /**
   * 获取回测报告
   */
  getBacktestReport: async (id: string): Promise<BacktestReport> => {
    if (USE_MOCK) {
      return {
        id: `report-${id}`,
        taskId: id,
        title: '回测报告',
        summary: '本次回测表现良好，收益率达到预期目标',
        totalReturn: 15.6,
        annualizedReturn: 23.4,
        maxDrawdown: 8.2,
        sharpeRatio: 1.85,
        generateTime: Date.now()
      }
    }

    const endpoint = `/api/v1/backtest/${id}/report`
    const response = await httpClient.get(endpoint)
    return response.data
  },

  // ============ 策略优化 ============

  /**
   * 优化策略参数
   */
  optimizeStrategy: async (strategyId: string, params: any): Promise<OptimizationResult> => {
    if (USE_MOCK) {
      return {
        parameters: {
          param1: 'optimized_value_1',
          param2: 'optimized_value_2'
        },
        metrics: {
          return: 18.5,
          sharpe: 2.1,
          maxDrawdown: 6.8
        },
        rank: 1,
        score: 95.6
      }
    }

    const endpoint = `/api/v1/strategy/${strategyId}/optimize`
    const response = await httpClient.post(endpoint, params)
    return response.data
  },

  /**
   * 获取策略列表
   */
  getStrategies: async (): Promise<Strategy[]> => {
    if (USE_MOCK) {
      return [
        {
          id: 'strategy-001',
          name: '双均线策略',
          type: '趋势跟踪',
          description: '基于双移动平均线的趋势跟踪策略'
        },
        {
          id: 'strategy-002',
          name: 'RSI反转策略', 
          type: '超买超卖',
          description: '基于RSI指标的反转策略'
        }
      ]
    }

    const endpoint = '/api/v1/strategies'
    const response = await httpClient.get(endpoint)
    return response.data
  }
}

// 导出默认API
export default backtestApi