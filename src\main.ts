/**
 * 量化投资平台主入口文件 - 基础测试版
 */
console.log('🚀 JavaScript开始执行...')

// 先测试基本的DOM操作
document.addEventListener('DOMContentLoaded', () => {
  console.log('📍 DOM已加载')
  const appElement = document.getElementById('app')
  if (appElement) {
    console.log('✅ 找到#app元素')
    appElement.innerHTML = `
      <div style="padding: 2rem; text-align: center; font-family: Arial, sans-serif; min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div style="background: white; padding: 3rem; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2c3e50; margin-bottom: 1rem;">🚀 量化投资平台</h1>
          <p style="color: #6b7280; margin-bottom: 2rem;">基础JavaScript已成功执行！</p>
          <button onclick="loadVueApp()" style="padding: 12px 24px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 1rem;">
            🔄 加载Vue应用
          </button>
          <div id="status" style="margin-top: 1rem; padding: 1rem; background: #f0f9ff; border-radius: 8px;">
            <p style="color: #0c4a6e;">JavaScript执行正常，等待Vue应用加载...</p>
          </div>
        </div>
      </div>
    `
    console.log('✅ 基础HTML已渲染')
  } else {
    console.error('❌ 未找到#app元素')
  }
})

// 全局函数用于加载Vue应用
window.loadVueApp = async function() {
  const statusElement = document.getElementById('status')
  if (statusElement) {
    statusElement.innerHTML = '<p style="color: #0c4a6e;">正在加载Vue应用...</p>'
  }

  try {
    console.log('📍 开始加载Vue...')
    const { createApp } = await import('vue')
    console.log('✅ Vue导入成功')

    // 创建简单的Vue应用
    const vueApp = createApp({
      template: `
        <div style="padding: 2rem; text-align: center; font-family: Arial, sans-serif; min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
          <div style="background: white; padding: 3rem; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto;">
            <h1 style="color: #2c3e50; margin-bottom: 1rem;">🚀 量化投资平台</h1>
            <p style="color: #6b7280; margin-bottom: 2rem;">Vue应用已成功启动！</p>

            <div style="margin-bottom: 2rem;">
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #3b82f6;">
                  <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">📊 仪表盘</h3>
                  <p style="color: #6b7280; font-size: 0.9rem;">投资数据概览</p>
                </div>
                <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #10b981;">
                  <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">📈 市场行情</h3>
                  <p style="color: #6b7280; font-size: 0.9rem;">实时市场数据</p>
                </div>
                <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #f59e0b;">
                  <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">💰 智能交易</h3>
                  <p style="color: #6b7280; font-size: 0.9rem;">自动化交易系统</p>
                </div>
                <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #8b5cf6;">
                  <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">🧠 策略研发</h3>
                  <p style="color: #6b7280; font-size: 0.9rem;">量化策略开发</p>
                </div>
              </div>
            </div>

            <div style="margin-bottom: 2rem;">
              <button @click="loadFullApp" style="padding: 12px 24px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 1rem; margin-right: 1rem;">
                🔄 加载完整应用
              </button>
              <button @click="testApi" style="padding: 12px 24px; background: #10b981; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 1rem;">
                🔧 测试API
              </button>
            </div>

            <div v-if="status" style="margin-top: 1rem; padding: 1rem; background: #f0f9ff; border-radius: 8px; border-left: 4px solid #0ea5e9;">
              <p style="color: #0c4a6e;">{{ status }}</p>
            </div>
          </div>
        </div>
      `,
      data() {
        return {
          status: ''
        }
      },
      methods: {
        async loadFullApp() {
          this.status = '正在加载完整应用...'
          // 这里可以加载完整的应用
          this.status = '完整应用功能开发中...'
        },
        async testApi() {
          this.status = '正在测试API连接...'
          try {
            const response = await fetch('http://localhost:8000/api/v1/market/overview')
            if (response.ok) {
              const data = await response.json()
              this.status = `✅ API连接成功！`
            } else {
              this.status = `❌ API返回错误: ${response.status}`
            }
          } catch (error) {
            this.status = `❌ API连接失败: ${error.message}`
          }
        }
      }
    })

    // 挂载Vue应用
    vueApp.mount('#app')
    console.log('✅ Vue应用挂载成功')

  } catch (error) {
    console.error('❌ Vue应用加载失败:', error)
    if (statusElement) {
      statusElement.innerHTML = `<p style="color: #dc2626;">Vue应用加载失败: ${error.message}</p>`
    }
  }
}

console.log('✅ 主脚本执行完成')

// 先创建一个简单的测试应用
const testApp = createApp({
  template: `
    <div style="padding: 2rem; text-align: center; font-family: Arial, sans-serif; min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
      <div style="background: white; padding: 3rem; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2c3e50; margin-bottom: 1rem;">🚀 量化投资平台</h1>
        <p style="color: #6b7280; margin-bottom: 2rem;">Vue应用已成功启动！</p>

        <div style="margin-bottom: 2rem;">
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #3b82f6;">
              <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">📊 仪表盘</h3>
              <p style="color: #6b7280; font-size: 0.9rem;">投资数据概览</p>
            </div>
            <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #10b981;">
              <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">📈 市场行情</h3>
              <p style="color: #6b7280; font-size: 0.9rem;">实时市场数据</p>
            </div>
            <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #f59e0b;">
              <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">💰 智能交易</h3>
              <p style="color: #6b7280; font-size: 0.9rem;">自动化交易系统</p>
            </div>
            <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #8b5cf6;">
              <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">🧠 策略研发</h3>
              <p style="color: #6b7280; font-size: 0.9rem;">量化策略开发</p>
            </div>
          </div>
        </div>

        <div style="margin-bottom: 2rem;">
          <button @click="loadFullApp" style="padding: 12px 24px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 1rem; margin-right: 1rem;">
            🔄 加载完整应用
          </button>
          <button @click="testApi" style="padding: 12px 24px; background: #10b981; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 1rem;">
            🔧 测试API
          </button>
        </div>

        <div v-if="status" style="margin-top: 1rem; padding: 1rem; background: #f0f9ff; border-radius: 8px; border-left: 4px solid #0ea5e9;">
          <p style="color: #0c4a6e;">{{ status }}</p>
        </div>
      </div>
    </div>
  `,
  data() {
    return {
      status: ''
    }
  },
  methods: {
    async loadFullApp() {
      this.status = '正在加载完整应用...'
      try {
        // 动态导入完整应用
        const { createPinia } = await import('pinia')
        const ElementPlus = await import('element-plus')
        const router = await import('./router')
        const App = await import('./App.vue')

        // 创建新的应用实例
        const { createApp } = await import('vue')
        const fullApp = createApp(App.default)

        // 配置插件
        fullApp.use(createPinia())
        fullApp.use(ElementPlus.default)
        fullApp.use(router.default)

        // 卸载当前应用并挂载新应用
        this.$el.parentNode.innerHTML = '<div id="app"></div>'
        fullApp.mount('#app')

        console.log('✅ 完整应用加载成功')
      } catch (error) {
        console.error('❌ 完整应用加载失败:', error)
        this.status = `加载失败: ${error.message}`
      }
    },
    async testApi() {
      this.status = '正在测试API连接...'
      try {
        const response = await fetch('http://localhost:8000/api/v1/market/overview')
        if (response.ok) {
          const data = await response.json()
          this.status = `✅ API连接成功！返回数据: ${JSON.stringify(data).substring(0, 100)}...`
        } else {
          this.status = `❌ API返回错误: ${response.status}`
        }
      } catch (error) {
        this.status = `❌ API连接失败: ${error.message}`
      }
    }
  }
})

// 挂载测试应用
testApp.mount('#app')
console.log('✅ 测试应用启动成功')
