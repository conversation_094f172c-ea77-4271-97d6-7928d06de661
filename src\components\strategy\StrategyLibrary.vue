<template>
  <div class="strategy-library">
    <div class="library-header">
      <div class="header-left">
        <h3>策略库</h3>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索策略..."
          style="width: 300px; margin-left: 20px;"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="header-actions">
        <el-button size="small" @click="showUploadDialog = true">
          <el-icon><Upload /></el-icon>
          上传策略
        </el-button>
        <el-button size="small" @click="refreshLibrary">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div class="library-filters">
      <el-space wrap>
        <el-select v-model="selectedCategory" placeholder="策略分类" clearable size="small">
          <el-option label="全部" value="" />
          <el-option label="趋势策略" value="trend" />
          <el-option label="反转策略" value="reversal" />
          <el-option label="套利策略" value="arbitrage" />
          <el-option label="量化策略" value="quant" />
          <el-option label="自定义策略" value="custom" />
        </el-select>
        
        <el-select v-model="selectedRisk" placeholder="风险等级" clearable size="small">
          <el-option label="低风险" value="low" />
          <el-option label="中风险" value="medium" />
          <el-option label="高风险" value="high" />
        </el-select>
        
        <el-select v-model="sortBy" placeholder="排序方式" size="small">
          <el-option label="创建时间" value="created_at" />
          <el-option label="收益率" value="return" />
          <el-option label="夏普比率" value="sharpe" />
          <el-option label="使用次数" value="usage" />
        </el-select>
        
        <el-switch
          v-model="showFavorites"
          active-text="仅显示收藏"
          inactive-text="显示全部"
        />
      </el-space>
    </div>

    <div class="strategy-grid">
      <div
        v-for="strategy in filteredStrategies"
        :key="strategy.id"
        class="strategy-card"
        @click="selectStrategy(strategy)"
      >
        <div class="card-header">
          <div class="strategy-info">
            <h4 class="strategy-name">{{ strategy.name }}</h4>
            <div class="strategy-meta">
              <el-tag :type="getCategoryType(strategy.category)" size="small">
                {{ getCategoryLabel(strategy.category) }}
              </el-tag>
              <el-tag :type="getRiskType(strategy.risk)" size="small">
                {{ getRiskLabel(strategy.risk) }}
              </el-tag>
            </div>
          </div>
          <div class="card-actions">
            <el-button
              size="mini"
              circle
              :type="strategy.favorite ? 'warning' : 'default'"
              @click.stop="toggleFavorite(strategy)"
            >
              <el-icon><Star /></el-icon>
            </el-button>
            <el-dropdown @command="handleCommand" trigger="click">
              <el-button size="mini" circle @click.stop>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`edit-${strategy.id}`">编辑</el-dropdown-item>
                  <el-dropdown-item :command="`clone-${strategy.id}`">克隆</el-dropdown-item>
                  <el-dropdown-item :command="`export-${strategy.id}`">导出</el-dropdown-item>
                  <el-dropdown-item :command="`delete-${strategy.id}`" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <div class="card-content">
          <p class="strategy-description">{{ strategy.description }}</p>
          
          <div class="strategy-metrics">
            <div class="metric-item">
              <span class="metric-label">年化收益率</span>
              <span class="metric-value" :class="getReturnClass(strategy.metrics.annualReturn)">
                {{ formatPercent(strategy.metrics.annualReturn) }}
              </span>
            </div>
            <div class="metric-item">
              <span class="metric-label">夏普比率</span>
              <span class="metric-value">{{ strategy.metrics.sharpeRatio?.toFixed(2) }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">最大回撤</span>
              <span class="metric-value negative">{{ formatPercent(strategy.metrics.maxDrawdown) }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">胜率</span>
              <span class="metric-value">{{ formatPercent(strategy.metrics.winRate) }}</span>
            </div>
          </div>

          <div class="strategy-stats">
            <div class="stat-item">
              <el-icon><User /></el-icon>
              <span>{{ strategy.author }}</span>
            </div>
            <div class="stat-item">
              <el-icon><Clock /></el-icon>
              <span>{{ formatDate(strategy.createdAt) }}</span>
            </div>
            <div class="stat-item">
              <el-icon><View /></el-icon>
              <span>{{ strategy.usage }}次使用</span>
            </div>
          </div>
        </div>

        <div class="card-footer">
          <el-button size="small" type="primary" @click.stop="useStrategy(strategy)">
            使用策略
          </el-button>
          <el-button size="small" @click.stop="viewDetails(strategy)">
            查看详情
          </el-button>
        </div>
      </div>

      <!-- 空状态 -->
      <el-empty v-if="filteredStrategies.length === 0" description="暂无策略" />
    </div>

    <!-- 策略详情对话框 -->
    <el-dialog v-model="showDetailsDialog" title="策略详情" width="80%" :before-close="closeDetailsDialog">
      <div class="strategy-details" v-if="selectedStrategyDetail">
        <el-row :gutter="20">
          <el-col :span="16">
            <div class="strategy-info-section">
              <h4>基本信息</h4>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="策略名称">{{ selectedStrategyDetail.name }}</el-descriptions-item>
                <el-descriptions-item label="策略分类">{{ getCategoryLabel(selectedStrategyDetail.category) }}</el-descriptions-item>
                <el-descriptions-item label="风险等级">{{ getRiskLabel(selectedStrategyDetail.risk) }}</el-descriptions-item>
                <el-descriptions-item label="作者">{{ selectedStrategyDetail.author }}</el-descriptions-item>
                <el-descriptions-item label="创建时间">{{ formatDate(selectedStrategyDetail.createdAt) }}</el-descriptions-item>
                <el-descriptions-item label="最后更新">{{ formatDate(selectedStrategyDetail.updatedAt) }}</el-descriptions-item>
              </el-descriptions>
              
              <div style="margin-top: 20px;">
                <h4>策略描述</h4>
                <p>{{ selectedStrategyDetail.description }}</p>
              </div>

              <div style="margin-top: 20px;">
                <h4>参数配置</h4>
                <el-table :data="getParametersList(selectedStrategyDetail.parameters)" border size="small">
                  <el-table-column prop="name" label="参数名" />
                  <el-table-column prop="value" label="当前值" />
                  <el-table-column prop="description" label="说明" />
                </el-table>
              </div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="strategy-metrics-section">
              <h4>绩效指标</h4>
              <div class="metrics-grid">
                <div class="metric-card">
                  <div class="metric-title">年化收益率</div>
                  <div class="metric-value" :class="getReturnClass(selectedStrategyDetail.metrics.annualReturn)">
                    {{ formatPercent(selectedStrategyDetail.metrics.annualReturn) }}
                  </div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">夏普比率</div>
                  <div class="metric-value">{{ selectedStrategyDetail.metrics.sharpeRatio?.toFixed(2) }}</div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">最大回撤</div>
                  <div class="metric-value negative">{{ formatPercent(selectedStrategyDetail.metrics.maxDrawdown) }}</div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">胜率</div>
                  <div class="metric-value">{{ formatPercent(selectedStrategyDetail.metrics.winRate) }}</div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">盈亏比</div>
                  <div class="metric-value">{{ selectedStrategyDetail.metrics.profitLossRatio?.toFixed(2) }}</div>
                </div>
                <div class="metric-card">
                  <div class="metric-title">波动率</div>
                  <div class="metric-value">{{ formatPercent(selectedStrategyDetail.metrics.volatility) }}</div>
                </div>
              </div>
            </div>

            <div class="strategy-chart-section" style="margin-top: 20px;">
              <h4>收益曲线</h4>
              <div class="chart-container">
                <EquityCurveChart
                  v-if="selectedStrategyDetail.equityCurve"
                  :data="selectedStrategyDetail.equityCurve"
                  :statistics="selectedStrategyDetail.metrics"
                  title=""
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 上传策略对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传策略" width="600px">
      <el-upload
        drag
        :http-request="handleUpload"
        :before-upload="beforeUpload"
        accept=".py,.json"
        :show-file-list="false"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将策略文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 .py 和 .json 格式的策略文件，文件大小不超过 10MB
          </div>
        </template>
      </el-upload>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, Upload, Refresh, Star, MoreFilled, User, Clock, View, UploadFilled 
} from '@element-plus/icons-vue'
import EquityCurveChart from '@/components/charts/EquityCurveChart.vue'
import { formatPercent, formatDate } from '@/utils/format'

const emit = defineEmits<{
  strategySelected: [strategy: any]
  strategyImported: [strategy: any]
}>()

// 响应式数据
const searchKeyword = ref('')
const selectedCategory = ref('')
const selectedRisk = ref('')
const sortBy = ref('created_at')
const showFavorites = ref(false)
const showDetailsDialog = ref(false)
const showUploadDialog = ref(false)
const selectedStrategyDetail = ref(null)
const strategies = ref([])

// 模拟策略数据
const mockStrategies = [
  {
    id: '1',
    name: '双均线突破策略',
    description: '基于短期和长期移动平均线交叉的经典趋势跟踪策略，适合中长期投资',
    category: 'trend',
    risk: 'medium',
    author: '策略开发团队',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15',
    usage: 156,
    favorite: true,
    parameters: {
      short_period: 5,
      long_period: 20,
      stop_loss: 0.05
    },
    metrics: {
      annualReturn: 0.18,
      sharpeRatio: 1.42,
      maxDrawdown: -0.12,
      winRate: 0.62,
      profitLossRatio: 1.8,
      volatility: 0.15
    },
    equityCurve: generateMockEquityCurve()
  },
  {
    id: '2',
    name: 'RSI反转策略',
    description: '利用RSI指标识别超买超卖区域，执行反转交易的短期策略',
    category: 'reversal',
    risk: 'high',
    author: '量化研究员',
    createdAt: '2024-01-05',
    updatedAt: '2024-01-20',
    usage: 89,
    favorite: false,
    parameters: {
      rsi_period: 14,
      overbought: 70,
      oversold: 30
    },
    metrics: {
      annualReturn: 0.25,
      sharpeRatio: 1.65,
      maxDrawdown: -0.18,
      winRate: 0.58,
      profitLossRatio: 2.1,
      volatility: 0.22
    },
    equityCurve: generateMockEquityCurve()
  },
  {
    id: '3',
    name: 'MACD动量策略',
    description: 'MACD指标金叉死叉信号的动量追踪策略，适合趋势明显的市场',
    category: 'trend',
    risk: 'medium',
    author: '算法交易专家',
    createdAt: '2024-01-10',
    updatedAt: '2024-01-25',
    usage: 203,
    favorite: true,
    parameters: {
      fast_period: 12,
      slow_period: 26,
      signal_period: 9
    },
    metrics: {
      annualReturn: 0.14,
      sharpeRatio: 1.28,
      maxDrawdown: -0.09,
      winRate: 0.65,
      profitLossRatio: 1.5,
      volatility: 0.12
    },
    equityCurve: generateMockEquityCurve()
  },
  {
    id: '4',
    name: '布林带均值回归',
    description: '基于布林带通道的均值回归策略，在震荡市场中表现优秀',
    category: 'reversal',
    risk: 'low',
    author: '风控专员',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-30',
    usage: 67,
    favorite: false,
    parameters: {
      period: 20,
      std_dev: 2.0,
      position_size: 0.1
    },
    metrics: {
      annualReturn: 0.11,
      sharpeRatio: 1.85,
      maxDrawdown: -0.06,
      winRate: 0.72,
      profitLossRatio: 1.3,
      volatility: 0.08
    },
    equityCurve: generateMockEquityCurve()
  }
]

function generateMockEquityCurve() {
  const data = []
  let value = 100000
  const startDate = new Date('2023-01-01')
  
  for (let i = 0; i < 250; i++) {
    const date = new Date(startDate)
    date.setDate(date.getDate() + i)
    
    value *= (1 + (Math.random() - 0.45) * 0.02)
    data.push({
      date: date.toISOString().split('T')[0],
      value: value
    })
  }
  
  return data
}

// 计算属性
const filteredStrategies = computed(() => {
  let filtered = strategies.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(strategy => 
      strategy.name.toLowerCase().includes(keyword) ||
      strategy.description.toLowerCase().includes(keyword) ||
      strategy.author.toLowerCase().includes(keyword)
    )
  }

  // 分类过滤
  if (selectedCategory.value) {
    filtered = filtered.filter(strategy => strategy.category === selectedCategory.value)
  }

  // 风险等级过滤
  if (selectedRisk.value) {
    filtered = filtered.filter(strategy => strategy.risk === selectedRisk.value)
  }

  // 收藏过滤
  if (showFavorites.value) {
    filtered = filtered.filter(strategy => strategy.favorite)
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'return':
        return b.metrics.annualReturn - a.metrics.annualReturn
      case 'sharpe':
        return b.metrics.sharpeRatio - a.metrics.sharpeRatio
      case 'usage':
        return b.usage - a.usage
      default: // created_at
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    }
  })

  return filtered
})

// 方法
const selectStrategy = (strategy: any) => {
  emit('strategySelected', strategy)
}

const useStrategy = (strategy: any) => {
  emit('strategySelected', strategy)
  ElMessage.success(`开始使用策略: ${strategy.name}`)
}

const viewDetails = (strategy: any) => {
  selectedStrategyDetail.value = strategy
  showDetailsDialog.value = true
}

const closeDetailsDialog = () => {
  showDetailsDialog.value = false
  selectedStrategyDetail.value = null
}

const toggleFavorite = (strategy: any) => {
  strategy.favorite = !strategy.favorite
  ElMessage.success(strategy.favorite ? '已添加到收藏' : '已取消收藏')
}

const handleCommand = (command: string) => {
  const [action, id] = command.split('-')
  const strategy = strategies.value.find(s => s.id === id)
  
  if (!strategy) return

  switch (action) {
    case 'edit':
      ElMessage.info(`编辑策略: ${strategy.name}`)
      emit('strategySelected', strategy)
      break
    case 'clone':
      ElMessage.info(`克隆策略: ${strategy.name}`)
      break
    case 'export':
      exportStrategy(strategy)
      break
    case 'delete':
      deleteStrategy(strategy)
      break
  }
}

const exportStrategy = (strategy: any) => {
  const data = JSON.stringify(strategy, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${strategy.name}.json`
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('策略导出成功')
}

const deleteStrategy = async (strategy: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除策略 "${strategy.name}" 吗？`, '确认删除', {
      type: 'warning'
    })
    
    const index = strategies.value.findIndex(s => s.id === strategy.id)
    if (index > -1) {
      strategies.value.splice(index, 1)
      ElMessage.success('策略删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const refreshLibrary = () => {
  // 模拟刷新
  ElMessage.success('策略库已刷新')
}

const beforeUpload = (file: File) => {
  const isValidType = file.type === 'application/json' || file.name.endsWith('.py')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只支持 .py 和 .json 格式的文件')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB')
    return false
  }
  return true
}

const handleUpload = async (options: any) => {
  const file = options.file
  
  try {
    // 模拟上传策略
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        let strategyData
        if (file.name.endsWith('.json')) {
          strategyData = JSON.parse(e.target?.result as string)
        } else {
          // 对于 .py 文件，创建基本的策略信息
          strategyData = {
            id: Date.now().toString(),
            name: file.name.replace('.py', ''),
            description: '从文件导入的策略',
            category: 'custom',
            risk: 'medium',
            author: '用户上传',
            createdAt: new Date().toISOString().split('T')[0],
            updatedAt: new Date().toISOString().split('T')[0],
            usage: 0,
            favorite: false,
            parameters: {},
            metrics: {
              annualReturn: 0,
              sharpeRatio: 0,
              maxDrawdown: 0,
              winRate: 0,
              profitLossRatio: 0,
              volatility: 0
            },
            equityCurve: []
          }
        }
        
        strategies.value.unshift(strategyData)
        showUploadDialog.value = false
        ElMessage.success('策略上传成功')
        emit('strategyImported', strategyData)
      } catch (error) {
        ElMessage.error('文件格式错误')
      }
    }
    reader.readAsText(file)
  } catch (error) {
    ElMessage.error('上传失败')
  }
}

const getParametersList = (parameters: any) => {
  return Object.entries(parameters).map(([key, value]) => ({
    name: key,
    value: value,
    description: getParameterDescription(key)
  }))
}

const getParameterDescription = (paramName: string) => {
  const descriptions = {
    short_period: '短期移动平均线周期',
    long_period: '长期移动平均线周期',
    rsi_period: 'RSI计算周期',
    overbought: 'RSI超买阈值',
    oversold: 'RSI超卖阈值',
    fast_period: 'MACD快线周期',
    slow_period: 'MACD慢线周期',
    signal_period: 'MACD信号线周期',
    period: '计算周期',
    std_dev: '标准差倍数',
    stop_loss: '止损比例',
    position_size: '仓位大小'
  }
  return descriptions[paramName] || '参数说明'
}

// 样式辅助函数
const getCategoryType = (category: string) => {
  const types = {
    trend: 'primary',
    reversal: 'success',
    arbitrage: 'warning',
    quant: 'info',
    custom: 'default'
  }
  return types[category] || 'default'
}

const getCategoryLabel = (category: string) => {
  const labels = {
    trend: '趋势策略',
    reversal: '反转策略',
    arbitrage: '套利策略',
    quant: '量化策略',
    custom: '自定义策略'
  }
  return labels[category] || category
}

const getRiskType = (risk: string) => {
  const types = {
    low: 'success',
    medium: 'warning',
    high: 'danger'
  }
  return types[risk] || 'default'
}

const getRiskLabel = (risk: string) => {
  const labels = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return labels[risk] || risk
}

const getReturnClass = (value: number) => {
  return value >= 0 ? 'positive' : 'negative'
}

onMounted(() => {
  strategies.value = mockStrategies
})
</script>

<style scoped>
.strategy-library {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.library-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.library-filters {
  padding: 12px 20px;
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.strategy-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  padding: 0 4px;
  overflow-y: auto;
}

.strategy-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.strategy-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #409EFF;
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.strategy-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.strategy-meta {
  display: flex;
  gap: 8px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-content {
  margin-bottom: 16px;
}

.strategy-description {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.strategy-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.metric-label {
  font-size: 12px;
  color: #909399;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.metric-value.positive {
  color: #67c23a;
}

.metric-value.negative {
  color: #f56c6c;
}

.strategy-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.card-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.strategy-details {
  max-height: 70vh;
  overflow-y: auto;
}

.strategy-info-section h4,
.strategy-metrics-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.metric-card {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.metric-title {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.metric-card .metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.metric-card .metric-value.positive {
  color: #67c23a;
}

.metric-card .metric-value.negative {
  color: #f56c6c;
}

.chart-container {
  height: 200px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
}

@media (max-width: 1200px) {
  .strategy-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .library-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .strategy-grid {
    grid-template-columns: 1fr;
  }
  
  .strategy-metrics {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
}
</style>