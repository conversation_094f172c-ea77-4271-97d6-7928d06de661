/**
 * 量化投资平台前端深度测试脚本
 * 使用Puppeteer模拟真实用户操作，测试所有功能模块
 */

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class FrontendTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = {
      timestamp: new Date().toISOString(),
      baseUrl: 'http://localhost:5173',
      tests: [],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        warnings: 0
      },
      performance: {},
      errors: [],
      screenshots: []
    };
  }

  async init() {
    console.log('🚀 启动浏览器进行前端测试...');

    this.browser = await puppeteer.launch({
      headless: false, // 显示浏览器窗口以便观察
      defaultViewport: { width: 1920, height: 1080 },
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--allow-running-insecure-content'
      ]
    });

    this.page = await this.browser.newPage();

    // 监听控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();

      if (type === 'error') {
        this.testResults.errors.push({
          type: 'console_error',
          message: text,
          timestamp: new Date().toISOString()
        });
      }

      console.log(`[${type.toUpperCase()}] ${text}`);
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      this.testResults.errors.push({
        type: 'page_error',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });
      console.error('❌ 页面错误:', error.message);
    });

    // 监听网络请求失败
    this.page.on('requestfailed', request => {
      this.testResults.errors.push({
        type: 'network_error',
        url: request.url(),
        failure: request.failure().errorText,
        timestamp: new Date().toISOString()
      });
      console.warn('⚠️ 网络请求失败:', request.url(), request.failure().errorText);
    });
  }

  async takeScreenshot(name) {
    const filename = `screenshot-${name}-${Date.now()}.png`;
    const filepath = path.join(__dirname, 'test-screenshots', filename);

    // 确保目录存在
    const dir = path.dirname(filepath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    await this.page.screenshot({ path: filepath, fullPage: true });
    this.testResults.screenshots.push({ name, filepath, timestamp: new Date().toISOString() });
    console.log(`📸 截图保存: ${filename}`);
  }

  async addTestResult(testName, status, details = {}) {
    const result = {
      name: testName,
      status, // 'passed', 'failed', 'warning'
      timestamp: new Date().toISOString(),
      ...details
    };

    this.testResults.tests.push(result);
    this.testResults.summary.total++;
    this.testResults.summary[status]++;

    const statusIcon = status === 'passed' ? '✅' : status === 'failed' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${testName}: ${status}`);

    if (details.message) {
      console.log(`   ${details.message}`);
    }
  }

  async testPageLoad(url, pageName) {
    console.log(`\n🔍 测试页面: ${pageName} (${url})`);

    try {
      const startTime = Date.now();

      // 导航到页面
      const response = await this.page.goto(url, {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      const loadTime = Date.now() - startTime;

      // 检查HTTP状态
      if (!response.ok()) {
        await this.addTestResult(`${pageName} - 页面加载`, 'failed', {
          message: `HTTP状态码: ${response.status()}`,
          loadTime
        });
        return false;
      }

      // 等待页面渲染
      await this.page.waitForTimeout(2000);

      // 检查页面标题
      const title = await this.page.title();

      // 检查是否有Vue应用
      const hasVueApp = await this.page.evaluate(() => {
        return document.querySelector('#app') !== null;
      });

      if (!hasVueApp) {
        await this.addTestResult(`${pageName} - Vue应用`, 'failed', {
          message: '未找到#app元素',
          loadTime
        });
        return false;
      }

      // 检查页面内容是否加载
      const hasContent = await this.page.evaluate(() => {
        const app = document.querySelector('#app');
        return app && app.children.length > 0;
      });

      if (!hasContent) {
        await this.addTestResult(`${pageName} - 页面内容`, 'failed', {
          message: '页面内容为空',
          loadTime
        });
        return false;
      }

      await this.addTestResult(`${pageName} - 页面加载`, 'passed', {
        message: `加载时间: ${loadTime}ms, 标题: ${title}`,
        loadTime,
        title
      });

      // 截图
      await this.takeScreenshot(pageName.toLowerCase().replace(/\s+/g, '-'));

      return true;

    } catch (error) {
      await this.addTestResult(`${pageName} - 页面加载`, 'failed', {
        message: `加载失败: ${error.message}`,
        error: error.stack
      });
      return false;
    }
  }

  async testNavigation() {
    console.log('\n🧭 测试导航功能...');

    try {
      // 等待导航栏加载
      await this.page.waitForSelector('.navbar-nav', { timeout: 10000 });

      // 获取所有导航链接
      const navLinks = await this.page.$$eval('.navbar-nav .nav-item', links =>
        links.map(link => ({
          text: link.textContent.trim(),
          href: link.getAttribute('href')
        }))
      );

      console.log(`找到 ${navLinks.length} 个导航链接:`, navLinks.map(l => l.text));

      // 测试每个导航链接
      for (const link of navLinks) {
        if (link.href && link.href !== '#') {
          const fullUrl = new URL(link.href, this.testResults.baseUrl).href;
          await this.testPageLoad(fullUrl, link.text);

          // 测试页面特定功能
          await this.testPageSpecificFeatures(link.href, link.text);

          // 短暂等待
          await this.page.waitForTimeout(1000);
        }
      }

      await this.addTestResult('导航功能', 'passed', {
        message: `成功测试 ${navLinks.length} 个导航链接`
      });

    } catch (error) {
      await this.addTestResult('导航功能', 'failed', {
        message: `导航测试失败: ${error.message}`,
        error: error.stack
      });
    }
  }

  async testPageSpecificFeatures(path, pageName) {
    console.log(`\n🔧 测试 ${pageName} 页面特定功能...`);

    try {
      switch (path) {
        case '/dashboard':
          await this.testDashboardFeatures();
          break;
        case '/market':
          await this.testMarketFeatures();
          break;
        case '/trading':
          await this.testTradingFeatures();
          break;
        case '/strategy':
          await this.testStrategyFeatures();
          break;
        case '/backtest':
          await this.testBacktestFeatures();
          break;
        case '/portfolio':
          await this.testPortfolioFeatures();
          break;
        case '/risk':
          await this.testRiskFeatures();
          break;
        default:
          console.log(`   跳过 ${pageName} 的特定功能测试`);
      }
    } catch (error) {
      await this.addTestResult(`${pageName} - 特定功能`, 'failed', {
        message: `功能测试失败: ${error.message}`,
        error: error.stack
      });
    }
  }

  async testDashboardFeatures() {
    // 测试仪表盘指标卡片
    const metricCards = await this.page.$$('.metric-card');
    if (metricCards.length > 0) {
      await this.addTestResult('仪表盘 - 指标卡片', 'passed', {
        message: `找到 ${metricCards.length} 个指标卡片`
      });
    } else {
      await this.addTestResult('仪表盘 - 指标卡片', 'warning', {
        message: '未找到指标卡片'
      });
    }

    // 测试功能模块链接
    const moduleCards = await this.page.$$('.module-card');
    if (moduleCards.length > 0) {
      await this.addTestResult('仪表盘 - 功能模块', 'passed', {
        message: `找到 ${moduleCards.length} 个功能模块`
      });
    } else {
      await this.addTestResult('仪表盘 - 功能模块', 'warning', {
        message: '未找到功能模块'
      });
    }
  }

  async testMarketFeatures() {
    // 测试搜索功能
    const searchInput = await this.page.$('#stock-search-input');
    if (searchInput) {
      await searchInput.type('000001');
      await this.page.waitForTimeout(1000);
      await this.addTestResult('市场 - 搜索功能', 'passed', {
        message: '搜索输入框正常工作'
      });
    } else {
      await this.addTestResult('市场 - 搜索功能', 'failed', {
        message: '未找到搜索输入框'
      });
    }

    // 测试市场筛选按钮
    const marketButtons = await this.page.$$('.market-buttons .el-button');
    if (marketButtons.length > 0) {
      await this.addTestResult('市场 - 筛选按钮', 'passed', {
        message: `找到 ${marketButtons.length} 个市场筛选按钮`
      });
    } else {
      await this.addTestResult('市场 - 筛选按钮', 'warning', {
        message: '未找到市场筛选按钮'
      });
    }

    // 测试图表容器
    const chartContainer = await this.page.$('.market-chart');
    if (chartContainer) {
      await this.addTestResult('市场 - 图表容器', 'passed', {
        message: '找到图表容器'
      });
    } else {
      await this.addTestResult('市场 - 图表容器', 'warning', {
        message: '未找到图表容器'
      });
    }
  }

  async testTradingFeatures() {
    // 检查交易功能是否为占位符
    const tradingContent = await this.page.$eval('body', el => el.textContent);
    if (tradingContent.includes('正在开发中') || tradingContent.includes('开发中')) {
      await this.addTestResult('交易 - 功能状态', 'warning', {
        message: '交易功能仍在开发中'
      });
    } else {
      await this.addTestResult('交易 - 功能状态', 'passed', {
        message: '交易功能已实现'
      });
    }
  }

  async testBacktestFeatures() {
    // 测试新建回测按钮
    const createButton = await this.page.$('button:has-text("新建回测")');
    if (createButton) {
      await this.addTestResult('回测 - 新建按钮', 'passed', {
        message: '找到新建回测按钮'
      });
    } else {
      await this.addTestResult('回测 - 新建按钮', 'warning', {
        message: '未找到新建回测按钮'
      });
    }

    // 测试回测列表表格
    const backtestTable = await this.page.$('.el-table');
    if (backtestTable) {
      await this.addTestResult('回测 - 数据表格', 'passed', {
        message: '找到回测数据表格'
      });
    } else {
      await this.addTestResult('回测 - 数据表格', 'warning', {
        message: '未找到回测数据表格'
      });
    }
  }

  async testStrategyFeatures() {
    // 基础功能检查
    const pageContent = await this.page.$eval('body', el => el.textContent);
    if (pageContent.includes('策略')) {
      await this.addTestResult('策略 - 页面内容', 'passed', {
        message: '策略页面内容正常'
      });
    } else {
      await this.addTestResult('策略 - 页面内容', 'warning', {
        message: '策略页面内容异常'
      });
    }
  }

  async testPortfolioFeatures() {
    // 基础功能检查
    const pageContent = await this.page.$eval('body', el => el.textContent);
    if (pageContent.includes('投资组合') || pageContent.includes('组合')) {
      await this.addTestResult('投资组合 - 页面内容', 'passed', {
        message: '投资组合页面内容正常'
      });
    } else {
      await this.addTestResult('投资组合 - 页面内容', 'warning', {
        message: '投资组合页面内容异常'
      });
    }
  }

  async testRiskFeatures() {
    // 基础功能检查
    const pageContent = await this.page.$eval('body', el => el.textContent);
    if (pageContent.includes('风险')) {
      await this.addTestResult('风险管理 - 页面内容', 'passed', {
        message: '风险管理页面内容正常'
      });
    } else {
      await this.addTestResult('风险管理 - 页面内容', 'warning', {
        message: '风险管理页面内容异常'
      });
    }
  }

  async testPerformance() {
    console.log('\n⚡ 测试性能指标...');

    try {
      // 获取性能指标
      const metrics = await this.page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime || 0,
          firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
        };
      });

      this.testResults.performance = metrics;

      // 评估性能
      if (metrics.domContentLoaded < 3000) {
        await this.addTestResult('性能 - DOM加载', 'passed', {
          message: `DOM加载时间: ${metrics.domContentLoaded.toFixed(2)}ms`
        });
      } else {
        await this.addTestResult('性能 - DOM加载', 'warning', {
          message: `DOM加载时间较慢: ${metrics.domContentLoaded.toFixed(2)}ms`
        });
      }

      if (metrics.firstContentfulPaint < 2000) {
        await this.addTestResult('性能 - 首次内容绘制', 'passed', {
          message: `FCP: ${metrics.firstContentfulPaint.toFixed(2)}ms`
        });
      } else {
        await this.addTestResult('性能 - 首次内容绘制', 'warning', {
          message: `FCP较慢: ${metrics.firstContentfulPaint.toFixed(2)}ms`
        });
      }

    } catch (error) {
      await this.addTestResult('性能测试', 'failed', {
        message: `性能测试失败: ${error.message}`,
        error: error.stack
      });
    }
  }

  async runAllTests() {
    try {
      await this.init();

      console.log('🎯 开始全面测试量化投资平台...\n');

      // 1. 测试首页加载
      await this.testPageLoad(this.testResults.baseUrl, '首页');

      // 2. 测试导航和所有页面
      await this.testNavigation();

      // 3. 测试性能
      await this.testPerformance();

      // 4. 生成测试报告
      await this.generateReport();

      console.log('\n🎉 测试完成！');

    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
      this.testResults.errors.push({
        type: 'test_runner_error',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  async generateReport() {
    const reportPath = path.join(__dirname, `test-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(this.testResults, null, 2));

    console.log('\n📊 测试报告摘要:');
    console.log(`   总测试数: ${this.testResults.summary.total}`);
    console.log(`   ✅ 通过: ${this.testResults.summary.passed}`);
    console.log(`   ❌ 失败: ${this.testResults.summary.failed}`);
    console.log(`   ⚠️  警告: ${this.testResults.summary.warnings}`);
    console.log(`   🐛 错误数: ${this.testResults.errors.length}`);
    console.log(`   📸 截图数: ${this.testResults.screenshots.length}`);
    console.log(`\n📄 详细报告: ${reportPath}`);

    return this.testResults;
  }
}

// 运行测试
const tester = new FrontendTester();
tester.runAllTests().catch(console.error);
