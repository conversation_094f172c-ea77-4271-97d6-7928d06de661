{"timestamp": "2025-08-07T12:18:24.961Z", "totalTests": 12, "passedTests": 12, "failedTests": 0, "errors": [{"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:18:26.414Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:18:26.415Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:18:27.698Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:18:27.698Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:18:31.611Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:18:31.612Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:18:35.491Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:18:35.492Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:18:39.375Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:18:39.376Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:18:43.221Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:18:43.221Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:18:47.090Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:18:47.091Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:18:51.030Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:18:51.031Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:18:54.901Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:18:54.902Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:18:55.496Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:18:55.496Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:18:58.359Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:18:58.360Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:18:59.396Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:18:59.396Z"}, {"type": "console_error", "message": "🚨 页面错误: JSHandle@error", "timestamp": "2025-08-07T12:19:03.155Z"}, {"type": "page_error", "message": "Cannot read properties of undefined (reading 'on')", "stack": "TypeError: Cannot read properties of undefined (reading 'on')\n    at <anonymous> (http://localhost:5174/src/App.vue:25:17)", "timestamp": "2025-08-07T12:19:03.156Z"}], "warnings": [], "pages": {"homepage": {"status": "success", "loadTime": 1588, "title": "量化投资平台", "hasContent": false, "screenshot": "homepage-1754569107441.png"}, "navigation": {"status": "success", "totalLinks": 0, "links": []}, "/dashboard": {"status": "success", "loadTime": 810, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/trading": {"status": "success", "loadTime": 900, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/strategy": {"status": "success", "loadTime": 855, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/backtest": {"status": "success", "loadTime": 847, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/market": {"status": "success", "loadTime": 812, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/portfolio": {"status": "success", "loadTime": 840, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/risk": {"status": "success", "loadTime": 889, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "/settings": {"status": "success", "loadTime": 1435, "title": "量化投资平台", "hasContent": false, "contentLength": 0}, "interactive": {"status": "success", "totalButtons": 0, "clickableButtons": 0, "totalInputs": 0, "workingInputs": 0}}, "performance": {"homepage": 1588, "domContentLoaded": 0.20000000018626451, "loadComplete": 0.3000000002793968, "firstPaint": 556, "firstContentfulPaint": 0, "resourceCount": 40}, "summary": {"successRate": "100.00%", "totalErrors": 26, "totalWarnings": 0, "testDuration": "2025-08-07T12:19:03.699Z"}}