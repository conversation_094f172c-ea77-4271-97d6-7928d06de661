{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "skipLibCheck": true, "noImplicitAny": false, "strictPropertyInitialization": false, "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "allowUnreachableCode": true, "allowUnusedLabels": true}}