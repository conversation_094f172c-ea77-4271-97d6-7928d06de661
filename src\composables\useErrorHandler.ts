/**
 * 全局错误处理组合函数
 */
import { ref } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'

export interface ErrorDetails {
  message: string
  code?: string | number
  stack?: string
  context?: Record<string, any>
  timestamp: string
  url: string
  userAgent: string
}

export const useErrorHandler = () => {
  const errors = ref<ErrorDetails[]>([])
  const isReporting = ref(false)

  // 处理API错误
  const handleApiError = (error: any, context?: Record<string, any>) => {
    const errorDetails: ErrorDetails = {
      message: error.message || '请求失败',
      code: error.response?.status || error.code,
      stack: error.stack,
      context: {
        url: error.config?.url,
        method: error.config?.method,
        ...context
      },
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    }

    errors.value.push(errorDetails)

    // 根据错误类型显示不同的提示
    if (error.response?.status === 401) {
      ElMessage.warning('登录已过期，请重新登录')
    } else if (error.response?.status === 403) {
      ElMessage.error('没有权限访问该资源')
    } else if (error.response?.status === 404) {
      ElMessage.error('请求的资源不存在')
    } else if (error.response?.status >= 500) {
      ElMessage.error('服务器异常，请稍后重试')
    } else if (error.code === 'NETWORK_ERROR') {
      ElMessage.error('网络连接异常，请检查网络设置')
    } else {
      ElMessage.error(errorDetails.message)
    }

    // 开发环境下打印详细错误信息
    if (import.meta.env.DEV) {
      console.error('API错误:', errorDetails)
    }

    return errorDetails
  }

  // 处理业务逻辑错误
  const handleBusinessError = (message: string, context?: Record<string, any>) => {
    const errorDetails: ErrorDetails = {
      message,
      context,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    }

    errors.value.push(errorDetails)
    ElMessage.warning(message)

    return errorDetails
  }

  // 处理系统级错误
  const handleSystemError = (error: Error, context?: Record<string, any>) => {
    const errorDetails: ErrorDetails = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    }

    errors.value.push(errorDetails)
    
    ElNotification.error({
      title: '系统异常',
      message: '程序发生异常，已自动记录错误信息',
      duration: 5000
    })

    // 开发环境下打印详细错误信息
    if (import.meta.env.DEV) {
      console.error('系统错误:', errorDetails)
    }

    return errorDetails
  }

  // 上报错误到服务器
  const reportError = async (errorDetails: ErrorDetails) => {
    if (isReporting.value) return

    try {
      isReporting.value = true
      
      // 这里应该调用实际的错误上报API
      // await api.post('/errors/report', errorDetails)
      
      console.log('错误已上报:', errorDetails)
    } catch (reportError) {
      console.error('错误上报失败:', reportError)
    } finally {
      isReporting.value = false
    }
  }

  // 清除错误记录
  const clearErrors = () => {
    errors.value = []
  }

  // 重试机制
  const createRetryHandler = (
    fn: Function, 
    maxRetries: number = 3, 
    delay: number = 1000
  ) => {
    return async (...args: any[]) => {
      let lastError: any
      
      for (let i = 0; i <= maxRetries; i++) {
        try {
          return await fn(...args)
        } catch (error) {
          lastError = error
          
          if (i === maxRetries) {
            handleApiError(error, { attempt: i + 1, maxRetries })
            throw error
          }
          
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
        }
      }
    }
  }

  return {
    errors,
    isReporting,
    handleApiError,
    handleBusinessError,
    handleSystemError,
    reportError,
    clearErrors,
    createRetryHandler
  }
}

// 全局错误处理器单例
let globalErrorHandler: ReturnType<typeof useErrorHandler> | null = null

export const getGlobalErrorHandler = () => {
  if (!globalErrorHandler) {
    globalErrorHandler = useErrorHandler()
  }
  return globalErrorHandler
}