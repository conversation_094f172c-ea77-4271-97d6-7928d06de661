<template>
  <div class="dashboard-view">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <h1 class="page-title">📊 投资仪表盘</h1>
        <div class="header-actions">
          <el-button-group>
            <el-button
              :type="timeRange === 'today' ? 'primary' : 'default'"
              size="small"
              @click="setTimeRange('today')"
            >
              今日
            </el-button>
            <el-button
              :type="timeRange === 'week' ? 'primary' : 'default'"
              size="small"
              @click="setTimeRange('week')"
            >
              本周
            </el-button>
            <el-button
              :type="timeRange === 'month' ? 'primary' : 'default'"
              size="small"
              @click="setTimeRange('month')"
            >
              本月
            </el-button>
          </el-button-group>

          <el-button
            type="primary"
            @click="refreshData"
            :loading="refreshing"
          >
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-grid">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="enhanced-metric-card primary-card" @click="viewPortfolioDetail">
            <div class="metric-header">
              <div class="metric-icon primary-icon">📈</div>
              <div class="metric-title">总资产</div>
            </div>
            <div class="metric-content">
              <div class="metric-value">¥{{ formatCurrency(accountMetrics.totalAssets || 0) }}</div>
              <div class="metric-change" :class="getChangeClass(accountMetrics.totalProfitPercent || 0)">
                {{ formatPercent(accountMetrics.totalProfitPercent || 0) }}
              </div>
            </div>
            <div class="metric-chart">
              <div class="mini-trend-line primary-trend"></div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="enhanced-metric-card success-card" @click="viewDailyDetail">
            <div class="metric-header">
              <div class="metric-icon success-icon">💰</div>
              <div class="metric-title">今日盈亏</div>
            </div>
            <div class="metric-content">
              <div class="metric-value text-success">
                {{ formatCurrency(accountMetrics.dailyProfit || 0, true) }}
              </div>
              <div class="metric-change text-success">
                {{ formatPercent(accountMetrics.dailyProfitPercent || 0) }}
              </div>
            </div>
            <div class="metric-chart">
              <div class="mini-trend-line success-trend"></div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="enhanced-metric-card success-card" @click="viewProfitDetail">
            <div class="metric-header">
              <div class="metric-icon success-icon">📊</div>
              <div class="metric-title">总盈亏</div>
            </div>
            <div class="metric-content">
              <div class="metric-value text-success">
                {{ formatCurrency(accountMetrics.totalProfit || 0, true) }}
              </div>
              <div class="metric-change text-success">
                {{ formatPercent(accountMetrics.totalProfitPercent || 0) }}
              </div>
            </div>
            <div class="metric-chart">
              <div class="mini-trend-line success-trend"></div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="enhanced-metric-card info-card" @click="viewPositionDetail">
            <div class="metric-header">
              <div class="metric-icon info-icon">🎯</div>
              <div class="metric-title">持仓股票</div>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ accountMetrics.positionCount }}</div>
              <div class="metric-subtitle">活跃策略: {{ accountMetrics.activeStrategies }}</div>
            </div>
            <div class="metric-progress">
              <el-progress 
                :percentage="(accountMetrics.positionCount / 20) * 100" 
                :show-text="false" 
                :stroke-width="4"
                color="#409eff"
              />
              <span class="progress-label">仓位使用率</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card class="dashboard-panel enhanced-chart-panel">
            <template #header>
              <div class="panel-header">
                <h3>📊 投资组合趋势</h3>
                <div class="panel-actions">
                  <el-radio-group v-model="chartTimeframe" size="small">
                    <el-radio-button value="1D">1日</el-radio-button>
                    <el-radio-button value="1W">1周</el-radio-button>
                    <el-radio-button value="1M">1月</el-radio-button>
                    <el-radio-button value="3M">3月</el-radio-button>
                    <el-radio-button value="1Y">1年</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
            </template>

            <div class="enhanced-chart-container">
              <div class="chart-stats">
                <div class="stat-item">
                  <span class="stat-label">当前净值</span>
                  <span class="stat-value primary">1.2456</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">今日涨跌</span>
                  <span class="stat-value success">+2.34%</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">累计收益</span>
                  <span class="stat-value success">+24.56%</span>
                </div>
              </div>
              <div class="chart-container" style="height: 300px; width: 100%;">
                <div style="text-align: center; padding: 100px 0; color: #999;">
                  📈 图表组件加载中...
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="dashboard-panel">
            <template #header>
              <h3>📰 市场资讯</h3>
            </template>
            <div class="news-list">
              <div class="news-item" v-for="(news, index) in mockNews" :key="index">
                <div class="news-content">
                  <h4 class="news-title">{{ news.title }}</h4>
                  <p class="news-summary">{{ news.summary }}</p>
                  <span class="news-time">{{ news.time }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 状态
const refreshing = ref(false)
const timeRange = ref<'today' | 'week' | 'month'>('today')
const chartTimeframe = ref('1D')

// 模拟数据
const accountMetrics = ref({
  totalAssets: 1234567.89,
  dailyProfit: 12345.67,
  dailyProfitPercent: 2.34,
  totalProfit: 234567.89,
  totalProfitPercent: 23.45,
  positionCount: 8,
  activeStrategies: 3
})

const mockNews = ref([
  {
    title: '市场分析：科技股表现强劲',
    summary: '今日科技板块整体上涨2.5%，人工智能概念股领涨...',
    time: '2小时前'
  },
  {
    title: '央行政策解读',
    summary: '央行维持基准利率不变，市场流动性保持合理充裕...',
    time: '4小时前'
  },
  {
    title: '新能源汽车销量创新高',
    summary: '11月新能源汽车销量同比增长35%，产业链受益...',
    time: '6小时前'
  }
])

// 格式化函数
const formatCurrency = (value: number, showSign = false): string => {
  const formatted = new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(Math.abs(value))
  
  if (showSign && value !== 0) {
    return value > 0 ? `+${formatted}` : `-${formatted}`
  }
  return formatted
}

const formatPercent = (value: number): string => {
  if (value === 0) return '0.00%'
  const sign = value > 0 ? '+' : ''
  return `${sign}${value.toFixed(2)}%`
}

const getChangeClass = (value: number): string => {
  if (value > 0) return 'text-success'
  if (value < 0) return 'text-danger'
  return 'text-info'
}

// 方法
const setTimeRange = (range: 'today' | 'week' | 'month') => {
  timeRange.value = range
  ElMessage.success(`切换到${range === 'today' ? '今日' : range === 'week' ? '本周' : '本月'}视图`)
}

const refreshData = async () => {
  refreshing.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    refreshing.value = false
  }
}

const viewPortfolioDetail = () => {
  router.push('/portfolio')
}

const viewDailyDetail = () => {
  ElMessage.info('查看今日详情')
}

const viewProfitDetail = () => {
  ElMessage.info('查看盈亏详情')
}

const viewPositionDetail = () => {
  router.push('/trading/positions')
}
</script>

<style scoped>
/* 基础布局 */
.dashboard-view {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.dashboard-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.metrics-grid {
  margin-bottom: 24px;
}

.charts-section {
  margin-bottom: 24px;
}

/* 增强的指标卡片样式 */
.enhanced-metric-card {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.enhanced-metric-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.enhanced-metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
}

.enhanced-metric-card.primary-card::before {
  background: linear-gradient(90deg, #409eff, #5dade2);
}

.enhanced-metric-card.success-card::before {
  background: linear-gradient(90deg, #67c23a, #58d68d);
}

.enhanced-metric-card.info-card::before {
  background: linear-gradient(90deg, #909399, #85929e);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.metric-icon.primary-icon {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1d4ed8;
}

.metric-icon.success-icon {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #15803d;
}

.metric-icon.info-icon {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #475569;
}

.metric-title {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.metric-content {
  margin-bottom: 16px;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 4px;
}

.metric-change {
  font-size: 14px;
  font-weight: 600;
}

.metric-subtitle {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

.metric-chart {
  height: 40px;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  background: linear-gradient(90deg, rgba(64, 158, 255, 0.1) 0%, rgba(103, 194, 58, 0.1) 100%);
}

.mini-trend-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(64, 158, 255, 0.3) 70%);
  animation: trendAnimation 3s ease-in-out infinite;
}

.mini-trend-line.primary-trend {
  background: linear-gradient(45deg, transparent 30%, rgba(64, 158, 255, 0.4) 70%);
}

.mini-trend-line.success-trend {
  background: linear-gradient(45deg, transparent 30%, rgba(103, 194, 58, 0.4) 70%);
}

@keyframes trendAnimation {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

.metric-progress {
  margin-top: 12px;
}

.progress-label {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
  display: block;
}

/* 图表面板样式 */
.enhanced-chart-panel {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

.enhanced-chart-panel .panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.enhanced-chart-panel .panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.enhanced-chart-container {
  padding: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.chart-stats {
  display: flex;
  gap: 32px;
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
}

.stat-value.primary {
  color: #1d4ed8;
}

.stat-value.success {
  color: #15803d;
}

.chart-container {
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  position: relative;
  overflow: hidden;
}

/* 文本颜色类 */
.text-success {
  color: #15803d !important;
}

.text-danger {
  color: #dc2626 !important;
}

.text-info {
  color: #6b7280 !important;
}

/* 新闻列表样式 */
.news-list {
  max-height: 400px;
  overflow-y: auto;
}

.news-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.news-item:last-child {
  border-bottom: none;
}

.news-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.news-summary {
  font-size: 12px;
  color: #6b7280;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.news-time {
  font-size: 11px;
  color: #9ca3af;
}
</style>
