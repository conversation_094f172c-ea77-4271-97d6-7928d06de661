<template>
  <div class="strategy-create-wizard">
    <el-dialog
      v-model="visible"
      title="创建策略向导"
      width="800px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <el-steps :active="currentStep" align-center class="wizard-steps">
        <el-step title="基础信息" description="设置策略基本信息" />
        <el-step title="策略类型" description="选择策略类型和模板" />
        <el-step title="参数配置" description="配置策略参数" />
        <el-step title="风险设置" description="设置风险控制参数" />
        <el-step title="完成创建" description="确认并创建策略" />
      </el-steps>

      <div class="wizard-content">
        <!-- 步骤1: 基础信息 -->
        <div v-if="currentStep === 0" class="step-content">
          <h3 class="step-title">策略基础信息</h3>
          <el-form :model="strategyForm" :rules="basicRules" ref="basicFormRef" label-width="120px">
            <el-form-item label="策略名称" prop="name">
              <el-input
                id="strategy-name-input"
                v-model="strategyForm.name"
                placeholder="请输入策略名称"
                maxlength="50"
                show-word-limit
                aria-label="策略名称输入框"
              />
            </el-form-item>

            <el-form-item label="策略描述" prop="description">
              <el-input
                id="strategy-description-input"
                v-model="strategyForm.description"
                type="textarea"
                :rows="4"
                placeholder="请描述策略的核心思路和特点"
                maxlength="500"
                show-word-limit
                aria-label="策略描述输入框"
              />
            </el-form-item>

            <el-form-item label="策略标签" prop="tags">
              <el-select
                v-model="strategyForm.tags"
                multiple
                filterable
                allow-create
                placeholder="选择或输入策略标签"
                style="width: 100%"
              >
                <el-option
                  v-for="tag in predefinedTags"
                  :key="tag"
                  :label="tag"
                  :value="tag"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="风险等级" prop="riskLevel">
              <el-radio-group v-model="strategyForm.riskLevel">
                <el-radio value="low">低风险</el-radio>
                <el-radio value="medium">中风险</el-radio>
                <el-radio value="high">高风险</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>

        <!-- 步骤2: 策略类型 -->
        <div v-if="currentStep === 1" class="step-content">
          <h3 class="step-title">选择策略类型</h3>
          <div class="strategy-types">
            <div
              v-for="type in strategyTypes"
              :key="type.value"
              class="type-card"
              :class="{ active: strategyForm.type === type.value }"
              @click="strategyForm.type = type.value"
            >
              <div class="type-icon">
                <el-icon :size="32">
                  <component :is="type.icon" />
                </el-icon>
              </div>
              <h4 class="type-name">{{ type.label }}</h4>
              <p class="type-description">{{ type.description }}</p>
              <div class="type-features">
                <el-tag
                  v-for="feature in type.features"
                  :key="feature"
                  size="small"
                  effect="plain"
                >
                  {{ feature }}
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 策略模板 -->
          <div v-if="strategyForm.type" class="strategy-templates">
            <h4 class="template-title">选择策略模板</h4>
            <el-radio-group v-model="strategyForm.template" class="template-group">
              <el-radio
                v-for="template in getTemplatesByType(strategyForm.type)"
                :key="template.id"
                :value="template.id"
                class="template-radio"
              >
                <div class="template-info">
                  <span class="template-name">{{ template.name }}</span>
                  <span class="template-desc">{{ template.description }}</span>
                </div>
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <!-- 步骤3: 参数配置 -->
        <div v-if="currentStep === 2" class="step-content">
          <h3 class="step-title">策略参数配置</h3>
          <div class="parameter-config">
            <div
              v-for="param in strategyParameters"
              :key="param.name"
              class="param-item"
            >
              <label class="param-label">
                {{ param.label }}
                <el-tooltip v-if="param.description" :content="param.description">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </label>

              <!-- 数字输入 -->
              <el-input-number
                v-if="param.type === 'number'"
                v-model="strategyForm.parameters[param.name]"
                :min="param.min"
                :max="param.max"
                :step="param.step"
                :precision="param.precision"
                controls-position="right"
              />

              <!-- 文本输入 -->
              <el-input
                v-else-if="param.type === 'string'"
                v-model="strategyForm.parameters[param.name]"
                :placeholder="param.placeholder"
              />

              <!-- 选择器 -->
              <el-select
                v-else-if="param.type === 'select'"
                v-model="strategyForm.parameters[param.name]"
                :placeholder="param.placeholder"
              >
                <el-option
                  v-for="option in param.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <!-- 开关 -->
              <el-switch
                v-else-if="param.type === 'boolean'"
                v-model="strategyForm.parameters[param.name]"
              />
            </div>
          </div>
        </div>

        <!-- 步骤4: 风险设置 -->
        <div v-if="currentStep === 3" class="step-content">
          <h3 class="step-title">风险控制设置</h3>
          <el-form :model="strategyForm.config" label-width="150px">
            <el-form-item label="初始资金">
              <el-input-number
                v-model="strategyForm.config.initialCapital"
                :min="10000"
                :max="10000000"
                :step="10000"
                controls-position="right"
              />
              <span class="input-suffix">元</span>
            </el-form-item>

            <el-form-item label="最大持仓比例">
              <el-input-number
                v-model="strategyForm.config.maxPositionSize"
                :min="0.01"
                :max="1"
                :step="0.01"
                :precision="2"
                controls-position="right"
              />
              <span class="input-suffix">%</span>
            </el-form-item>

            <el-form-item label="止损比例">
              <el-input-number
                v-model="strategyForm.config.stopLoss"
                :min="0.01"
                :max="0.5"
                :step="0.01"
                :precision="2"
                controls-position="right"
              />
              <span class="input-suffix">%</span>
            </el-form-item>

            <el-form-item label="止盈比例">
              <el-input-number
                v-model="strategyForm.config.takeProfit"
                :min="0.01"
                :max="2"
                :step="0.01"
                :precision="2"
                controls-position="right"
              />
              <span class="input-suffix">%</span>
            </el-form-item>

            <el-form-item label="最大持股数量">
              <el-input-number
                v-model="strategyForm.config.maxHoldings"
                :min="1"
                :max="50"
                controls-position="right"
              />
              <span class="input-suffix">只</span>
            </el-form-item>

            <el-form-item label="调仓频率">
              <el-select v-model="strategyForm.config.rebalanceFrequency">
                <el-option label="每日" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="每月" value="monthly" />
                <el-option label="每季度" value="quarterly" />
              </el-select>
            </el-form-item>

            <el-form-item label="基准指数">
              <el-select v-model="strategyForm.config.benchmark">
                <el-option label="沪深300" value="CSI300" />
                <el-option label="中证500" value="CSI500" />
                <el-option label="上证指数" value="SSE" />
                <el-option label="深证成指" value="SZSE" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <!-- 步骤5: 完成创建 -->
        <div v-if="currentStep === 4" class="step-content">
          <h3 class="step-title">确认策略信息</h3>
          <div class="strategy-summary">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="策略名称">{{ strategyForm.name }}</el-descriptions-item>
              <el-descriptions-item label="策略类型">{{ getTypeLabel(strategyForm.type) }}</el-descriptions-item>
              <el-descriptions-item label="风险等级">{{ getRiskLabel(strategyForm.riskLevel) }}</el-descriptions-item>
              <el-descriptions-item label="初始资金">{{ formatCurrency(strategyForm.config.initialCapital) }}</el-descriptions-item>
              <el-descriptions-item label="策略描述" :span="2">{{ strategyForm.description }}</el-descriptions-item>
            </el-descriptions>

            <div class="create-actions">
              <el-checkbox v-model="autoBacktest">创建后自动运行回测</el-checkbox>
              <el-checkbox v-model="autoStart">创建后自动启动策略</el-checkbox>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="wizard-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
          <el-button
            v-if="currentStep < 4"
            type="primary"
            @click="nextStep"
            :loading="validating"
          >
            下一步
          </el-button>
          <el-button
            v-else
            type="primary"
            @click="createStrategy"
            :loading="creating"
          >
            创建策略
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  TrendCharts,
  DataAnalysis,
  Timer,
  Cpu,
  QuestionFilled
} from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'created', strategy: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态
const currentStep = ref(0)
const validating = ref(false)
const creating = ref(false)
const autoBacktest = ref(false)
const autoStart = ref(false)

// 表单引用
const basicFormRef = ref()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const strategyForm = reactive({
  name: '',
  description: '',
  tags: [],
  riskLevel: 'medium',
  type: '',
  template: '',
  parameters: {} as Record<string, any>,
  config: {
    initialCapital: 100000,
    maxPositionSize: 0.1,
    stopLoss: 0.05,
    takeProfit: 0.15,
    maxHoldings: 10,
    rebalanceFrequency: 'daily',
    benchmark: 'CSI300'
  }
})

// 验证规则
const basicRules = {
  name: [
    { required: true, message: '请输入策略名称', trigger: 'blur' },
    { min: 2, max: 50, message: '策略名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入策略描述', trigger: 'blur' },
    { min: 10, max: 500, message: '策略描述长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 预定义标签
const predefinedTags = [
  '趋势跟踪', '均值回归', '动量策略', '价值投资',
  '技术分析', '基本面分析', '量化选股', '择时策略',
  '多因子', '机器学习', '高频交易', '套利策略'
]

// 策略类型
const strategyTypes = [
  {
    value: 'trend_following',
    label: '趋势跟踪',
    description: '基于价格趋势的策略',
    icon: 'TrendCharts',
    features: ['趋势识别', '动量交易', '突破策略']
  },
  {
    value: 'mean_reversion',
    label: '均值回归',
    description: '基于价格回归的策略',
    icon: 'DataAnalysis',
    features: ['超买超卖', '价格回归', '震荡交易']
  },
  {
    value: 'arbitrage',
    label: '套利策略',
    description: '利用价差进行套利',
    icon: 'Timer',
    features: ['价差套利', '统计套利', '风险中性']
  },
  {
    value: 'machine_learning',
    label: '机器学习',
    description: '基于AI算法的策略',
    icon: 'Cpu',
    features: ['深度学习', '特征工程', '模式识别']
  }
]

// 策略模板
const strategyTemplates = {
  trend_following: [
    { id: 'ma_cross', name: '双均线策略', description: '基于移动平均线交叉的趋势策略' },
    { id: 'breakout', name: '突破策略', description: '基于价格突破的趋势策略' },
    { id: 'momentum', name: '动量策略', description: '基于价格动量的趋势策略' }
  ],
  mean_reversion: [
    { id: 'rsi_reversal', name: 'RSI反转策略', description: '基于RSI指标的反转策略' },
    { id: 'bollinger_bands', name: '布林带策略', description: '基于布林带的均值回归策略' },
    { id: 'pairs_trading', name: '配对交易', description: '基于股票对的统计套利策略' }
  ],
  arbitrage: [
    { id: 'statistical_arbitrage', name: '统计套利', description: '基于统计模型的套利策略' },
    { id: 'index_arbitrage', name: '指数套利', description: '基于指数与成分股的套利策略' }
  ],
  machine_learning: [
    { id: 'lstm_prediction', name: 'LSTM预测', description: '基于LSTM神经网络的价格预测策略' },
    { id: 'random_forest', name: '随机森林', description: '基于随机森林的多因子选股策略' }
  ]
}

// 策略参数（根据模板动态生成）
const strategyParameters = computed(() => {
  const template = strategyForm.template
  if (!template) return []

  // 这里应该根据不同的模板返回不同的参数配置
  // 简化示例
  return [
    {
      name: 'lookback_period',
      label: '回看周期',
      type: 'number',
      min: 1,
      max: 100,
      step: 1,
      description: '用于计算指标的历史数据周期'
    },
    {
      name: 'signal_threshold',
      label: '信号阈值',
      type: 'number',
      min: 0.01,
      max: 1,
      step: 0.01,
      precision: 2,
      description: '触发交易信号的阈值'
    }
  ]
})

// 方法
const getTemplatesByType = (type: string) => {
  return strategyTemplates[type as keyof typeof strategyTemplates] || []
}

const getTypeLabel = (type: string) => {
  const typeObj = strategyTypes.find(t => t.value === type)
  return typeObj ? typeObj.label : type
}

const getRiskLabel = (risk: string) => {
  const map: Record<string, string> = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return map[risk] || risk
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

const nextStep = async () => {
  // 验证当前步骤
  if (currentStep.value === 0) {
    if (!basicFormRef.value) return
    try {
      await basicFormRef.value.validate()
    } catch (error) {
      return
    }
  } else if (currentStep.value === 1) {
    if (!strategyForm.type) {
      ElMessage.warning('请选择策略类型')
      return
    }
    if (!strategyForm.template) {
      ElMessage.warning('请选择策略模板')
      return
    }
  }

  currentStep.value++
}

const prevStep = () => {
  currentStep.value--
}

const createStrategy = async () => {
  creating.value = true
  try {
    // 模拟创建策略
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('策略创建成功')
    emit('created', strategyForm)
    handleClose()
  } catch (error) {
    ElMessage.error('策略创建失败')
  } finally {
    creating.value = false
  }
}

const handleClose = () => {
  visible.value = false
  // 重置表单
  currentStep.value = 0
  Object.assign(strategyForm, {
    name: '',
    description: '',
    tags: [],
    riskLevel: 'medium',
    type: '',
    template: '',
    parameters: {},
    config: {
      initialCapital: 100000,
      maxPositionSize: 0.1,
      stopLoss: 0.05,
      takeProfit: 0.15,
      maxHoldings: 10,
      rebalanceFrequency: 'daily',
      benchmark: 'CSI300'
    }
  })
}

// 监听模板变化，初始化参数
watch(() => strategyForm.template, (newTemplate) => {
  if (newTemplate) {
    // 初始化参数默认值
    strategyForm.parameters = {
      lookback_period: 20,
      signal_threshold: 0.05
    }
  }
})
</script>

<style scoped>
.strategy-create-wizard {
  .wizard-steps {
    margin-bottom: 32px;
  }

  .wizard-content {
    min-height: 400px;
    padding: 0 24px;
  }

  .step-content {
    .step-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 24px;
      color: #333;
    }
  }

  .strategy-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .type-card {
      border: 2px solid #e6e6e6;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      }

      &.active {
        border-color: #409eff;
        background: #f0f8ff;
      }

      .type-icon {
        margin-bottom: 12px;
        color: #409eff;
      }

      .type-name {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 8px 0;
      }

      .type-description {
        color: #666;
        font-size: 14px;
        margin: 0 0 12px 0;
      }

      .type-features {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        justify-content: center;
      }
    }
  }

  .strategy-templates {
    .template-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
    }

    .template-group {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .template-radio {
        margin: 0;
        padding: 12px;
        border: 1px solid #e6e6e6;
        border-radius: 6px;

        .template-info {
          display: flex;
          flex-direction: column;
          margin-left: 8px;

          .template-name {
            font-weight: 600;
          }

          .template-desc {
            color: #666;
            font-size: 12px;
          }
        }
      }
    }
  }

  .parameter-config {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;

    .param-item {
      .param-label {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 8px;
        font-weight: 500;
      }
    }
  }

  .strategy-summary {
    .create-actions {
      margin-top: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 6px;

      .el-checkbox {
        display: block;
        margin-bottom: 8px;
      }
    }
  }

  .wizard-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .input-suffix {
    margin-left: 8px;
    color: #666;
    font-size: 14px;
  }
}
</style>
