<template>
  <div class="trading-terminal">
    <div class="terminal-header">
      <div class="header-left">
        <el-select v-model="selectedSymbol" placeholder="选择交易品种" @change="onSymbolChange">
          <el-option
            v-for="symbol in symbols"
            :key="symbol.code"
            :label="`${symbol.name} (${symbol.code})`"
            :value="symbol.code"
          />
        </el-select>
        <div class="connection-status" :class="{ connected: isWebSocketConnected }">
          <el-icon><Connection /></el-icon>
          <span>{{ isWebSocketConnected ? '已连接' : '未连接' }}</span>
        </div>
      </div>
      <div class="header-right">
        <el-button-group>
          <el-button :type="activeTab === 'buy' ? 'primary' : 'default'" @click="activeTab = 'buy'">
            买入
          </el-button>
          <el-button :type="activeTab === 'sell' ? 'primary' : 'default'" @click="activeTab = 'sell'">
            卖出
          </el-button>
        </el-button-group>
      </div>
    </div>

    <div class="terminal-content">
      <!-- 价格信息 -->
      <div class="price-info">
        <div class="price-item">
          <span class="label">最新价</span>
          <span class="value" :class="priceChangeClass">{{ currentPrice }}</span>
          <div class="price-change-indicator" :class="priceChangeClass">
            {{ priceChangeDirection }}
          </div>
        </div>
        <div class="price-item">
          <span class="label">涨跌幅</span>
          <span class="value" :class="priceChangeClass">{{ priceChange }}%</span>
        </div>
        <div class="price-item">
          <span class="label">买一价</span>
          <span class="value bid">{{ bidPrice }}</span>
          <small class="volume">{{ bidVolume }}</small>
        </div>
        <div class="price-item">
          <span class="label">卖一价</span>
          <span class="value ask">{{ askPrice }}</span>
          <small class="volume">{{ askVolume }}</small>
        </div>
        <div class="price-item">
          <span class="label">成交量</span>
          <span class="value">{{ formatVolume(currentVolume) }}</span>
        </div>
        <div class="price-item">
          <span class="label">更新时间</span>
          <span class="value time">{{ lastUpdateTime }}</span>
        </div>
      </div>

      <!-- 交易表单 -->
      <div class="trading-form">
        <el-form :model="orderForm" :rules="rules" ref="formRef" label-width="80px">
          <el-form-item label="订单类型" prop="orderType">
            <el-select v-model="orderForm.orderType" @change="onOrderTypeChange">
              <el-option label="限价单" value="limit" />
              <el-option label="市价单" value="market" />
              <el-option label="止损单" value="stop" />
              <el-option label="止盈单" value="takeProfit" />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="orderForm.orderType === 'limit'"
            label="价格"
            prop="price"
          >
            <div class="price-input-group">
              <el-input-number
                v-model="orderForm.price"
                :precision="2"
                :step="0.01"
                :min="0"
                style="width: 100%"
              />
              <div class="quick-price-buttons">
                <el-button size="small" @click="setQuickPrice('bid')">买一</el-button>
                <el-button size="small" @click="setQuickPrice('ask')">卖一</el-button>
                <el-button size="small" @click="setQuickPrice('last')">最新</el-button>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="数量" prop="quantity">
            <div class="quantity-input-group">
              <el-input-number
                id="order-quantity-input"
                v-model="orderForm.quantity"
                :precision="0"
                :step="100"
                :min="100"
                style="width: 100%"
                aria-label="交易数量输入框"
              />
              <div class="quick-quantity-buttons">
                <el-button size="small" @click="setQuickQuantity(25)">1/4</el-button>
                <el-button size="small" @click="setQuickQuantity(50)">1/2</el-button>
                <el-button size="small" @click="setQuickQuantity(100)">全部</el-button>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="有效期" prop="timeInForce">
            <el-select
              id="order-time-in-force-select"
              v-model="orderForm.timeInForce"
              aria-label="订单有效期选择器"
            >
              <el-option label="当日有效" value="DAY" />
              <el-option label="立即成交或撤销" value="IOC" />
              <el-option label="全部成交或撤销" value="FOK" />
              <el-option label="撤销前有效" value="GTC" />
            </el-select>
          </el-form-item>

          <!-- 止损止盈 -->
          <el-form-item>
            <div class="stop-profit-section">
              <el-checkbox v-model="orderForm.enableStopLoss">止损</el-checkbox>
              <el-input-number
                v-if="orderForm.enableStopLoss"
                v-model="orderForm.stopLossPrice"
                :precision="2"
                :step="0.01"
                :min="0"
                size="small"
                style="width: 120px; margin-left: 8px"
                placeholder="止损价"
              />
            </div>
          </el-form-item>

          <el-form-item>
            <div class="stop-profit-section">
              <el-checkbox v-model="orderForm.enableTakeProfit">止盈</el-checkbox>
              <el-input-number
                v-if="orderForm.enableTakeProfit"
                v-model="orderForm.takeProfitPrice"
                :precision="2"
                :step="0.01"
                :min="0"
                size="small"
                style="width: 120px; margin-left: 8px"
                placeholder="止盈价"
              />
            </div>
          </el-form-item>

          <!-- 预计费用 -->
          <el-form-item>
            <div class="order-summary">
              <div class="summary-item">
                <span>预计金额：</span>
                <span class="amount">{{ formatNumber(estimatedAmount) }}</span>
              </div>
              <div class="summary-item">
                <span>手续费：</span>
                <span class="fee">{{ formatNumber(estimatedFee) }}</span>
              </div>
              <div class="summary-item">
                <span>总计：</span>
                <span class="total">{{ formatNumber(estimatedTotal) }}</span>
              </div>
            </div>
          </el-form-item>

          <!-- 交易按钮 -->
          <el-form-item>
            <el-button
              :type="activeTab === 'buy' ? 'success' : 'danger'"
              :loading="submitting"
              @click="submitOrder"
              style="width: 100%"
              :disabled="!canSubmitOrder"
            >
              {{ getOrderButtonText() }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 账户信息 -->
      <div class="account-info">
        <div class="info-item">
          <span class="label">可用资金</span>
          <span class="value">{{ formatNumber(accountInfo.availableBalance) }}</span>
        </div>
        <div class="info-item">
          <span class="label">持仓市值</span>
          <span class="value">{{ formatNumber(accountInfo.positionValue) }}</span>
        </div>
        <div class="info-item">
          <span class="label">总资产</span>
          <span class="value">{{ formatNumber(accountInfo.totalAssets) }}</span>
        </div>
        <div class="info-item">
          <span class="label">今日盈亏</span>
          <span class="value" :class="accountInfo.todayPnl >= 0 ? 'profit' : 'loss'">
            {{ accountInfo.todayPnl >= 0 ? '+' : '' }}{{ formatNumber(accountInfo.todayPnl) }}
          </span>
        </div>
        <div class="info-item">
          <span class="label">可用手数</span>
          <span class="value">{{ availableLots }}</span>
        </div>
        <div class="info-item">
          <span class="label">风险度</span>
          <span class="value" :class="getRiskLevelClass()">{{ riskLevel }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Connection } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { useWebSocket } from '@/composables/useWebSocket'
import { tradingApi } from '@/api/trading'
import type { TickData } from '@/types/api'

interface Symbol {
  code: string
  name: string
  price: number
  change: number
  bidPrice: number
  askPrice: number
  bidVolume: number
  askVolume: number
  volume: number
  lastUpdateTime: string
}

interface OrderForm {
  orderType: string
  price: number
  quantity: number
  timeInForce: string
  enableStopLoss: boolean
  stopLossPrice: number
  enableTakeProfit: boolean
  takeProfitPrice: number
}

interface AccountInfo {
  availableBalance: number
  positionValue: number
  totalAssets: number
  todayPnl: number
  marginUsed: number
  marginAvailable: number
}

const activeTab = ref<'buy' | 'sell'>('buy')
const selectedSymbol = ref('')
const submitting = ref(false)
const formRef = ref<FormInstance>()
const priceChangeDirection = ref('')
const lastPrice = ref(0)

// WebSocket连接
const { isConnected: isWebSocketConnected, subscribeMarketTick, subscribeTradingAccount } = useWebSocket()

// 模拟数据
const symbols = ref<Symbol[]>([
  {
    code: 'IF2312',
    name: '沪深300主力',
    price: 3850.2,
    change: 1.25,
    bidPrice: 3850.0,
    askPrice: 3850.4,
    bidVolume: 50,
    askVolume: 30,
    volume: 125600,
    lastUpdateTime: new Date().toLocaleTimeString()
  },
  {
    code: 'IC2312',
    name: '中证500主力',
    price: 5420.8,
    change: -0.85,
    bidPrice: 5420.6,
    askPrice: 5421.0,
    bidVolume: 80,
    askVolume: 60,
    volume: 98500,
    lastUpdateTime: new Date().toLocaleTimeString()
  },
  {
    code: 'IH2312',
    name: '上证50主力',
    price: 2680.5,
    change: 0.65,
    bidPrice: 2680.3,
    askPrice: 2680.7,
    bidVolume: 40,
    askVolume: 25,
    volume: 67800,
    lastUpdateTime: new Date().toLocaleTimeString()
  }
])

const accountInfo = reactive<AccountInfo>({
  availableBalance: 500000,
  positionValue: 150000,
  totalAssets: 650000,
  todayPnl: 2500,
  marginUsed: 45000,
  marginAvailable: 455000
})

const orderForm = reactive<OrderForm>({
  orderType: 'limit',
  price: 0,
  quantity: 100,
  timeInForce: 'DAY',
  enableStopLoss: false,
  stopLossPrice: 0,
  enableTakeProfit: false,
  takeProfitPrice: 0
})

// 表单验证规则
const rules = {
  orderType: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
  price: [{ required: true, message: '请输入价格', trigger: 'blur' }],
  quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  timeInForce: [{ required: true, message: '请选择有效期', trigger: 'change' }]
}

// 计算属性
const currentSymbol = computed(() => {
  return symbols.value.find(s => s.code === selectedSymbol.value)
})

const currentPrice = computed(() => {
  return currentSymbol.value?.price.toFixed(2) || '0.00'
})

const currentVolume = computed(() => {
  return currentSymbol.value?.volume || 0
})

const priceChange = computed(() => {
  return currentSymbol.value?.change.toFixed(2) || '0.00'
})

const bidPrice = computed(() => {
  return currentSymbol.value?.bidPrice.toFixed(2) || '0.00'
})

const askPrice = computed(() => {
  return currentSymbol.value?.askPrice.toFixed(2) || '0.00'
})

const bidVolume = computed(() => {
  return currentSymbol.value?.bidVolume || 0
})

const askVolume = computed(() => {
  return currentSymbol.value?.askVolume || 0
})

const lastUpdateTime = computed(() => {
  return currentSymbol.value?.lastUpdateTime || '--:--:--'
})

const priceChangeClass = computed(() => {
  const change = currentSymbol.value?.change || 0
  return change > 0 ? 'profit' : change < 0 ? 'loss' : ''
})

// 预计费用计算
const estimatedAmount = computed(() => {
  if (orderForm.orderType === 'market') {
    const price = activeTab.value === 'buy' ?
      (currentSymbol.value?.askPrice || 0) :
      (currentSymbol.value?.bidPrice || 0)
    return price * orderForm.quantity
  }
  return orderForm.price * orderForm.quantity
})

const estimatedFee = computed(() => {
  return estimatedAmount.value * 0.0003 // 假设手续费率0.03%
})

const estimatedTotal = computed(() => {
  return estimatedAmount.value + estimatedFee.value
})

// 可用手数计算
const availableLots = computed(() => {
  if (!currentSymbol.value) return 0
  const price = activeTab.value === 'buy' ? currentSymbol.value.askPrice : currentSymbol.value.bidPrice
  return Math.floor(accountInfo.availableBalance / (price * 100))
})

// 风险度计算
const riskLevel = computed(() => {
  if (accountInfo.totalAssets === 0) return 0
  return ((accountInfo.marginUsed / accountInfo.totalAssets) * 100).toFixed(1)
})

// 是否可以提交订单
const canSubmitOrder = computed(() => {
  if (orderForm.orderType === 'limit' && orderForm.price <= 0) return false
  if (orderForm.quantity <= 0) return false
  if (estimatedTotal.value > accountInfo.availableBalance) return false
  return true
})

// 方法
const formatNumber = (value: number): string => {
  return value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

const formatVolume = (value: number): string => {
  if (value >= 10000) {
    return (value / 10000).toFixed(1) + '万'
  }
  return value.toLocaleString()
}

const getRiskLevelClass = () => {
  const risk = parseFloat(riskLevel.value)
  if (risk < 30) return 'safe'
  if (risk < 60) return 'warning'
  return 'danger'
}

const getOrderButtonText = () => {
  const action = activeTab.value === 'buy' ? '买入' : '卖出'
  if (!canSubmitOrder.value) {
    if (estimatedTotal.value > accountInfo.availableBalance) {
      return '资金不足'
    }
    return '请完善信息'
  }
  return `${action} ${selectedSymbol.value}`
}

// 快速设置价格
const setQuickPrice = (type: 'bid' | 'ask' | 'last') => {
  if (!currentSymbol.value) return

  switch (type) {
    case 'bid':
      orderForm.price = currentSymbol.value.bidPrice
      break
    case 'ask':
      orderForm.price = currentSymbol.value.askPrice
      break
    case 'last':
      orderForm.price = currentSymbol.value.price
      break
  }
}

// 快速设置数量
const setQuickQuantity = (percentage: number) => {
  if (!currentSymbol.value) return

  const maxLots = availableLots.value
  const targetLots = Math.floor(maxLots * percentage / 100)
  orderForm.quantity = Math.max(100, targetLots * 100)
}

// 品种变化
const onSymbolChange = () => {
  if (currentSymbol.value) {
    orderForm.price = currentSymbol.value.price

    // 订阅新品种的实时数据
    subscribeToSymbol(selectedSymbol.value)
  }
}

// 订单类型变化
const onOrderTypeChange = () => {
  if (orderForm.orderType === 'market') {
    orderForm.price = 0
  } else if (currentSymbol.value) {
    orderForm.price = currentSymbol.value.price
  }
}

// 订阅品种实时数据
const subscribeToSymbol = (symbol: string) => {
  subscribeMarketTick([symbol], (tickData: TickData) => {
    const symbolData = symbols.value.find(s => s.code === symbol)
    if (symbolData) {
      // 更新价格变化方向
      if (tickData.price > lastPrice.value) {
        priceChangeDirection.value = '↑'
      } else if (tickData.price < lastPrice.value) {
        priceChangeDirection.value = '↓'
      }
      lastPrice.value = tickData.price

      // 更新数据
      symbolData.price = tickData.price
      symbolData.bidPrice = tickData.bidPrice || symbolData.bidPrice
      symbolData.askPrice = tickData.askPrice || symbolData.askPrice
      symbolData.bidVolume = tickData.bidVolume || symbolData.bidVolume
      symbolData.askVolume = tickData.askVolume || symbolData.askVolume
      symbolData.volume = tickData.volume || symbolData.volume
      symbolData.lastUpdateTime = new Date().toLocaleTimeString()

      // 计算涨跌幅（这里简化处理）
      symbolData.change = ((tickData.price - 3800) / 3800) * 100 // 假设昨收价
    }
  })
}

// 提交订单
const submitOrder = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    const orderData = {
      symbol: selectedSymbol.value,
      side: activeTab.value,
      type: orderForm.orderType,
      quantity: orderForm.quantity,
      price: orderForm.orderType === 'market' ? undefined : orderForm.price,
      timeInForce: orderForm.timeInForce,
      stopLoss: orderForm.enableStopLoss ? orderForm.stopLossPrice : undefined,
      takeProfit: orderForm.enableTakeProfit ? orderForm.takeProfitPrice : undefined
    }

    await ElMessageBox.confirm(
      `确定要${activeTab.value === 'buy' ? '买入' : '卖出'}${selectedSymbol.value}吗？
      数量：${orderForm.quantity}
      ${orderForm.orderType === 'limit' ? `价格：${orderForm.price}` : '类型：市价单'}
      预计金额：${formatNumber(estimatedTotal.value)}`,
      '确认交易',
      {
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    submitting.value = true

    // 调用交易API
    const response = await tradingApi.submitOrder(orderData)

    if (response.success) {
      ElMessage.success('订单提交成功')

      // 重置表单
      formRef.value.resetFields()
      orderForm.enableStopLoss = false
      orderForm.enableTakeProfit = false
    } else {
      ElMessage.error(response.message || '订单提交失败')
    }

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('订单提交失败')
      console.error('Submit order error:', error)
    }
  } finally {
    submitting.value = false
  }
}

// 监听账户变化
subscribeTradingAccount((accountData) => {
  Object.assign(accountInfo, accountData)
})

// 初始化
onMounted(() => {
  selectedSymbol.value = symbols.value[0]?.code || ''
  onSymbolChange()
})

// 清理
onUnmounted(() => {
  // WebSocket订阅会自动清理
})

// 监听价格变化方向指示器
watch(priceChangeDirection, (newDirection) => {
  if (newDirection) {
    setTimeout(() => {
      priceChangeDirection.value = ''
    }, 1000)
  }
})
</script>

<style scoped>
.trading-terminal {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.terminal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left .el-select {
  width: 200px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
  padding: 4px 8px;
  border-radius: 4px;
  background: #f5f7fa;
}

.connection-status.connected {
  color: #67c23a;
  background: #f0f9ff;
}

.terminal-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.price-info {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 6px;
}

.price-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.price-item .label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.price-item .value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.price-item .value.profit {
  color: #f56c6c;
}

.price-item .value.loss {
  color: #67c23a;
}

.price-item .value.bid {
  color: #67c23a;
}

.price-item .value.ask {
  color: #f56c6c;
}

.price-item .value.time {
  font-size: 12px;
  font-weight: normal;
}

.price-item .volume {
  font-size: 10px;
  color: #909399;
  margin-top: 2px;
}

.price-change-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 12px;
  font-weight: bold;
  animation: flash 1s ease-out;
}

@keyframes flash {
  0% { opacity: 1; transform: scale(1.2); }
  100% { opacity: 0; transform: scale(1); }
}

.trading-form {
  flex: 1;
}

.price-input-group,
.quantity-input-group {
  width: 100%;
}

.quick-price-buttons,
.quick-quantity-buttons {
  display: flex;
  gap: 4px;
  margin-top: 8px;
}

.quick-price-buttons .el-button,
.quick-quantity-buttons .el-button {
  flex: 1;
  padding: 4px 8px;
  font-size: 12px;
}

.stop-profit-section {
  display: flex;
  align-items: center;
}

.order-summary {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 14px;
}

.summary-item:last-child {
  margin-bottom: 0;
  font-weight: 600;
  border-top: 1px solid #dee2e6;
  padding-top: 4px;
}

.summary-item .amount,
.summary-item .fee,
.summary-item .total {
  font-weight: 600;
}

.account-info {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 6px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item .label {
  font-size: 14px;
  color: #606266;
}

.info-item .value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.info-item .value.profit {
  color: #f56c6c;
}

.info-item .value.loss {
  color: #67c23a;
}

.info-item .value.safe {
  color: #67c23a;
}

.info-item .value.warning {
  color: #e6a23c;
}

.info-item .value.danger {
  color: #f56c6c;
}

@media (max-width: 768px) {
  .price-info {
    grid-template-columns: repeat(3, 1fr);
  }

  .account-info {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
