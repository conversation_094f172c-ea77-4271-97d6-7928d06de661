#!/usr/bin/env node
/**
 * 前端开发服务器启动脚本
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动前端开发服务器...');

// 启动Vite开发服务器
const viteProcess = spawn('npx', ['vite', '--host', '0.0.0.0', '--port', '5173'], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true
});

viteProcess.on('error', (error) => {
  console.error('❌ 启动失败:', error);
  process.exit(1);
});

viteProcess.on('close', (code) => {
  console.log(`前端服务器退出，代码: ${code}`);
  process.exit(code);
});

// 优雅退出
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭前端服务器...');
  viteProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭前端服务器...');
  viteProcess.kill('SIGTERM');
});
