# 前端集成指南

本文档介绍如何将量化交易平台的前端与后端API进行集成。

## 目录结构

```
frontend/integration/
├── api-client.ts          # API客户端封装
├── hooks/
│   └── useBacktest.ts    # 回测相关React Hook
├── components/
│   └── BacktestDashboard.tsx  # 回测仪表盘组件
├── types/
│   └── index.ts          # TypeScript类型定义
└── README.md            # 本文档
```

## 快速开始

### 1. 安装依赖

```bash
npm install react react-dom typescript
# 或者使用 yarn
yarn add react react-dom typescript
```

### 2. 配置API客户端

```typescript
import { apiClient } from './integration/api-client';

// 设置API基础URL
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// 如果需要认证，设置token
apiClient.setToken('your-auth-token');
```

### 3. 使用回测功能

```typescript
import React from 'react';
import { useBacktest } from './integration/hooks/useBacktest';
import BacktestDashboard from './integration/components/BacktestDashboard';

function App() {
  const { createBacktest, tasks, loading } = useBacktest();

  const handleCreateBacktest = async () => {
    const request = {
      strategy_config: {
        name: '动量策略测试',
        description: '测试动量策略',
        strategy_type: 'momentum',
        version: '1.0.0',
        parameters: {
          lookback_period: 20,
          momentum_threshold: 0.02
        },
        universe: ['000001.SZ', '000002.SZ'],
        max_positions: 10,
        position_size: 0.1,
        max_drawdown: 0.2,
        benchmark: '000300.SH'
      },
      backtest_config: {
        initial_capital: 1000000,
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        benchmark: '000300.SH',
        commission_rate: 0.0003,
        stamp_tax_rate: 0.001,
        transfer_fee_rate: 0.00002,
        min_commission: 5.0,
        slippage_type: 'percentage',
        slippage_value: 0.001,
        cash_interest_rate: 0.02
      },
      data_source: 'akshare'
    };

    const taskId = await createBacktest(request);
    console.log('Created backtest task:', taskId);
  };

  return (
    <div className="App">
      <BacktestDashboard 
        onCreateNew={handleCreateBacktest}
        onTaskSelect={(task) => console.log('Selected task:', task)}
      />
    </div>
  );
}

export default App;
```

## API客户端 (APIClient)

### 基础用法

```typescript
import { apiClient } from './integration/api-client';

// 创建回测
const response = await apiClient.createBacktest(backtestRequest);

// 获取任务状态
const status = await apiClient.getBacktestStatus(taskId);

// 获取任务列表
const tasks = await apiClient.listBacktests();

// 取消任务
const result = await apiClient.cancelBacktest(taskId);
```

### 错误处理

所有API方法都返回统一的响应格式：

```typescript
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    error: string;
    message: string;
    details?: any;
    timestamp: string;
  };
  timestamp: string;
  request_id?: string;
}

// 使用示例
const response = await apiClient.createBacktest(request);
if (response.success) {
  console.log('Success:', response.data);
} else {
  console.error('Error:', response.error?.message);
}
```

## WebSocket连接

### 基础用法

```typescript
import { apiClient } from './integration/api-client';

const ws = apiClient.createWebSocket('my-client-id');

// 连接WebSocket
await ws.connect();

// 订阅主题
ws.subscribe(['backtest_progress', 'market_data']);

// 监听消息
ws.onMessage('backtest_progress', (data) => {
  console.log('Backtest progress:', data);
});

ws.onMessage('market_data', (data) => {
  console.log('Market data update:', data);
});

// 发送消息
ws.send('ping', {});

// 断开连接
ws.disconnect();
```

### 可订阅的主题

- `backtest_progress`: 回测进度更新
- `backtest_status`: 回测状态变更
- `market_data`: 市场数据更新
- `server_status`: 服务器状态

## React Hook (useBacktest)

### 基础用法

```typescript
import { useBacktest } from './integration/hooks/useBacktest';

function BacktestComponent() {
  const {
    tasks,           // 任务列表
    loading,         // 加载状态
    error,           // 错误信息
    creating,        // 创建中状态
    selectedTask,    // 选中的任务
    createBacktest,  // 创建回测
    cancelBacktest,  // 取消回测
    deleteBacktest,  // 删除回测
    getResults,      // 获取结果
    getReport,       // 获取报告
    selectTask,      // 选择任务
    clearError,      // 清除错误
    refreshTasks,    // 刷新任务
    wsManager        // WebSocket管理器
  } = useBacktest({
    autoRefresh: true,        // 自动刷新
    refreshInterval: 5000,    // 刷新间隔(ms)
    enableWebSocket: true,    // 启用WebSocket
    clientId: 'dashboard-1'   // 客户端ID
  });

  // 使用hook中的方法...
}
```

### 配置选项

- `autoRefresh`: 是否自动刷新任务列表（默认：true）
- `refreshInterval`: 自动刷新间隔，毫秒（默认：5000）
- `enableWebSocket`: 是否启用WebSocket实时更新（默认：true）
- `clientId`: WebSocket客户端ID（默认：'default-client'）

## 组件 (BacktestDashboard)

### 基础用法

```typescript
import BacktestDashboard from './integration/components/BacktestDashboard';

function App() {
  return (
    <BacktestDashboard
      className="w-full h-full"
      onTaskSelect={(task) => {
        console.log('Task selected:', task);
        // 处理任务选择
      }}
      onCreateNew={() => {
        console.log('Create new backtest');
        // 显示创建回测对话框
      }}
    />
  );
}
```

### 属性

- `className`: CSS类名
- `onTaskSelect`: 任务选择回调
- `onCreateNew`: 创建新任务回调

## 类型定义

### 策略配置

```typescript
interface StrategyConfig {
  name: string;
  description: string;
  strategy_type: 'momentum' | 'mean_reversion' | 'pairs_trading' | 'arbitrage' | 'factor' | 'machine_learning';
  version: string;
  parameters: Record<string, any>;
  universe: string[];
  max_positions: number;
  position_size: number;
  stop_loss?: number;
  take_profit?: number;
  max_drawdown: number;
  start_date?: string;
  end_date?: string;
  benchmark: string;
}
```

### 回测配置

```typescript
interface BacktestConfig {
  initial_capital: number;
  start_date: string;
  end_date: string;
  benchmark: string;
  commission_rate: number;
  stamp_tax_rate: number;
  transfer_fee_rate: number;
  min_commission: number;
  slippage_type: 'percentage' | 'fixed' | 'volume_based' | 'market_impact';
  slippage_value: number;
  cash_interest_rate: number;
}
```

### 风险限制

```typescript
interface RiskLimits {
  max_position_size: number;
  max_total_position: number;
  max_sector_exposure: number;
  max_positions_count: number;
  max_daily_loss: number;
  max_drawdown: number;
  stop_loss_threshold: number;
  max_portfolio_volatility: number;
  max_tracking_error: number;
  min_avg_volume: number;
  max_volume_participation: number;
  var_confidence_level: number;
  max_var: number;
}
```

## 错误处理最佳实践

### 1. 统一错误处理

```typescript
import { useBacktest } from './integration/hooks/useBacktest';

function BacktestComponent() {
  const { error, clearError } = useBacktest();

  useEffect(() => {
    if (error) {
      // 显示错误通知
      showErrorNotification(error);
      
      // 自动清除错误（可选）
      setTimeout(clearError, 5000);
    }
  }, [error, clearError]);

  // ...
}
```

### 2. 网络错误重试

```typescript
const createBacktestWithRetry = async (request: BacktestRequest, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const taskId = await createBacktest(request);
      return taskId;
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
};
```

### 3. 加载状态处理

```typescript
function BacktestComponent() {
  const { loading, creating } = useBacktest();

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div>
      <button disabled={creating}>
        {creating ? '创建中...' : '创建回测'}
      </button>
      {/* 其他内容 */}
    </div>
  );
}
```

## 性能优化建议

### 1. 避免不必要的重新渲染

```typescript
import { useMemo, useCallback } from 'react';

const BacktestList = ({ tasks }) => {
  const sortedTasks = useMemo(() => {
    return tasks.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  }, [tasks]);

  const handleTaskClick = useCallback((task) => {
    // 处理点击
  }, []);

  return (
    // JSX
  );
};
```

### 2. 合理使用WebSocket

```typescript
// 根据页面需要有选择地订阅主题
const { wsManager } = useBacktest({
  enableWebSocket: isBacktestPageActive, // 只在回测页面启用
  clientId: `user-${userId}` // 使用唯一的客户端ID
});

useEffect(() => {
  if (wsManager && needRealTimeUpdates) {
    wsManager.subscribe(['backtest_progress']);
    
    return () => {
      wsManager.unsubscribe(['backtest_progress']);
    };
  }
}, [wsManager, needRealTimeUpdates]);
```

### 3. 数据缓存

```typescript
// 使用React Query或SWR进行数据缓存
import { useQuery } from 'react-query';

const useBacktestResults = (taskId: string) => {
  return useQuery(
    ['backtest-results', taskId],
    () => apiClient.getBacktestResults(taskId),
    {
      staleTime: 5 * 60 * 1000, // 5分钟内不重新获取
      cacheTime: 10 * 60 * 1000, // 缓存10分钟
    }
  );
};
```

## 部署配置

### 环境变量

```bash
# .env.development
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000

# .env.production
REACT_APP_API_URL=https://api.yourcompany.com
REACT_APP_WS_URL=wss://api.yourcompany.com
```

### Nginx配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket代理
    location /ws {
        proxy_pass http://backend:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 故障排除

### 常见问题

1. **CORS错误**
   - 确保后端正确配置CORS
   - 检查API URL是否正确

2. **WebSocket连接失败**
   - 检查WebSocket URL格式
   - 确认防火墙设置
   - 验证网络连接

3. **认证失败**
   - 检查token是否正确设置
   - 确认token未过期

### 调试技巧

```typescript
// 启用详细日志
const apiClient = new APIClient('http://localhost:8000');
apiClient.setDebugMode(true); // 假设有这个方法

// 监听WebSocket事件
const ws = apiClient.createWebSocket('debug-client');
ws.onMessage('*', (data) => {
  console.log('WebSocket message:', data);
});
```

## 示例项目

查看 `examples/` 目录中的完整示例项目，了解如何在实际项目中使用这些组件和API。

## 技术支持

如有问题，请联系技术支持团队或查看项目文档。