<template>
  <div class="notification-settings">
    <div class="settings-section">
      <h3 class="section-title">
        <el-icon><Bell /></el-icon>
        通知设置
      </h3>
      <p class="section-description">管理您的通知偏好设置</p>

      <el-form :model="form" label-width="150px">
        <!-- 邮件通知 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>邮件通知</span>
          </template>
          
          <el-form-item label="交易通知">
            <el-switch v-model="form.email.trade" />
            <span class="switch-label">交易成功时发送邮件</span>
          </el-form-item>

          <el-form-item label="价格预警">
            <el-switch v-model="form.email.priceAlert" />
            <span class="switch-label">价格触发预警时发送邮件</span>
          </el-form-item>

          <el-form-item label="系统通知">
            <el-switch v-model="form.email.system" />
            <span class="switch-label">系统维护等重要通知</span>
          </el-form-item>
        </el-card>

        <!-- 短信通知 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>短信通知</span>
          </template>
          
          <el-form-item label="登录验证">
            <el-switch v-model="form.sms.login" />
            <span class="switch-label">异地登录时发送短信验证</span>
          </el-form-item>

          <el-form-item label="大额交易">
            <el-switch v-model="form.sms.largeTrade" />
            <span class="switch-label">大额交易时发送短信确认</span>
          </el-form-item>
        </el-card>

        <!-- 推送通知 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>推送通知</span>
          </template>
          
          <el-form-item label="实时行情">
            <el-switch v-model="form.push.market" />
            <span class="switch-label">重要行情变动推送</span>
          </el-form-item>

          <el-form-item label="策略信号">
            <el-switch v-model="form.push.strategy" />
            <span class="switch-label">策略买卖信号推送</span>
          </el-form-item>
        </el-card>

        <el-form-item>
          <el-button type="primary" @click="saveSettings" :loading="saving">
            保存设置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Bell } from '@element-plus/icons-vue'

const saving = ref(false)

const form = reactive({
  email: {
    trade: true,
    priceAlert: true,
    system: true
  },
  sms: {
    login: true,
    largeTrade: true
  },
  push: {
    market: false,
    strategy: true
  }
})

const saveSettings = async () => {
  saving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('通知设置保存成功')
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.notification-settings {
  max-width: 800px;
}

.settings-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.section-description {
  color: #666;
  font-size: 14px;
  margin: 0 0 24px 0;
}

.form-section {
  margin-bottom: 24px;
  border: 1px solid #e6e6e6;
}

.switch-label {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}
</style>
