<template>
  <div class="trading-center">
    <div class="page-title">交易中心测试页面</div>
    <div class="nav-right">
      <button>测试按钮1</button>
      <button>测试按钮2</button>
      <button>测试按钮3</button>
    </div>
    <p>这是一个最简单的测试页面，用于验证路由是否正常工作。</p>
  </div>
</template>

<script setup lang="ts">
console.log('TradingCenterTest2 组件已加载')
</script>

<style scoped>
.trading-center {
  padding: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
}

.nav-right {
  margin-bottom: 20px;
}

button {
  margin-right: 10px;
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>
