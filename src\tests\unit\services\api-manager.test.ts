/**
 * API管理器服务单元测试
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ApiManagerService } from '@/services/api-manager.service'

// Mock配置
vi.mock('@/config', () => ({
  appConfig: {
    enableMock: false,
    isDev: false
  }
}))

// Mock HTTP客户端
vi.mock('@/api/http', () => ({
  httpClient: {
    get: vi.fn()
  }
}))

// Mock服务
vi.mock('@/services/mock.service', () => ({
  mockService: {
    getQuoteData: vi.fn()
  }
}))

describe('ApiManagerService', () => {
  let apiManager: ApiManagerService

  beforeEach(() => {
    // 重置所有mock
    vi.clearAllMocks()
    
    // 创建新的API管理器实例
    apiManager = new ApiManagerService()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('构造函数和初始化', () => {
    it('应该正确初始化API管理器', () => {
      expect(apiManager).toBeInstanceOf(ApiManagerService)
      expect(apiManager.isMockMode()).toBe(false)
    })

    it('应该在开发环境下启用Mock模式', () => {
      // 模拟开发环境
      vi.stubEnv('DEV', true)
      const devApiManager = new ApiManagerService()
      expect(devApiManager.isMockMode()).toBe(true)
    })
  })

  describe('模式切换', () => {
    it('应该能够切换到Mock模式', () => {
      apiManager.setMockMode(true)
      expect(apiManager.isMockMode()).toBe(true)
    })

    it('应该能够切换到API模式', () => {
      apiManager.setMockMode(false)
      expect(apiManager.isMockMode()).toBe(false)
    })
  })

  describe('API调用', () => {
    it('在Mock模式下应该调用Mock方法', async () => {
      apiManager.setMockMode(true)
      
      const mockMethod = vi.fn().mockResolvedValue({ data: 'mock' })
      const realApiCall = vi.fn().mockResolvedValue({ data: 'real' })

      const result = await apiManager.call(mockMethod, realApiCall)

      expect(mockMethod).toHaveBeenCalled()
      expect(realApiCall).not.toHaveBeenCalled()
      expect(result).toEqual({ data: 'mock' })
    })

    it('在API模式下应该调用真实API', async () => {
      apiManager.setMockMode(false)
      
      const mockMethod = vi.fn().mockResolvedValue({ data: 'mock' })
      const realApiCall = vi.fn().mockResolvedValue({ data: 'real' })

      const result = await apiManager.call(mockMethod, realApiCall)

      expect(realApiCall).toHaveBeenCalled()
      expect(mockMethod).not.toHaveBeenCalled()
      expect(result).toEqual({ data: 'real' })
    })

    it('API失败时应该回退到Mock数据', async () => {
      apiManager.setMockMode(false)
      
      const mockMethod = vi.fn().mockResolvedValue({ data: 'mock' })
      const realApiCall = vi.fn().mockRejectedValue(new Error('API Error'))

      const result = await apiManager.call(mockMethod, realApiCall, true)

      expect(realApiCall).toHaveBeenCalled()
      expect(mockMethod).toHaveBeenCalled()
      expect(result).toEqual({ data: 'mock' })
    })

    it('API失败且不允许回退时应该抛出错误', async () => {
      apiManager.setMockMode(false)
      
      const mockMethod = vi.fn().mockResolvedValue({ data: 'mock' })
      const realApiCall = vi.fn().mockRejectedValue(new Error('API Error'))

      await expect(
        apiManager.call(mockMethod, realApiCall, false)
      ).rejects.toThrow('API Error')

      expect(realApiCall).toHaveBeenCalled()
      expect(mockMethod).not.toHaveBeenCalled()
    })
  })

  describe('重试机制', () => {
    it('应该在失败时重试指定次数', async () => {
      apiManager.setMockMode(false)
      
      const mockMethod = vi.fn().mockResolvedValue({ data: 'mock' })
      const realApiCall = vi.fn()
        .mockRejectedValueOnce(new Error('First failure'))
        .mockRejectedValueOnce(new Error('Second failure'))
        .mockResolvedValue({ data: 'success' })

      const result = await apiManager.callWithRetry(mockMethod, realApiCall, 3, 10)

      expect(realApiCall).toHaveBeenCalledTimes(3)
      expect(result).toEqual({ data: 'success' })
    })

    it('所有重试失败后应该使用Mock数据', async () => {
      apiManager.setMockMode(false)
      
      const mockMethod = vi.fn().mockResolvedValue({ data: 'mock' })
      const realApiCall = vi.fn().mockRejectedValue(new Error('API Error'))

      const result = await apiManager.callWithRetry(mockMethod, realApiCall, 2, 10)

      expect(realApiCall).toHaveBeenCalledTimes(2)
      expect(mockMethod).toHaveBeenCalled()
      expect(result).toEqual({ data: 'mock' })
    })
  })

  describe('批量调用', () => {
    it('应该能够批量处理多个API调用', async () => {
      apiManager.setMockMode(false)
      
      const calls = [
        {
          key: 'call1',
          mockMethod: vi.fn().mockResolvedValue({ data: 'mock1' }),
          realApiCall: vi.fn().mockResolvedValue({ data: 'real1' })
        },
        {
          key: 'call2',
          mockMethod: vi.fn().mockResolvedValue({ data: 'mock2' }),
          realApiCall: vi.fn().mockRejectedValue(new Error('API Error'))
        }
      ]

      const results = await apiManager.batchCall(calls)

      expect(results.call1).toEqual({ data: 'real1' })
      expect(results.call2).toBeInstanceOf(Error)
    })
  })

  describe('状态获取', () => {
    it('应该返回正确的状态信息', () => {
      const status = apiManager.getStatus()
      
      expect(status).toHaveProperty('mode')
      expect(status).toHaveProperty('config')
      expect(status.config).toHaveProperty('enableMock')
      expect(status.config).toHaveProperty('isDev')
      expect(status.config).toHaveProperty('apiBaseUrl')
    })
  })

  describe('单例模式', () => {
    it('应该返回相同的实例', () => {
      const instance1 = ApiManagerService.getInstance()
      const instance2 = ApiManagerService.getInstance()
      
      expect(instance1).toBe(instance2)
    })
  })
})
