<template>
  <div class="historical-data-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>历史行情数据</h1>
      <p>提供A股、港股、美股等历史行情数据查询和分析工具</p>
    </div>

    <!-- 快捷入口 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :span="6">
        <el-card class="quick-card" shadow="hover" @click="quickSelect('热门股票')">
          <div class="quick-content">
            <el-icon class="quick-icon"><Star /></el-icon>
            <span>热门股票</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="quick-card" shadow="hover" @click="quickSelect('银行股')">
          <div class="quick-content">
            <el-icon class="quick-icon"><Coin /></el-icon>
            <span>银行股</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="quick-card" shadow="hover" @click="quickSelect('科技股')">
          <div class="quick-content">
            <el-icon class="quick-icon"><Monitor /></el-icon>
            <span>科技股</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="quick-card" shadow="hover" @click="quickSelect('白酒股')">
          <div class="quick-content">
            <el-icon class="quick-icon"><Bowl /></el-icon>
            <span>白酒股</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 统计信息卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(stats.total_stocks || 0) }}</div>
            <div class="stat-label">可查股票总数</div>
          </div>
          <el-icon class="stat-icon"><TrendCharts /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ Object.keys(stats.markets || {}).length }}</div>
            <div class="stat-label">覆盖交易所</div>
          </div>
          <el-icon class="stat-icon"><Location /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ Object.keys(stats.industries || {}).length }}</div>
            <div class="stat-label">涵盖行业</div>
          </div>
          <el-icon class="stat-icon"><Grid /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.data_range ? calculateYears(stats.data_range) : 0 }}年</div>
            <div class="stat-label">历史数据跨度</div>
          </div>
          <el-icon class="stat-icon"><Calendar /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 - 更符合中国习惯的布局 -->
    <el-card class="search-card">
      <div class="search-section">
        <!-- 主搜索区域 -->
        <div class="main-search">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入股票代码（如：000001）或名称（如：平安银行）"
            @keyup.enter="handleSearch"
            clearable
            size="large"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button type="primary" @click="handleSearch" :loading="loading">
                搜索
              </el-button>
            </template>
          </el-input>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <label>交易所：</label>
              <el-radio-group v-model="selectedMarket" @change="handleFilterChange">
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button label="SH">上交所</el-radio-button>
                <el-radio-button label="SZ">深交所</el-radio-button>
                <el-radio-button label="BJ">北交所</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          
          <div class="filter-row">
            <div class="filter-item">
              <label>热门行业：</label>
              <el-radio-group v-model="selectedIndustry" @change="handleFilterChange">
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button label="银行">银行</el-radio-button>
                <el-radio-button label="医药生物">医药</el-radio-button>
                <el-radio-button label="食品饮料">食品</el-radio-button>
                <el-radio-button label="电子">电子</el-radio-button>
                <el-radio-button label="计算机">软件</el-radio-button>
                <el-radio-button label="房地产">地产</el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <div class="filter-actions">
            <el-button @click="handleReset" icon="RefreshLeft">重置条件</el-button>
            <el-button @click="handleAdvancedFilter" icon="Filter">高级筛选</el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 股票列表 - 优化表格布局 -->
    <el-card class="stock-list-card">
      <template #header>
        <div class="card-header">
          <span>股票列表</span>
          <div class="header-actions">
            <el-tag type="info">共找到 {{ pagination.total }} 只股票</el-tag>
            <el-dropdown @command="handleExportCommand">
              <el-button type="primary" size="small">
                导出数据<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="current">导出当前页</el-dropdown-item>
                  <el-dropdown-item command="all">导出全部</el-dropdown-item>
                  <el-dropdown-item command="selected">导出已选</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>

      <el-table
        :data="stockList"
        v-loading="loading"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        :row-class-name="getRowClassName"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="symbol" label="代码" width="100" fixed="left">
          <template #default="{ row }">
            <span class="stock-symbol">{{ row.symbol }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" width="120" fixed="left" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="stock-name">{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="market" label="交易所" width="80" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getMarketTagType(row.market)"
              size="small"
            >
              {{ getMarketName(row.market) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="industry" label="所属行业" width="120" show-overflow-tooltip />
        <el-table-column prop="last_price" label="最新价" width="100" align="right">
          <template #default="{ row }">
            <span v-if="row.last_price" class="price">{{ formatPrice(row.last_price) }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="change_percent" label="涨跌幅" width="100" align="right">
          <template #default="{ row }">
            <span v-if="row.change_percent" :class="getPriceChangeClass(row.change_percent)">
              {{ formatPercentage(row.change_percent) }}
            </span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_records" label="数据量" width="100" align="right">
          <template #default="{ row }">
            <span class="data-count">{{ formatNumber(row.total_records) }}条</span>
          </template>
        </el-table-column>
        <el-table-column prop="start_date" label="起始日期" width="110" align="center" />
        <el-table-column prop="end_date" label="截止日期" width="110" align="center" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click.stop="viewStockData(row)"
              icon="View"
            >
              查看
            </el-button>
            <el-button
              type="success"
              size="small"
              @click.stop="viewChart(row)"
              icon="TrendCharts"
            >
              K线图
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 - 中国习惯的布局 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span>显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} - {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，共 {{ pagination.total }} 条记录</span>
        </div>
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="pagination.total"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 股票数据详情对话框 - 优化布局 -->
    <el-dialog
      v-model="dataDialogVisible"
      :title="`${selectedStock?.name} (${selectedStock?.symbol}) - 历史行情数据`"
      width="90%"
      top="5vh"
      class="data-dialog"
    >
      <div class="data-dialog-content">
        <!-- 工具栏 -->
        <div class="data-toolbar">
          <div class="date-controls">
            <span class="label">查询时间：</span>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="loadStockData"
              size="small"
            />
            <el-button type="primary" @click="loadStockData" :loading="loadingData" size="small" icon="Search">
              查询
            </el-button>
          </div>
          
          <div class="data-actions">
            <el-button type="success" @click="exportStockData" :disabled="stockData.length === 0" size="small" icon="Download">
              导出Excel
            </el-button>
            <el-button @click="viewFullChart" size="small" icon="FullScreen">
              全屏图表
            </el-button>
          </div>
        </div>

        <!-- 数据概览 -->
        <div class="data-summary" v-if="stockData.length > 0">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-label">数据条数</div>
                <div class="summary-value">{{ stockData.length }}条</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-label">期间最高</div>
                <div class="summary-value price-up">{{ getMaxPrice() }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-label">期间最低</div>
                <div class="summary-value price-down">{{ getMinPrice() }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-label">累计涨跌</div>
                <div class="summary-value" :class="getTotalChangeClass()">{{ getTotalChange() }}</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 数据表格 - 中国股市习惯的列顺序 -->
        <el-table
          :data="stockData"
          v-loading="loadingData"
          max-height="400"
          style="width: 100%"
          size="small"
        >
          <el-table-column prop="日期" label="交易日期" width="100" fixed="left" />
          <el-table-column prop="开盘价" label="开盘" width="80" align="right">
            <template #default="{ row }">
              <span class="price">{{ formatPrice(row['开盘价']) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="收盘价" label="收盘" width="80" align="right">
            <template #default="{ row }">
              <span class="price">{{ formatPrice(row['收盘价']) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="最高价" label="最高" width="80" align="right">
            <template #default="{ row }">
              <span class="price price-up">{{ formatPrice(row['最高价']) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="最低价" label="最低" width="80" align="right">
            <template #default="{ row }">
              <span class="price price-down">{{ formatPrice(row['最低价']) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="涨跌幅(%)" label="涨跌幅" width="90" align="right">
            <template #default="{ row }">
              <span :class="getPriceChangeClass(row['涨跌幅(%)'])">
                {{ formatPercentage(row['涨跌幅(%)']) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="成交量(手)" label="成交量(手)" width="120" align="right">
            <template #default="{ row }">
              <span class="volume">{{ formatVolume(row['成交量(手)']) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="成交额(元)" label="成交额" width="120" align="right">
            <template #default="{ row }">
              <span class="amount">{{ formatMoney(row['成交额(元)']) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 高级筛选对话框 -->
    <el-dialog v-model="advancedFilterVisible" title="高级筛选" width="600px">
      <el-form :model="advancedFilter" label-width="100px">
        <el-form-item label="市值范围">
          <el-slider
            v-model="advancedFilter.marketCapRange"
            range
            :min="0"
            :max="10000"
            :step="100"
            show-stops
          />
        </el-form-item>
        <el-form-item label="价格区间">
          <el-input-number v-model="advancedFilter.minPrice" :min="0" placeholder="最低价" />
          <span style="margin: 0 10px;">-</span>
          <el-input-number v-model="advancedFilter.maxPrice" :min="0" placeholder="最高价" />
        </el-form-item>
        <el-form-item label="数据完整度">
          <el-radio-group v-model="advancedFilter.dataQuality">
            <el-radio label="all">全部</el-radio>
            <el-radio label="complete">数据完整</el-radio>
            <el-radio label="recent">近期活跃</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="advancedFilterVisible = false">取消</el-button>
        <el-button type="primary" @click="applyAdvancedFilter">应用筛选</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  TrendCharts, Location, Grid, Calendar, Search, RefreshLeft, Filter, Download,
  Star, Coin, Monitor, Bowl, View, FullScreen, ArrowDown
} from '@element-plus/icons-vue'
import { httpClient } from '@/api/http'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const loadingData = ref(false)
const dataDialogVisible = ref(false)
const advancedFilterVisible = ref(false)

const searchKeyword = ref('')
const selectedMarket = ref('')
const selectedIndustry = ref('')
const selectedStocks = ref<any[]>([])

const stats = ref<any>({})
const stockList = ref<any[]>([])
const industryList = ref<string[]>([])
const selectedStock = ref<any>(null)
const stockData = ref<any[]>([])
const dateRange = ref<string[]>([])

const pagination = reactive({
  page: 1,
  pageSize: 50,
  total: 0
})

const advancedFilter = reactive({
  marketCapRange: [0, 10000],
  minPrice: null,
  maxPrice: null,
  dataQuality: 'all'
})

// 计算属性
const calculateYears = (range: any) => {
  if (!range || !range.start_date || !range.end_date) return 0
  const start = new Date(range.start_date)
  const end = new Date(range.end_date)
  return Math.ceil((end.getTime() - start.getTime()) / (365 * 24 * 60 * 60 * 1000))
}

// 方法
const quickSelect = (type: string) => {
  switch (type) {
    case '热门股票':
      selectedIndustry.value = ''
      searchKeyword.value = '茅台 平安 招行 腾讯'
      break
    case '银行股':
      selectedIndustry.value = '银行'
      break
    case '科技股':
      selectedIndustry.value = '计算机'
      break
    case '白酒股':
      selectedIndustry.value = '食品饮料'
      searchKeyword.value = '茅台 五粮液 剑南春'
      break
  }
  handleSearch()
}

const handleFilterChange = () => {
  pagination.page = 1
  loadStockList()
}

const handleAdvancedFilter = () => {
  advancedFilterVisible.value = true
}

const applyAdvancedFilter = () => {
  advancedFilterVisible.value = false
  loadStockList()
}

const handleSelectionChange = (selection: any[]) => {
  selectedStocks.value = selection
}

const handleExportCommand = (command: string) => {
  switch (command) {
    case 'current':
      exportCurrentPage()
      break
    case 'all':
      exportAllData()
      break
    case 'selected':
      exportSelectedData()
      break
  }
}

const exportCurrentPage = () => {
  if (stockList.value.length === 0) {
    ElMessage.warning('当前页无数据可导出')
    return
  }
  
  const csvData = stockList.value.map(stock => [
    stock.symbol,
    stock.name,
    stock.market,
    stock.industry,
    stock.last_price || '-',
    stock.change_percent ? `${stock.change_percent}%` : '-',
    stock.total_records || 0,
    stock.start_date || '-',
    stock.end_date || '-'
  ])
  
  downloadCSV(csvData, '股票列表_当前页')
}

const exportAllData = () => {
  ElMessage.info('正在导出全部数据，请稍候...')
  // 这里应该调用API获取全部数据
}

const exportSelectedData = () => {
  if (selectedStocks.value.length === 0) {
    ElMessage.warning('请先选择要导出的股票')
    return
  }
  
  const csvData = selectedStocks.value.map(stock => [
    stock.symbol,
    stock.name,
    stock.market,
    stock.industry,
    stock.last_price || '-',
    stock.change_percent ? `${stock.change_percent}%` : '-',
    stock.total_records || 0,
    stock.start_date || '-',
    stock.end_date || '-'
  ])
  
  downloadCSV(csvData, '股票列表_已选择')
}

const downloadCSV = (data: any[][], filename: string) => {
  const headers = ['股票代码', '股票名称', '交易所', '行业', '最新价', '涨跌幅', '数据量', '起始日期', '截止日期']
  const csvContent = [
    headers.join(','),
    ...data.map(row => row.join(','))
  ].join('\n')
  
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8' })
  
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${filename}_${new Date().toLocaleDateString().replace(/\//g, '-')}.csv`
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
  
  ElMessage.success('数据导出成功')
}

const viewFullChart = () => {
  if (!selectedStock.value) return
  
  // 在新窗口打开全屏图表
  const url = `/market/${selectedStock.value.symbol}?fullscreen=true`
  window.open(url, '_blank')
}

// 数据统计方法
const getMaxPrice = () => {
  if (stockData.value.length === 0) return '-'
  const maxPrice = Math.max(...stockData.value.map(item => parseFloat(item['最高价'] || 0)))
  return formatPrice(maxPrice)
}

const getMinPrice = () => {
  if (stockData.value.length === 0) return '-'
  const minPrice = Math.min(...stockData.value.map(item => parseFloat(item['最低价'] || Infinity)))
  return formatPrice(minPrice)
}

const getTotalChange = () => {
  if (stockData.value.length < 2) return '-'
  const firstPrice = parseFloat(stockData.value[stockData.value.length - 1]['开盘价'] || 0)
  const lastPrice = parseFloat(stockData.value[0]['收盘价'] || 0)
  if (firstPrice === 0) return '-'
  
  const change = ((lastPrice - firstPrice) / firstPrice * 100)
  return formatPercentage(change)
}

const getTotalChangeClass = () => {
  if (stockData.value.length < 2) return 'price-neutral'
  const firstPrice = parseFloat(stockData.value[stockData.value.length - 1]['开盘价'] || 0)
  const lastPrice = parseFloat(stockData.value[0]['收盘价'] || 0)
  const change = lastPrice - firstPrice
  
  if (change > 0) return 'price-up'
  if (change < 0) return 'price-down'
  return 'price-neutral'
}

// 格式化方法优化
const getMarketName = (market: string) => {
  const marketNames: Record<string, string> = {
    'SH': '上交所',
    'SZ': '深交所', 
    'BJ': '北交所'
  }
  return marketNames[market] || market
}

const getRowClassName = ({ row }: { row: any }) => {
  if (row.change_percent > 0) return 'row-up'
  if (row.change_percent < 0) return 'row-down'
  return ''
}

const formatPrice = (price: number | string) => {
  const num = parseFloat(price as string)
  if (isNaN(num)) return '-'
  return num.toFixed(2)
}

const formatPercentage = (percent: number | string) => {
  const num = parseFloat(percent as string)
  if (isNaN(num)) return '-'
  const sign = num > 0 ? '+' : ''
  return `${sign}${num.toFixed(2)}%`
}

const formatVolume = (volume: number | string) => {
  const num = parseFloat(volume as string)
  if (isNaN(num)) return '-'
  
  if (num >= 100000000) {
    return (num / 100000000).toFixed(2) + '亿手'
  } else if (num >= 10000) {
    return (num / 10000).toFixed(2) + '万手'
  }
  return num.toLocaleString() + '手'
}

// 原有方法保持不变...
const loadStats = async () => {
  console.log('🚀 开始加载历史数据统计...')

  // 使用模拟统计数据
  stats.value = {
    total_stocks: 4892,
    markets: {
      'SH': 1876,
      'SZ': 2654,
      'BJ': 362
    },
    industries: {
      '银行': 42,
      '房地产': 156,
      '食品饮料': 89,
      '医药生物': 234,
      '电子': 298,
      '计算机': 187,
      '机械设备': 245,
      '化工': 312,
      '汽车': 134,
      '电力设备': 167,
      '有色金属': 89,
      '建筑材料': 76,
      '钢铁': 45,
      '煤炭': 23,
      '石油石化': 34,
      '通信': 67,
      '传媒': 89,
      '军工': 56,
      '农林牧渔': 43,
      '纺织服装': 67
    },
    data_range: {
      start_date: '2020-01-01',
      end_date: '2024-12-31'
    },
    last_updated: new Date().toISOString()
  }

  // 提取行业列表
  industryList.value = Object.keys(stats.value.industries)
  console.log('📊 使用模拟统计数据:', stats.value)
}

const loadStockList = async () => {
  loading.value = true
  console.log('🚀 开始加载历史股票列表...')

  // 使用模拟股票列表数据
  const mockStocks = [
    {
      symbol: '000001',
      name: '平安银行',
      market: 'SZ',
      industry: '银行',
      has_historical_data: true,
      total_records: 1245,
      start_date: '2020-01-01',
      end_date: '2024-12-31',
      file_size: '2.3MB',
      last_price: 13.80,
      change: 0.25,
      change_percent: 1.84
    },
    {
      symbol: '000002',
      name: '万科A',
      market: 'SZ',
      industry: '房地产',
      has_historical_data: true,
      total_records: 1245,
      start_date: '2020-01-01',
      end_date: '2024-12-31',
      file_size: '2.1MB',
      last_price: 19.50,
      change: -0.30,
      change_percent: -1.52
    },
    {
      symbol: '600036',
      name: '招商银行',
      market: 'SH',
      industry: '银行',
      has_historical_data: true,
      total_records: 1245,
      start_date: '2020-01-01',
      end_date: '2024-12-31',
      file_size: '2.5MB',
      last_price: 38.20,
      change: 1.20,
      change_percent: 3.24
    },
    {
      symbol: '600519',
      name: '贵州茅台',
      market: 'SH',
      industry: '食品饮料',
      has_historical_data: true,
      total_records: 1245,
      start_date: '2020-01-01',
      end_date: '2024-12-31',
      file_size: '2.8MB',
      last_price: 1680.50,
      change: -15.30,
      change_percent: -0.90
    },
    {
      symbol: '000858',
      name: '五粮液',
      market: 'SZ',
      industry: '食品饮料',
      has_historical_data: true,
      total_records: 1245,
      start_date: '2020-01-01',
      end_date: '2024-12-31',
      file_size: '2.4MB',
      last_price: 145.60,
      change: 2.80,
      change_percent: 1.96
    },
    {
      symbol: '300059',
      name: '东方财富',
      market: 'SZ',
      industry: '计算机',
      has_historical_data: true,
      total_records: 1245,
      start_date: '2020-01-01',
      end_date: '2024-12-31',
      file_size: '2.2MB',
      last_price: 15.20,
      change: 0.45,
      change_percent: 3.05
    },
    {
      symbol: '002415',
      name: '海康威视',
      market: 'SZ',
      industry: '电子',
      has_historical_data: true,
      total_records: 1245,
      start_date: '2020-01-01',
      end_date: '2024-12-31',
      file_size: '2.6MB',
      last_price: 32.80,
      change: -0.60,
      change_percent: -1.80
    },
    {
      symbol: '600276',
      name: '恒瑞医药',
      market: 'SH',
      industry: '医药生物',
      has_historical_data: true,
      total_records: 1245,
      start_date: '2020-01-01',
      end_date: '2024-12-31',
      file_size: '2.7MB',
      last_price: 56.70,
      change: 1.50,
      change_percent: 2.72
    }
  ]

  // 应用筛选
  let filteredStocks = mockStocks
  if (selectedMarket.value) {
    filteredStocks = filteredStocks.filter(stock => stock.market === selectedMarket.value)
  }
  if (selectedIndustry.value) {
    filteredStocks = filteredStocks.filter(stock => stock.industry === selectedIndustry.value)
  }

  stockList.value = filteredStocks
  pagination.total = filteredStocks.length
  console.log('📊 使用模拟股票列表:', stockList.value.length, '只股票')
  loading.value = false
}

const handleSearch = async () => {
  pagination.page = 1
  await loadStockList()
}

const handleReset = () => {
  searchKeyword.value = ''
  selectedMarket.value = ''
  selectedIndustry.value = ''
  pagination.page = 1
  loadStockList()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadStockList()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadStockList()
}

const handleRowClick = (row: any) => {
  selectedStock.value = row
  dataDialogVisible.value = true
  loadStockData()
}

const viewStockData = (row: any) => {
  selectedStock.value = row
  dataDialogVisible.value = true
  loadStockData()
}

const viewChart = (row: any) => {
  router.push(`/market/${row.symbol}`)
}

const exportStockData = () => {
  if (!stockData.value || stockData.value.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }

  try {
    const headers = ['交易日期', '开盘', '收盘', '最高', '最低', '涨跌幅', '成交量', '成交额']
    const rows = stockData.value.map(item => [
      item['日期'],
      item['开盘价'],
      item['收盘价'],
      item['最高价'],
      item['最低价'],
      `${item['涨跌幅(%)']}%`,
      item['成交量(手)'],
      item['成交额(元)']
    ])

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n')

    const BOM = '\uFEFF'
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8' })

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${selectedStock.value.name}_${selectedStock.value.symbol}_历史数据_${new Date().toLocaleDateString().replace(/\//g, '-')}.csv`
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('数据导出失败')
  }
}

const loadStockData = async () => {
  if (!selectedStock.value) return

  loadingData.value = true
  
  // 使用模拟数据
  const mockData = Array.from({ length: 20 }, (_, i) => {
    const date = new Date()
    date.setDate(date.getDate() - i)
    const basePrice = selectedStock.value.last_price || 100
    const randomChange = (Math.random() - 0.5) * 0.1
    const price = basePrice * (1 + randomChange)
    
    return {
      '日期': date.toISOString().split('T')[0],
      '开盘价': (price * (1 + (Math.random() - 0.5) * 0.02)).toFixed(2),
      '收盘价': price.toFixed(2),
      '最高价': (price * (1 + Math.random() * 0.03)).toFixed(2),
      '最低价': (price * (1 - Math.random() * 0.03)).toFixed(2),
      '涨跌幅(%)': (randomChange * 100).toFixed(2),
      '成交量(手)': Math.floor(Math.random() * 1000000),
      '成交额(元)': Math.floor(Math.random() * 100000000)
    }
  })
  
  stockData.value = mockData
  loadingData.value = false
}

// 工具函数
const getMarketTagType = (market: string) => {
  switch (market) {
    case 'SH': return 'danger'
    case 'SZ': return 'success'
    case 'BJ': return 'warning'
    default: return 'info'
  }
}

const formatNumber = (num: number) => {
  if (!num) return '0'
  return num.toLocaleString()
}

const formatMoney = (amount: number | string) => {
  const num = parseFloat(amount as string)
  if (!num || isNaN(num)) return '0'
  
  if (num >= 100000000) {
    return (num / 100000000).toFixed(2) + '亿'
  } else if (num >= 10000) {
    return (num / 10000).toFixed(2) + '万'
  }
  return num.toLocaleString()
}

const getPriceChangeClass = (change: number | string) => {
  const num = parseFloat(change as string)
  if (isNaN(num)) return 'price-neutral'
  if (num > 0) return 'price-up'
  if (num < 0) return 'price-down'
  return 'price-neutral'
}

// 生命周期
onMounted(() => {
  loadStats()
  loadStockList()
})
</script>

<style scoped>
.historical-data-page {
  padding: 20px;
  background-color: #f5f7fa;
}

.page-header {
  margin-bottom: 20px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 16px;
}

/* 快捷入口 */
.quick-actions {
  margin-bottom: 20px;
}

.quick-card {
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #e4e7ed;
}

.quick-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2);
}

.quick-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.quick-icon {
  font-size: 24px;
  color: #409eff;
  margin-bottom: 8px;
}

/* 统计卡片优化 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  position: relative;
  z-index: 2;
  padding: 20px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 4px;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  line-height: 1;
}

.stat-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  color: #f0f0f0;
  z-index: 1;
}

/* 搜索区域优化 */
.search-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.search-section {
  padding: 20px;
}

.main-search {
  margin-bottom: 20px;
}

.search-input {
  font-size: 16px;
}

.filter-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-item label {
  font-weight: 500;
  margin-right: 10px;
  color: #606266;
  white-space: nowrap;
}

.filter-actions {
  text-align: right;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

/* 表格优化 */
.stock-list-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 表格行样式 */
:deep(.row-up) {
  background-color: #fef0f0;
}

:deep(.row-down) {
  background-color: #f0f9ff;
}

.stock-symbol {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #409eff;
}

.stock-name {
  font-weight: 500;
}

.price {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

.price-up {
  color: #F56C6C;
}

.price-down {
  color: #67C23A;
}

.price-neutral {
  color: #909399;
}

.no-data {
  color: #c0c4cc;
}

.data-count {
  color: #909399;
  font-size: 12px;
}

.volume {
  font-size: 12px;
  color: #909399;
}

.amount {
  font-size: 12px;
  color: #909399;
}

/* 分页优化 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
}

.pagination-info {
  color: #909399;
  font-size: 14px;
}

/* 对话框优化 */
.data-dialog {
  border-radius: 8px;
}

.data-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.data-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.date-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-controls .label {
  font-weight: 500;
  color: #606266;
}

.data-actions {
  display: flex;
  gap: 10px;
}

.data-summary {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.summary-item {
  text-align: center;
}

.summary-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.summary-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .historical-data-page {
    padding: 10px;
  }
  
  .quick-actions .el-col {
    margin-bottom: 10px;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-item {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .data-toolbar {
    flex-direction: column;
    gap: 15px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 5px;
  }
}
</style>