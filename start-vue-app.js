#!/usr/bin/env node

/**
 * 启动Vue应用的简化脚本
 * 解决Vite开发服务器启动问题
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 启动量化投资平台Vue应用...');

// 设置环境变量
process.env.NODE_ENV = 'development';
process.env.VITE_API_BASE_URL = 'http://localhost:8000';
process.env.VITE_WS_BASE_URL = 'ws://localhost:8000';

// 启动Vite开发服务器
const viteProcess = spawn('npx', [
  'vite',
  '--port', '5173',
  '--host', '0.0.0.0',
  '--mode', 'development',
  '--force'
], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true
});

viteProcess.on('error', (error) => {
  console.error('❌ 启动失败:', error);
  process.exit(1);
});

viteProcess.on('close', (code) => {
  console.log(`🔚 Vue应用已停止 (退出码: ${code})`);
  process.exit(code);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭Vue应用...');
  viteProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭Vue应用...');
  viteProcess.kill('SIGTERM');
});

console.log('✅ Vue应用启动脚本已运行');
console.log('📍 访问地址: http://localhost:5173');
console.log('🔧 API地址: http://localhost:8000');
